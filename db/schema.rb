# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_07_18_055052) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_stat_statements"
  enable_extension "plpgsql"

  create_table "ai_assist_chat_messages", force: :cascade do |t|
    t.string "prompt"
    t.text "response"
    t.integer "score"
    t.text "comment"
    t.string "status", default: "unresolved", null: false
    t.bigint "user_id"
    t.string "messageable_type"
    t.bigint "messageable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["messageable_type", "messageable_id"], name: "index_ai_chat_messages_on_messageable"
    t.index ["user_id"], name: "index_ai_assist_chat_messages_on_user_id"
  end

  create_table "ai_assist_exercise_attempts", force: :cascade do |t|
    t.integer "ai_exercise_id"
    t.integer "user_id"
    t.string "answer", limit: 255
    t.integer "number_attempts"
    t.boolean "correct"
    t.datetime "created_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.integer "failure_reason_id"
    t.index ["ai_exercise_id", "user_id"], name: "index_ai_assist_exercise_attempts_on_ai_exercise_id_and_user_id", unique: true
    t.index ["failure_reason_id"], name: "index_ai_assist_exercise_attempts_on_failure_reason_id"
  end

  create_table "ai_assist_exercises", id: :serial, force: :cascade do |t|
    t.text "content"
    t.text "solution"
    t.text "options"
    t.string "correct_option_index", limit: 255
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "number"
    t.string "type", default: "AiAssist::Exercise", null: false
    t.integer "chapter_id"
    t.string "level", limit: 255
    t.string "question_type", limit: 255, default: "problem_solving"
    t.integer "number_of_options"
    t.integer "avg_time_correct"
    t.integer "avg_time_incorrect"
    t.integer "times_attempted", default: 0
    t.datetime "last_attempted_at", precision: nil
    t.integer "percentage_correct", default: 0
    t.integer "lesson_id"
    t.integer "exercise_id"
    t.integer "user_id"
  end

  create_table "ai_assist_summaries", force: :cascade do |t|
    t.string "summary_type"
    t.text "content"
    t.bigint "lesson_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lesson_id"], name: "index_ai_assist_summaries_on_lesson_id"
    t.index ["user_id"], name: "index_ai_assist_summaries_on_user_id"
  end

  create_table "announcements", id: :serial, force: :cascade do |t|
    t.text "content", null: false
    t.string "filter"
    t.integer "sender_id"
    t.boolean "messages_sent", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "sticky", default: false, null: false
    t.index ["sender_id"], name: "index_announcements_on_sender_id"
  end

  create_table "api_keys", id: :serial, force: :cascade do |t|
    t.string "key", null: false
    t.string "client_name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["client_name"], name: "index_api_keys_on_client_name", unique: true
    t.index ["key"], name: "index_api_keys_on_key", unique: true
  end

  create_table "audit_comments", id: :serial, force: :cascade do |t|
    t.text "body"
    t.integer "lesson_id"
    t.integer "user_id"
    t.boolean "resolved", default: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["lesson_id"], name: "index_audit_comments_on_lesson_id"
    t.index ["user_id"], name: "index_audit_comments_on_user_id"
  end

  create_table "authy_accounts", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.string "authy_id"
    t.datetime "last_sign_in_with_authy", precision: nil
    t.boolean "authy_enabled", default: false
    t.index ["authy_id"], name: "index_authy_accounts_on_authy_id"
  end

  create_table "background_tasks", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.integer "evaluation_attempt_id"
    t.string "status", default: "in_progress", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["evaluation_attempt_id"], name: "index_background_tasks_on_evaluation_attempt_id"
    t.index ["name"], name: "index_background_tasks_on_name"
  end

  create_table "billing_infos", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.string "card_type", limit: 255
    t.string "last_four", limit: 255
    t.string "ip_address", limit: 255
    t.string "country", limit: 255
    t.string "address1", limit: 255
    t.string "city", limit: 255
    t.string "state", limit: 255
    t.string "zip", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "first_six", limit: 255
    t.integer "month"
    t.integer "year"
    t.string "paypal_billing_agreement_id"
    t.string "payment_method"
    t.index ["user_id"], name: "index_billing_infos_on_user_id"
  end

  create_table "cancel_reasons", id: :serial, force: :cascade do |t|
    t.string "reason"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "ckeditor_assets", id: :serial, force: :cascade do |t|
    t.string "data_file_name", null: false
    t.string "data_content_type"
    t.integer "data_file_size"
    t.integer "assetable_id"
    t.string "assetable_type", limit: 30
    t.string "type", limit: 30
    t.integer "width"
    t.integer "height"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["assetable_type", "assetable_id"], name: "idx_ckeditor_assetable"
    t.index ["assetable_type", "type", "assetable_id"], name: "idx_ckeditor_assetable_type"
  end

  create_table "concept_masteries", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "content_assets", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.integer "assetable_id", null: false
    t.string "assetable_type", limit: 255, null: false
    t.string "asset", limit: 255, null: false
    t.index ["assetable_id", "assetable_type"], name: "index_content_assets_on_assetable_id_and_assetable_type"
  end

  create_table "credit_card_details", force: :cascade do |t|
    t.string "first_six", null: false
    t.string "last_four", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_credit_card_details_on_user_id"
  end

  create_table "daily_question_contacts", id: :serial, force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "email"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "daily_questions", id: :serial, force: :cascade do |t|
    t.integer "exercise_id", null: false
    t.date "date", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["exercise_id"], name: "index_daily_questions_on_exercise_id"
  end

  create_table "data_migrations", id: false, force: :cascade do |t|
    t.string "version", null: false
    t.index ["version"], name: "unique_data_migrations", unique: true
  end

  create_table "delayed_jobs", id: :serial, force: :cascade do |t|
    t.integer "priority", default: 0, null: false
    t.integer "attempts", default: 0, null: false
    t.text "handler", null: false
    t.text "last_error"
    t.datetime "run_at", precision: nil
    t.datetime "locked_at", precision: nil
    t.datetime "failed_at", precision: nil
    t.string "locked_by", limit: 255
    t.string "queue", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["priority", "run_at"], name: "delayed_jobs_priority"
  end

  create_table "evaluation_attempts", id: :serial, force: :cascade do |t|
    t.datetime "start_time", precision: nil
    t.datetime "end_time", precision: nil
    t.integer "user_id"
    t.integer "evaluation_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.datetime "last_access_at", precision: nil
    t.boolean "paused", default: false
    t.integer "total_seconds_paused"
    t.integer "total_elapsed_seconds"
    t.integer "percentage_correct", default: 0
    t.integer "time_per_question"
    t.text "remaining_answer_edits"
    t.integer "answered_problem_attempts_count", default: 0
    t.jsonb "adaptive_data", default: {}
    t.index ["evaluation_id"], name: "index_evaluation_attempts_on_evaluation_id"
    t.index ["user_id"], name: "index_evaluation_attempts_on_user_id"
  end

  create_table "evaluations", id: :serial, force: :cascade do |t|
    t.string "level", limit: 255
    t.boolean "archived", default: false
    t.integer "chapter_id"
    t.integer "user_id"
    t.string "type", limit: 255
    t.string "name", limit: 255
    t.integer "time_per_question"
    t.integer "practice_topic_id"
    t.text "problems_filtered_by"
    t.integer "test_mode", default: 0
    t.integer "study_module_item_id"
    t.jsonb "sections", default: []
    t.integer "homework_task_id"
    t.index ["chapter_id"], name: "index_evaluations_on_chapter_id"
    t.index ["study_module_item_id"], name: "index_evaluations_on_study_module_item_id"
    t.index ["user_id"], name: "index_evaluations_on_user_id"
  end

  create_table "evaluations_exercises", id: :serial, force: :cascade do |t|
    t.integer "evaluation_id"
    t.integer "problem_id"
    t.integer "position"
    t.index ["evaluation_id"], name: "index_evaluations_exercises_on_evaluation_id"
    t.index ["problem_id"], name: "index_evaluations_exercises_on_problem_id"
  end

  create_table "exam_scores", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.text "score"
    t.date "taken_on"
    t.boolean "is_official", default: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "study_module_item_id"
    t.integer "user_calendar_task_id"
    t.boolean "for_legacy_course", default: false
    t.index ["study_module_item_id"], name: "index_exam_scores_on_study_module_item_id"
    t.index ["user_calendar_task_id"], name: "index_exam_scores_on_user_calendar_task_id"
  end

  create_table "exercise_attempts", id: :serial, force: :cascade do |t|
    t.integer "exercise_id"
    t.integer "user_id"
    t.string "answer", limit: 255
    t.integer "number_attempts"
    t.boolean "correct"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "failure_reason_id"
    t.index ["exercise_id", "user_id"], name: "index_exercise_attempts_on_exercise_id_and_user_id", unique: true
    t.index ["failure_reason_id"], name: "index_exercise_attempts_on_failure_reason_id"
  end

  create_table "exercise_graphics_interpretations", id: :serial, force: :cascade do |t|
    t.string "number_of_options_per_dropdown", null: false
    t.integer "exercise_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["exercise_id"], name: "index_exercise_graphics_interpretations_on_exercise_id"
  end

  create_table "exercise_lessons", id: :serial, force: :cascade do |t|
    t.integer "exercise_id"
    t.integer "lesson_id"
    t.index ["exercise_id"], name: "index_exercise_lessons_on_exercise_id"
    t.index ["lesson_id"], name: "index_exercise_lessons_on_lesson_id"
  end

  create_table "exercise_levels", id: :serial, force: :cascade do |t|
    t.integer "exercise_id"
    t.datetime "effective_start_date", precision: nil
    t.datetime "effective_end_date", precision: nil
    t.string "level"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["exercise_id"], name: "index_exercise_levels_on_exercise_id"
  end

  create_table "exercise_multi_source_reasoning_tabs", id: :serial, force: :cascade do |t|
    t.integer "multi_source_reasoning_id"
    t.string "name", null: false
    t.string "content", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["multi_source_reasoning_id"], name: "index_multi_source_reasoning_tabs_on_multi_source_reasoning_id"
  end

  create_table "exercise_multi_source_reasonings", id: :serial, force: :cascade do |t|
    t.integer "exercise_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "col_1"
    t.string "col_2"
    t.string "question_type", default: "single_answer", null: false
    t.index ["exercise_id"], name: "index_exercise_multi_source_reasonings_on_exercise_id"
  end

  create_table "exercise_table_analyses", id: :serial, force: :cascade do |t|
    t.integer "exercise_id"
    t.string "content", null: false
    t.string "col_1", null: false
    t.string "col_2", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "fix_last_row", default: false
    t.index ["exercise_id"], name: "index_exercise_table_analyses_on_exercise_id"
  end

  create_table "exercise_two_part_analyses", id: :serial, force: :cascade do |t|
    t.string "col_1", null: false
    t.string "col_2", null: false
    t.integer "exercise_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "statement_name"
    t.index ["exercise_id"], name: "index_exercise_two_part_analyses_on_exercise_id"
  end

  create_table "exercises", id: :serial, force: :cascade do |t|
    t.text "content"
    t.text "solution"
    t.text "options"
    t.string "correct_option_index", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "number"
    t.string "type", default: "Problem"
    t.integer "chapter_id"
    t.string "level", limit: 255
    t.integer "percentage_correct", default: 0
    t.integer "avg_time", default: 0
    t.datetime "last_attempted_at", precision: nil
    t.datetime "avg_calculated_at", precision: nil
    t.string "video_id", limit: 255
    t.string "question_type", limit: 255, default: "problem_solving"
    t.string "prepended_symbol", limit: 255
    t.string "appended_symbol", limit: 255
    t.text "quantity_a"
    t.text "quantity_b"
    t.integer "number_of_options"
    t.integer "group_parent_id"
    t.integer "group_size", default: 1
    t.text "first_attempts_analytics_array"
    t.string "options_title", limit: 255
    t.integer "avg_time_correct"
    t.integer "avg_time_incorrect"
    t.integer "times_attempted", default: 0
    t.integer "instructor_id"
    t.string "image"
    t.integer "lesson_id"
    t.string "ondemand_video_id"
    t.index ["avg_calculated_at"], name: "index_exercises_on_avg_calculated_at"
    t.index ["chapter_id"], name: "index_exercises_on_chapter_id"
    t.index ["instructor_id"], name: "index_exercises_on_instructor_id"
    t.index ["last_attempted_at"], name: "index_exercises_on_last_attempted_at"
  end

  create_table "exercises_tracks", id: false, force: :cascade do |t|
    t.integer "exercise_id", null: false
    t.integer "track_id", null: false
    t.index ["track_id", "exercise_id"], name: "index_exercises_tracks_on_track_id_and_exercise_id", unique: true
  end

  create_table "experiment_users", id: :serial, force: :cascade do |t|
    t.integer "experiment_id"
    t.string "user_token", limit: 255
    t.string "variation", limit: 255
    t.boolean "converted"
    t.integer "revenue_cents", default: 0
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "user_id"
    t.index ["experiment_id"], name: "index_experiment_users_on_experiment_id"
  end

  create_table "experiments", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.text "variations"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "failure_reasons", id: :serial, force: :cascade do |t|
    t.string "reason", limit: 255
    t.integer "position"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.text "solution"
    t.string "section"
    t.string "subsection"
    t.string "image"
    t.integer "option_list_type", default: 0
  end

  create_table "flags", id: :serial, force: :cascade do |t|
    t.string "label", limit: 255
    t.integer "flaggable_id"
    t.integer "user_id"
    t.string "flaggable_type", limit: 255
    t.datetime "created_at", precision: nil
    t.index ["flaggable_id", "flaggable_type"], name: "index_flags_on_flaggable_id_and_flaggable_type"
    t.index ["user_id"], name: "index_flags_on_user_id"
  end

  create_table "flash_card_attempts", id: :serial, force: :cascade do |t|
    t.integer "flash_card_session_id"
    t.integer "user_id"
    t.integer "flash_card_id"
    t.decimal "score", precision: 2, scale: 1
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "is_mastery_increased", default: false
    t.index ["flash_card_id"], name: "index_flash_card_attempts_on_flash_card_id"
    t.index ["flash_card_session_id"], name: "index_flash_card_attempts_on_flash_card_session_id"
    t.index ["user_id"], name: "index_flash_card_attempts_on_user_id"
  end

  create_table "flash_card_decks", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "chapter_id"
    t.string "name", null: false
    t.string "section", null: false
    t.string "subsection"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_flash_card_decks_on_user_id"
  end

  create_table "flash_card_masteries", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "flash_card_id"
    t.decimal "mastery_score", precision: 2, scale: 1, default: "0.0"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["flash_card_id"], name: "index_flash_card_masteries_on_flash_card_id"
    t.index ["user_id", "flash_card_id"], name: "index_flash_card_masteries_on_user_id_and_flash_card_id", unique: true
    t.index ["user_id"], name: "index_flash_card_masteries_on_user_id"
  end

  create_table "flash_card_sessions", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.string "type"
    t.text "flash_card_ids", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "meta_info"
    t.integer "flash_card_deck_id"
    t.index ["flash_card_deck_id"], name: "index_flash_card_sessions_on_flash_card_deck_id"
    t.index ["user_id"], name: "index_flash_card_sessions_on_user_id"
  end

  create_table "flash_cards", id: :serial, force: :cascade do |t|
    t.text "front"
    t.text "back"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "user_id"
    t.integer "flash_card_deck_id"
    t.integer "word_definition_id"
    t.index ["flash_card_deck_id"], name: "index_flash_cards_on_flash_card_deck_id"
    t.index ["user_id"], name: "index_flash_cards_on_user_id"
    t.index ["word_definition_id"], name: "index_flash_cards_on_word_definition_id"
  end

  create_table "frequently_asked_questions", id: :serial, force: :cascade do |t|
    t.text "question"
    t.text "answer"
    t.string "checksum"
    t.integer "questionable_id"
    t.string "questionable_type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["questionable_id", "questionable_type"], name: "index_frequently_asked_questions_on_id_and_type"
  end

  create_table "full_contact_details", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.string "avatar"
    t.string "twitter"
    t.string "linkedin"
    t.string "facebook"
    t.string "instagram"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_full_contact_details_on_user_id"
  end

  create_table "gmat_scores", id: :serial, force: :cascade do |t|
    t.integer "verbal_score", default: 27, null: false
    t.integer "quant_score", default: 27, null: false
    t.float "verbal_percentile", null: false
    t.float "quant_percentile", null: false
    t.integer "total_score", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["verbal_score", "quant_score"], name: "index_gmat_scores_on_verbal_score_and_quant_score", unique: true
  end

  create_table "guest_requests", id: :serial, force: :cascade do |t|
    t.string "auth_token", limit: 255
    t.string "first_name", limit: 255
    t.string "last_name", limit: 255
    t.string "email", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "track_id"
    t.boolean "for_legacy_course", default: false
    t.index ["track_id"], name: "index_guest_requests_on_track_id"
  end

  create_table "homework_groups", force: :cascade do |t|
    t.string "name"
    t.bigint "online_class_session_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["online_class_session_id"], name: "index_homework_groups_on_online_class_session_id"
  end

  create_table "homework_task_comments", force: :cascade do |t|
    t.bigint "instructor_id"
    t.bigint "homework_task_id"
    t.text "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["homework_task_id"], name: "index_homework_task_comments_on_homework_task_id"
    t.index ["instructor_id"], name: "index_homework_task_comments_on_instructor_id"
  end

  create_table "homework_task_takeaways", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "homework_task_id", null: false
    t.text "content", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["homework_task_id"], name: "index_homework_task_takeaways_on_homework_task_id"
    t.index ["user_id", "homework_task_id"], name: "index_homework_task_takeaways_on_user_id_and_homework_task_id", unique: true
    t.index ["user_id"], name: "index_homework_task_takeaways_on_user_id"
  end

  create_table "homework_tasks", force: :cascade do |t|
    t.string "task_type", null: false
    t.jsonb "data", default: []
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "homework_group_id"
    t.string "title"
    t.text "description"
    t.integer "amount_of_test"
    t.string "difficulty_level"
    t.integer "chapter_id"
    t.index ["homework_group_id"], name: "index_homework_tasks_on_homework_group_id"
  end

  create_table "instructors", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "title"
    t.string "description"
    t.string "image"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "default_instructor", default: false
    t.string "score"
    t.string "large_image"
    t.string "email"
    t.string "medium_image"
  end

  create_table "item_response_theory_data", id: :serial, force: :cascade do |t|
    t.integer "exercise_id"
    t.jsonb "irt", default: {}
    t.string "cat_content_tag", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["exercise_id"], name: "index_item_response_theory_data_on_exercise_id"
  end

  create_table "knowledge_bases", force: :cascade do |t|
    t.string "section"
    t.string "knowledge_base_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "leads", id: :serial, force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "email"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "plan_code"
    t.boolean "unsubscribed", default: false
    t.string "referred_by", default: "checkout"
    t.boolean "is_email_valid"
    t.string "intercom_contact_id"
    t.string "confirmation_token"
    t.datetime "confirmation_token_expires_at", precision: nil
    t.datetime "confirmation_token_sent_at", precision: nil
    t.datetime "confirmed_at", precision: nil
    t.index ["email"], name: "index_leads_on_email"
  end

  create_table "lesson_accesses", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "lesson_id"
    t.integer "chapter_id"
    t.datetime "created_at", precision: nil
    t.boolean "end_chapter", default: false
    t.index ["chapter_id", "user_id"], name: "index_lesson_accesses_on_chapter_id_and_user_id"
    t.index ["lesson_id", "user_id"], name: "index_lesson_accesses_on_lesson_id_and_user_id"
    t.index ["lesson_id"], name: "index_lesson_accesses_on_lesson_id"
    t.index ["user_id"], name: "index_lesson_accesses_on_user_id"
  end

  create_table "lesson_feedbacks", id: :serial, force: :cascade do |t|
    t.integer "lesson_id"
    t.integer "user_id"
    t.integer "score"
    t.text "comment"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "status", default: "unresolved", null: false
    t.datetime "resolved_at", precision: nil
    t.integer "resolved_by_id"
    t.index ["lesson_id"], name: "index_lesson_feedbacks_on_lesson_id"
    t.index ["resolved_by_id"], name: "index_lesson_feedbacks_on_resolved_by_id"
    t.index ["user_id"], name: "index_lesson_feedbacks_on_user_id"
  end

  create_table "lesson_tracks", id: :serial, force: :cascade do |t|
    t.integer "lesson_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "dynamic_lesson_number", limit: 255, default: ""
    t.integer "track_id"
    t.integer "time_to_read"
    t.index ["lesson_id"], name: "index_lesson_tracks_on_lesson_id"
    t.index ["track_id"], name: "index_lesson_tracks_on_track_id"
  end

  create_table "lessons", id: :serial, force: :cascade do |t|
    t.string "type", limit: 255
    t.string "full_number", limit: 255
    t.string "name", limit: 255
    t.text "content"
    t.integer "parent_id"
    t.integer "chapter_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "thumbnail", limit: 255
    t.string "order_number", limit: 255
    t.integer "number"
    t.integer "easy_problem_count", default: 0
    t.integer "medium_problem_count", default: 0
    t.integer "hard_problem_count", default: 0
    t.string "section", limit: 255
    t.string "subsection", limit: 255
    t.datetime "archived_at", precision: nil
    t.integer "replaced_by"
    t.integer "video_timestamp"
    t.index ["archived_at"], name: "index_lessons_on_archived_at"
    t.index ["chapter_id"], name: "index_lessons_on_chapter_id"
    t.index ["parent_id"], name: "index_lessons_on_parent_id"
  end

  create_table "marketing_modals", id: :serial, force: :cascade do |t|
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.string "title", null: false
    t.string "subtitle"
    t.text "message", null: false
    t.boolean "enable_call_action", default: false
    t.boolean "custom_subgroup", default: false
    t.string "button_label"
    t.string "url"
    t.string "filter"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "desktop_image"
    t.string "mobile_image"
    t.integer "modal_type", default: 0
    t.string "modal_page", default: "homescreen"
    t.index ["end_date", "modal_page"], name: "index_marketing_modals_on_end_date_and_modal_page", unique: true
    t.index ["start_date", "modal_page"], name: "index_marketing_modals_on_start_date_and_modal_page", unique: true
  end

  create_table "messages", id: :serial, force: :cascade do |t|
    t.text "content", null: false
    t.datetime "read_at", precision: nil
    t.datetime "deleted_at", precision: nil
    t.integer "sender_id"
    t.integer "recipient_id"
    t.integer "lesson_feedback_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "announcement_id"
    t.index ["announcement_id"], name: "index_messages_on_announcement_id"
    t.index ["lesson_feedback_id"], name: "index_messages_on_lesson_feedback_id"
    t.index ["recipient_id"], name: "index_messages_on_recipient_id"
    t.index ["sender_id"], name: "index_messages_on_sender_id"
  end

  create_table "must_knows", id: :serial, force: :cascade do |t|
    t.integer "lesson_id"
    t.text "content"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["lesson_id"], name: "index_must_knows_on_lesson_id"
  end

  create_table "notes", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "noteable_id"
    t.string "noteable_type", limit: 255
    t.text "annotation"
    t.text "selected_text"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "color", limit: 255, default: "color-1"
    t.string "note_type", limit: 255, default: "text"
    t.string "checksum", limit: 255
    t.index ["noteable_id", "noteable_type"], name: "index_notes_on_noteable_id_and_noteable_type"
    t.index ["user_id"], name: "index_notes_on_user_id"
  end

  create_table "notification_statuses", id: :serial, force: :cascade do |t|
    t.integer "notification_id"
    t.integer "user_id"
    t.datetime "read_at", precision: nil
    t.string "deleted_at"
    t.string "datetime"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["notification_id", "user_id"], name: "index_notification_statuses_on_notification_id_and_user_id"
  end

  create_table "notifications", id: :serial, force: :cascade do |t|
    t.text "message"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "sticky", default: false
  end

  create_table "official_guide_question_numbers", force: :cascade do |t|
    t.bigint "chapter_id"
    t.jsonb "question_numbers", default: [], null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "guide_version", default: "2025-2026", null: false
    t.index ["chapter_id"], name: "index_official_guide_question_numbers_on_chapter_id"
  end

  create_table "ondemand_weekly_hour_instructors", force: :cascade do |t|
    t.integer "instructor_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "ondemand_weekly_hours", force: :cascade do |t|
    t.string "day", null: false
    t.string "start_time"
    t.string "end_time"
    t.string "zoom_link"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "online_class_contacts", id: :serial, force: :cascade do |t|
    t.string "email", null: false
    t.integer "online_class_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["online_class_id"], name: "index_online_class_contacts_on_online_class_id"
  end

  create_table "online_class_messages", id: :serial, force: :cascade do |t|
    t.integer "online_class_id"
    t.integer "sender_id"
    t.string "message", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "subject"
    t.string "cc_emails"
    t.index ["online_class_id"], name: "index_online_class_messages_on_online_class_id"
    t.index ["sender_id"], name: "index_online_class_messages_on_sender_id"
  end

  create_table "online_class_session_documents", force: :cascade do |t|
    t.bigint "online_class_session_id"
    t.string "name"
    t.string "file"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["online_class_session_id"], name: "idx_on_online_class_session_id_d9026ed90a"
  end

  create_table "online_class_sessions", id: :serial, force: :cascade do |t|
    t.integer "online_class_id"
    t.string "description"
    t.date "date"
    t.string "start_time"
    t.string "end_time"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "homework_task_data"
    t.integer "homework_status", default: 0
    t.string "zoom_link"
    t.string "recording_url"
    t.boolean "show_in_library", default: false
    t.index ["online_class_id"], name: "index_online_class_sessions_on_online_class_id"
  end

  create_table "online_classes", id: :serial, force: :cascade do |t|
    t.string "start_time"
    t.string "end_time"
    t.integer "spots"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "instructor_id"
    t.string "syllabus"
    t.string "test_type"
    t.string "zoom_link"
    t.index ["instructor_id"], name: "index_online_classes_on_instructor_id"
  end

  create_table "online_classes_users", id: :serial, force: :cascade do |t|
    t.integer "online_class_id", null: false
    t.integer "user_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["online_class_id", "user_id"], name: "index_online_classes_users_on_online_class_id_and_user_id"
  end

  create_table "percentile_rankings", id: :serial, force: :cascade do |t|
    t.integer "gmat_score", null: false
    t.integer "percentile", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["gmat_score"], name: "index_percentile_rankings_on_gmat_score", unique: true
  end

  create_table "permissions", id: :serial, force: :cascade do |t|
    t.integer "role_id"
    t.string "module_name"
    t.string "action_type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["role_id"], name: "index_permissions_on_role_id"
  end

  create_table "possible_duplicates", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.text "duplicate_from"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.boolean "is_blacklisted", default: false
    t.index ["user_id"], name: "index_possible_duplicates_on_user_id"
  end

  create_table "problem_attempts", id: :serial, force: :cascade do |t|
    t.datetime "start_time", precision: nil
    t.datetime "end_time", precision: nil
    t.string "answer", limit: 255
    t.integer "problem_id"
    t.integer "evaluation_attempt_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.datetime "paused_at", precision: nil
    t.integer "total_seconds_paused", default: 0
    t.boolean "correct"
    t.integer "total_elapsed_seconds", default: 0
    t.integer "failure_reason_id"
    t.boolean "is_last_attempt", default: true
    t.integer "seconds_paused", default: 0
    t.decimal "standard_error_of_estimation", precision: 8, scale: 2
    t.index ["evaluation_attempt_id", "problem_id"], name: "index_problem_attempts_on_evaluation_attempt_id_and_problem_id", unique: true
    t.index ["evaluation_attempt_id"], name: "index_problem_attempts_on_evaluation_attempt_id"
    t.index ["problem_id"], name: "index_problem_attempts_on_problem_id"
  end

  create_table "problem_example_tags", id: :serial, force: :cascade do |t|
    t.integer "problem_id"
    t.integer "example_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["example_id"], name: "index_problem_example_tags_on_example_id"
    t.index ["problem_id"], name: "index_problem_example_tags_on_problem_id"
  end

  create_table "questionnaire_attempts", id: :serial, force: :cascade do |t|
    t.integer "exam_score_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "questionnaire_id"
    t.index ["exam_score_id"], name: "index_questionnaire_attempts_on_exam_score_id"
    t.index ["questionnaire_id"], name: "index_questionnaire_attempts_on_questionnaire_id"
  end

  create_table "questionnaire_categories", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "icon"
    t.text "solution"
    t.integer "order"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "questionnaire_id"
    t.index ["questionnaire_id"], name: "index_questionnaire_categories_on_questionnaire_id"
  end

  create_table "questionnaire_question_attempts", id: :serial, force: :cascade do |t|
    t.integer "questionnaire_attempt_id"
    t.integer "questionnaire_question_id"
    t.integer "questionnaire_question_option_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["questionnaire_attempt_id"], name: "index_question_attempts_on_questionnaire_attempt_id"
    t.index ["questionnaire_question_id"], name: "index_question_attempts_on_questionnaire_question_id"
    t.index ["questionnaire_question_option_id"], name: "index_question_attempts_on_questionnaire_question_options_id"
  end

  create_table "questionnaire_question_options", id: :serial, force: :cascade do |t|
    t.integer "questionnaire_question_id"
    t.string "content"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "value", default: 0
    t.integer "order"
    t.index ["questionnaire_question_id"], name: "index_question_options_on_questionnaire_question_id"
  end

  create_table "questionnaire_questions", id: :serial, force: :cascade do |t|
    t.integer "questionnaire_category_id"
    t.text "content"
    t.integer "order"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["questionnaire_category_id"], name: "index_questionnaire_questions_on_questionnaire_category_id"
  end

  create_table "questionnaires", id: :serial, force: :cascade do |t|
    t.boolean "for_legacy_course", default: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "reading_comprehension_details", id: :serial, force: :cascade do |t|
    t.integer "exercise_id", null: false
    t.text "passage", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "strategy_review"
    t.text "passage_analysis"
    t.text "vocabulary_clarification"
    t.text "sentence_simplification"
    t.string "question_type", default: "single_answer", null: false
    t.string "strategy_review_video_id"
    t.string "passage_analysis_video_id"
    t.string "vocabulary_clarification_video_id"
    t.string "sentence_simplification_video_id"
    t.string "passage_heading"
    t.index ["exercise_id"], name: "index_reading_comprehension_details_on_exercise_id"
  end

  create_table "reading_comprehension_overrides", id: :serial, force: :cascade do |t|
    t.integer "exercise_id", null: false
    t.text "passage"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "question_type"
    t.index ["exercise_id"], name: "index_reading_comprehension_overrides_on_exercise_id"
  end

  create_table "roles", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.integer "resource_id"
    t.string "resource_type", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["name", "resource_type", "resource_id"], name: "index_roles_on_name_and_resource_type_and_resource_id"
    t.index ["name"], name: "index_roles_on_name"
  end

  create_table "sale_banners", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "discount"
    t.date "start_date"
    t.date "end_date"
    t.string "color"
    t.string "sale_type", default: "special"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "is_custom_name", default: false
  end

  create_table "sections", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "sessions", id: :serial, force: :cascade do |t|
    t.string "session_id", null: false
    t.text "data"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["session_id"], name: "index_sessions_on_session_id", unique: true
    t.index ["updated_at"], name: "index_sessions_on_updated_at"
  end

  create_table "settings", id: :serial, force: :cascade do |t|
    t.string "key", null: false
    t.string "value", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "study_module_items", id: :serial, force: :cascade do |t|
    t.text "name"
    t.string "item_type", limit: 255
    t.integer "order"
    t.boolean "has_checkbox"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.text "user_evaluation_chapter_ids"
    t.integer "topic_id"
    t.integer "study_module_subsection_id"
    t.string "title"
    t.index ["study_module_subsection_id"], name: "index_study_module_items_on_study_module_subsection_id"
    t.index ["topic_id"], name: "index_study_module_items_on_topic_id"
  end

  create_table "study_module_subsections", id: :serial, force: :cascade do |t|
    t.integer "study_module_id"
    t.integer "chapter_id"
    t.string "section", limit: 255
    t.string "name", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "order"
  end

  create_table "study_modules", id: :serial, force: :cascade do |t|
    t.integer "study_plan_id"
    t.string "name", limit: 255
    t.string "subtitle", limit: 255
    t.integer "order"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.boolean "show_in_long_study_plan", default: true
    t.boolean "show_in_short_study_plan", default: true
    t.string "phase", limit: 255
  end

  create_table "study_plan_settings", id: :serial, force: :cascade do |t|
    t.integer "user_id", null: false
    t.boolean "is_calendar_view_enabled", default: false
    t.boolean "is_study_hours_per_week", default: true
    t.date "study_start_date"
    t.text "study_hours_per_day"
    t.text "study_dates_to_exclude"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "calendar_view_type", default: "month"
    t.integer "study_hours_per_week"
    t.index ["user_id"], name: "index_study_plan_settings_on_user_id", unique: true
  end

  create_table "study_plans", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "track_id"
    t.index ["track_id"], name: "index_study_plans_on_track_id"
  end

  create_table "sub_exercises", id: :serial, force: :cascade do |t|
    t.integer "exercise_id"
    t.text "title"
    t.integer "position"
    t.text "options"
    t.integer "correct_option_index"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["exercise_id"], name: "index_sub_exercises_on_exercise_id"
  end

  create_table "sub_sections", id: :serial, force: :cascade do |t|
    t.string "name", null: false
    t.integer "section_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["section_id"], name: "index_sub_sections_on_section_id"
  end

  create_table "targeted_practice_details", id: :serial, force: :cascade do |t|
    t.integer "exercise_id", null: false
    t.text "instructions"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "question_kind"
    t.index ["exercise_id"], name: "index_targeted_practice_details_on_exercise_id"
  end

  create_table "test_dates", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.date "next_exam_date", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_test_dates_on_user_id"
  end

  create_table "testimonials", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.text "description"
    t.string "photo", limit: 255
    t.string "video_id", limit: 255
    t.string "video_photo", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "tracks", id: :serial, force: :cascade do |t|
    t.integer "evaluation_id"
    t.string "name", limit: 255
    t.string "score_from", limit: 255
    t.string "score_to", limit: 255
    t.text "target_accuracy"
    t.text "problem_levels_to_show"
    t.text "diagnostic_problem_ids"
    t.text "diagnostic_problem_chapter_multitags"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.text "calendar_structure"
    t.boolean "for_legacy_course", default: false
    t.index ["evaluation_id"], name: "index_tracks_on_evaluation_id"
  end

  create_table "transactions", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "recurly_invoice_number"
    t.string "plan_code"
    t.string "coupon_code"
    t.integer "discount_amount_cents"
    t.integer "total_amount_cents"
    t.datetime "closed_at", precision: nil
    t.string "purchase_country"
    t.boolean "is_recurring", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "partially_refunded", default: false
    t.boolean "totally_refunded", default: false
    t.integer "original_recurly_invoice_number"
    t.boolean "first_full_plan", default: false
    t.index ["recurly_invoice_number"], name: "index_transactions_on_recurly_invoice_number"
    t.index ["user_id"], name: "index_transactions_on_user_id"
  end

  create_table "user_analysis_data", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.date "improvement_check_at"
    t.integer "one_week_back_accuracy"
    t.integer "two_weeks_back_accuracy"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["user_id"], name: "index_user_analysis_data_on_user_id"
  end

  create_table "user_calendar_tasks", id: :serial, force: :cascade do |t|
    t.integer "user_calendar_id"
    t.integer "study_module_item_id"
    t.date "schedule_date"
    t.date "rescheduled_date"
    t.integer "task_study_time", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "test_level"
    t.integer "topic_from"
    t.integer "topic_to"
    t.boolean "is_completed", default: false
    t.boolean "is_skipped", default: false
    t.datetime "completed_at", precision: nil
    t.index ["study_module_item_id"], name: "index_user_calendar_tasks_on_study_module_item_id"
    t.index ["user_calendar_id"], name: "index_user_calendar_tasks_on_user_calendar_id"
  end

  create_table "user_calendars", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "invalidated_at", precision: nil
    t.index ["user_id"], name: "index_user_calendars_on_user_id", unique: true
  end

  create_table "user_data", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.string "key", null: false
    t.text "value"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_user_data_on_user_id"
  end

  create_table "user_diagnostics", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "evaluation_id"
    t.integer "track_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["evaluation_id"], name: "index_user_diagnostics_on_evaluation_id"
    t.index ["track_id"], name: "index_user_diagnostics_on_track_id"
    t.index ["user_id"], name: "index_user_diagnostics_on_user_id"
  end

  create_table "user_referral_rewards", force: :cascade do |t|
    t.bigint "user_referral_id", null: false
    t.boolean "was_reward_granted", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_referral_id"], name: "index_user_referral_rewards_on_user_referral_id", unique: true
  end

  create_table "user_referrals", id: :serial, force: :cascade do |t|
    t.string "email", null: false
    t.boolean "is_reminder_set", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "referral_user_id"
    t.boolean "was_reward_granted", default: false
    t.integer "referrer_user_id", null: false
    t.index ["referral_user_id"], name: "index_user_referrals_on_referral_user_id"
    t.index ["referrer_user_id"], name: "index_user_referrals_on_referrer_user_id"
  end

  create_table "user_stats", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.string "page", limit: 255
    t.text "content"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.datetime "invalidated_at", precision: nil
    t.index ["user_id"], name: "index_user_stats_on_user_id"
  end

  create_table "user_times", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "all_page", default: 0
    t.date "date"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_user_times_on_user_id"
  end

  create_table "users", id: :serial, force: :cascade do |t|
    t.string "email", limit: 255, default: "", null: false
    t.string "encrypted_password", limit: 255, default: "", null: false
    t.string "reset_password_token", limit: 255
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip", limit: 255
    t.string "last_sign_in_ip", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "confirmation_token", limit: 255
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email", limit: 255
    t.string "name", limit: 255
    t.string "recurly_account_code", limit: 255
    t.string "cancel_reason", limit: 255
    t.boolean "welcome_showed", default: false
    t.boolean "exam_taken", default: false
    t.boolean "is_next_exam_date_undefined", default: false
    t.string "time_zone", default: "Pacific Time (US & Canada)"
    t.string "first_name", limit: 255
    t.string "last_name", limit: 255
    t.datetime "subscription_ends_at", precision: nil
    t.boolean "on_trial"
    t.string "current_plan", limit: 255
    t.string "previous_plan", limit: 255
    t.boolean "short_study_plan", default: false
    t.text "exam_scores"
    t.string "tapfiliate_conversion_id", limit: 255
    t.boolean "guest", default: false
    t.string "referred_by", limit: 255
    t.string "organization", limit: 255
    t.boolean "is_going_to_apply_next_mlt_cycle"
    t.string "business_school_admission_application", limit: 255
    t.string "auth_token", limit: 255
    t.integer "transactions_total_value_cents"
    t.string "chapters_view", limit: 255, default: "thumbs"
    t.string "program_applying_for", limit: 255
    t.text "session_data"
    t.text "active_sections", default: "---\n- quant\n- verbal\n- di\n- bwa\n"
    t.integer "track_id"
    t.integer "cancel_reason_id"
    t.string "session_identifier"
    t.boolean "study_plan_guide_enabled", default: true
    t.integer "failed_attempts", default: 0, null: false
    t.datetime "locked_at", precision: nil
    t.string "shareasale_trial_order_number"
    t.integer "nps"
    t.string "font_size_preference", default: "large"
    t.boolean "mba_admission", default: false
    t.string "recurly_coupon_code"
    t.string "phone"
    t.boolean "sms_enabled", default: false
    t.string "goal_matriculation_year"
    t.boolean "signed_up_for_free_account", default: false
    t.string "recurly_coupon_code_2"
    t.integer "ai_assist_message_count", default: 0
    t.boolean "ondemand_enabled", default: false
    t.integer "ai_tutor_hint_counter", default: 0
    t.boolean "full_ondemand_access", default: false
    t.boolean "referral_rock_commission_generated", default: false
    t.boolean "show_og_questions", default: true
    t.string "guide_year"
    t.index ["cancel_reason_id"], name: "index_users_on_cancel_reason_id"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["recurly_account_code"], name: "index_users_on_recurly_account_code", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["track_id"], name: "index_users_on_track_id"
  end

  create_table "users_roles", id: false, force: :cascade do |t|
    t.integer "user_id"
    t.integer "role_id"
    t.index ["user_id", "role_id"], name: "index_users_roles_on_user_id_and_role_id"
  end

  create_table "utm_events", id: :serial, force: :cascade do |t|
    t.string "source", limit: 255
    t.string "medium", limit: 255
    t.string "campaign", limit: 255
    t.string "term", limit: 255
    t.string "content", limit: 255
    t.string "event", limit: 255
    t.string "landing_path", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "utmable_id"
    t.string "utmable_type", limit: 255
  end

  create_table "versions", id: :serial, force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object"
    t.datetime "created_at", precision: nil
    t.text "object_changes"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "video_sections", id: :serial, force: :cascade do |t|
    t.string "name"
    t.integer "order"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "videos", id: :serial, force: :cascade do |t|
    t.integer "videable_id"
    t.string "videable_type", limit: 255
    t.string "video_id", limit: 255
    t.string "title", limit: 255
    t.string "subtitle", limit: 255
    t.string "duration", limit: 255
    t.string "image", limit: 255
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "order"
    t.index ["videable_id"], name: "index_videos_on_videable_id"
  end

  create_table "word_definitions", id: :serial, force: :cascade do |t|
    t.string "word"
    t.string "part_of_speech"
    t.string "synonyms"
    t.integer "chapter_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "level"
    t.string "example_sentence"
    t.string "definition"
    t.index ["chapter_id"], name: "index_word_definitions_on_chapter_id"
  end

  create_table "word_definitions_other_forms", id: :serial, force: :cascade do |t|
    t.string "form"
    t.string "definition"
    t.integer "word_definition_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "word"
    t.index ["word_definition_id"], name: "index_word_definitions_other_forms_on_word_definition_id"
  end

  add_foreign_key "ai_assist_chat_messages", "users"
  add_foreign_key "ai_assist_summaries", "lessons"
  add_foreign_key "ai_assist_summaries", "users"
  add_foreign_key "announcements", "users", column: "sender_id"
  add_foreign_key "background_tasks", "evaluation_attempts"
  add_foreign_key "credit_card_details", "users"
  add_foreign_key "daily_questions", "exercises"
  add_foreign_key "evaluations", "study_module_items"
  add_foreign_key "exercise_graphics_interpretations", "exercises"
  add_foreign_key "exercise_multi_source_reasoning_tabs", "exercise_multi_source_reasonings", column: "multi_source_reasoning_id"
  add_foreign_key "exercise_multi_source_reasonings", "exercises"
  add_foreign_key "exercise_table_analyses", "exercises"
  add_foreign_key "exercise_two_part_analyses", "exercises"
  add_foreign_key "exercises", "instructors"
  add_foreign_key "flash_card_attempts", "flash_card_sessions"
  add_foreign_key "flash_card_attempts", "flash_cards"
  add_foreign_key "flash_card_attempts", "users"
  add_foreign_key "flash_card_decks", "users"
  add_foreign_key "flash_card_masteries", "flash_cards"
  add_foreign_key "flash_card_masteries", "users"
  add_foreign_key "flash_card_sessions", "flash_card_decks"
  add_foreign_key "flash_card_sessions", "users"
  add_foreign_key "flash_cards", "flash_card_decks"
  add_foreign_key "flash_cards", "users"
  add_foreign_key "flash_cards", "word_definitions"
  add_foreign_key "full_contact_details", "users"
  add_foreign_key "homework_groups", "online_class_sessions"
  add_foreign_key "homework_task_comments", "homework_tasks"
  add_foreign_key "homework_task_comments", "instructors"
  add_foreign_key "homework_task_takeaways", "users"
  add_foreign_key "homework_tasks", "homework_groups"
  add_foreign_key "item_response_theory_data", "exercises"
  add_foreign_key "lesson_feedbacks", "lessons"
  add_foreign_key "lesson_feedbacks", "users"
  add_foreign_key "lesson_feedbacks", "users", column: "resolved_by_id"
  add_foreign_key "messages", "lesson_feedbacks"
  add_foreign_key "messages", "users", column: "recipient_id"
  add_foreign_key "messages", "users", column: "sender_id"
  add_foreign_key "online_class_contacts", "online_classes"
  add_foreign_key "online_class_messages", "online_classes"
  add_foreign_key "online_class_messages", "users", column: "sender_id"
  add_foreign_key "online_class_session_documents", "online_class_sessions"
  add_foreign_key "online_class_sessions", "online_classes"
  add_foreign_key "permissions", "roles"
  add_foreign_key "questionnaire_attempts", "exam_scores"
  add_foreign_key "questionnaire_question_attempts", "questionnaire_attempts"
  add_foreign_key "questionnaire_question_attempts", "questionnaire_question_options"
  add_foreign_key "questionnaire_question_attempts", "questionnaire_questions"
  add_foreign_key "questionnaire_question_options", "questionnaire_questions"
  add_foreign_key "questionnaire_questions", "questionnaire_categories"
  add_foreign_key "reading_comprehension_details", "exercises"
  add_foreign_key "reading_comprehension_overrides", "exercises"
  add_foreign_key "study_plan_settings", "users"
  add_foreign_key "sub_sections", "sections"
  add_foreign_key "targeted_practice_details", "exercises"
  add_foreign_key "test_dates", "users"
  add_foreign_key "transactions", "users"
  add_foreign_key "user_calendar_tasks", "study_module_items"
  add_foreign_key "user_calendar_tasks", "user_calendars"
  add_foreign_key "user_calendars", "users"
  add_foreign_key "user_diagnostics", "evaluations"
  add_foreign_key "user_diagnostics", "tracks"
  add_foreign_key "user_diagnostics", "users"
  add_foreign_key "user_referral_rewards", "user_referrals"
  add_foreign_key "user_referrals", "users", column: "referral_user_id"
  add_foreign_key "user_referrals", "users", column: "referrer_user_id"
  add_foreign_key "user_times", "users"
  add_foreign_key "users", "cancel_reasons"
end
