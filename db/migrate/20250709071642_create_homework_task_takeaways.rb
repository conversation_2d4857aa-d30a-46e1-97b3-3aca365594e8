class CreateHomeworkTaskTakeaways < ActiveRecord::Migration[7.2]
  def change
    create_table :homework_task_takeaways do |t|
      t.bigint :user_id, null: false
      t.bigint :homework_task_id, null: false
      t.text :content, null: false

      t.timestamps
    end

    add_foreign_key :homework_task_takeaways, :users
    add_index :homework_task_takeaways, %i(user_id homework_task_id), unique: true
  end
end
