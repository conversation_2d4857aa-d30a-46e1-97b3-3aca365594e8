# frozen_string_literal: true

class Dm5083 < ActiveRecord::Migration[7.2]
  def up
    ActiveRecord::Base.transaction do
      data_migration_except_for_sat
    end
  end

  def down
    raise ActiveRecord::IrreversibleMigration
  end

  private

  def data_migration_except_for_sat
    return if EXAM_NAME == "sat"

    OnlineClassSession.find_each do |session|
      next if session.homework_task_data.blank?

      problem_ids = session.homework_task_data

      homework_tasks = HomeworkTask.where(id: problem_ids)

      if homework_tasks.count == problem_ids.count
        puts "Session #{session.id} already uses HomeworkTasks"
      else
        new_task = HomeworkTask.create!(
          task_type: HomeworkTask::TASK_TYPES[:custom_test],
          data: problem_ids,
          online_class_session_id: session.id
        )
        session.update!(homework_task_data: [new_task.id])

        UserEvaluation.where(name: "LiveTeach #{Time.zone.parse(session.date.to_s).strftime('%b %-d %Y')} (#{session.id}) - Homework").update_all(homework_task_id: new_task.id)

        puts "Updated session #{session.id} with new HomeworkTask #{new_task.id}"
      end
    end
  end
end
