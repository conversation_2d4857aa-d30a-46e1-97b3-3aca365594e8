namespace :online_class_session do
  desc "Updates homework status of online class session on its end time"
  task update_homework_status: [:environment] do
    zone = "Eastern Time (US & Canada)"

    OnlineClassSession.ready_for_release.each do |online_class_session|
      next if ActiveSupport::TimeZone[zone].parse("#{online_class_session.date} #{online_class_session.end_time}") >= ActiveSupport::TimeZone[zone].now

      online_class_session.update!(homework_status: :released)
    end
  end
end
