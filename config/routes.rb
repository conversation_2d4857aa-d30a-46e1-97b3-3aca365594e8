class NotApiConstraint
  def matches?(_request)
    !Setting.for("ttp_api_enabled")
  end
end

Ttp::Application.routes.draw do
  mount Ckeditor::Engine => "/ckeditor"
  # This is a momentary hack for ckeditor to retrieve its icons from the right place
  # TODO: Remove this hack when ckeditor is upgraded
  get "/:icon_file", to: redirect("/assets/ckeditor/skins/moono/%{icon_file}"),
    constraints: { icon_file: /icons(_hidpi)?\.png/ }

  if Rails.env.production?
    constraints subdomain: "" do
      get ":current_path", to: redirect(subdomain: "www", path: "/%{current_path}"), current_path: /.*/
    end

    constraints subdomain: "www" do
      get ":current_path", to: redirect(subdomain: "www", path: "/"), current_path: /\b(?!(company|berkeley|ucberkeley)\b)\w+/
    end
  end

  devise_for :users, controllers: { confirmations: "confirmations", sessions: "sessions", passwords: "passwords", devise_authy: "devise_authy" }

  as :user do
    get "users/sign_in/:token" => "sessions#sign_in_with_token", as: :sign_in_with_token
    get "users/instructions_sent", to: "passwords#instructions_sent", as: :instructions_sent
    get "users/edit" => "registrations#edit", as: :edit_user_registration
    put "users/:id" => "registrations#update", as: :user_registration
    get "users/email_sent" => "confirmations#email_sent", as: :email_sent
    patch "/user/confirmation" => "confirmations#update", via: :patch, as: :update_user_confirmation
  end

  put "users/:id/tapfiliate" => "subscriptions#add_tapfiliate_conversion_id_to_user", :as => :add_tapfiliate_conversion_id_to_user

  get "/admin/translations(/:locale)", to: "translations#index", as: "admin_translations"
  post "/admin/translations(/:locale)", to: "translations#update", as: "admin_update_translations"
  delete "/admin/translations", to: "translations#destroy", as: "admin_destroy_translations"
  get "admin/translations/:locale/edit", to: "translations#edit", as: "admin_edit_translations"
  get "admin/translations/:locale/new", to: "translations#new", as: "admin_new_translations"

  get "/lessons/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, to: "lessons#index", as: :lessons
  get "/index_chapter_topics/:id", to: "lessons#index_chapter_topics", as: :index_chapter_topcis
  get "/lessons/:section/list", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, to: "lessons#list", as: :lessons_list
  get "/lesson/:id", to: "lessons#show", as: :lesson
  get "/lesson/:id/end_chapter", to: "lessons#end_chapter", as: :end_chapter
  get "/chapter/:id/continue_reading", to: "lessons#continue_reading", as: :continue_reading_chapter

  post "/lesson/:id/update", to: "lessons#update", as: :update_lesson
  get "/equation_guide", to: "lessons#equation_guide", as: :equation_guide

  post "/audit_comments", to: "audit_comments#create", as: :audit_comments
  post "/audit_comment/:id/resolved", to: "audit_comments#resolve", as: :audit_comment_resolve

  get "/editors/list_chapters", to: "editors#list_chapters", as: :editors_list_chapters
  get "/editors/list_problems/:chapter_id", to: "editors#list_problems", as: :editors_list_problems
  get "/problems/problem/:id", to: "problems#show", as: :show_problem

  namespace :exercises do
    resources :grouped, only: %w(show)
  end

  resources :exercises, only: [] do
    resources :frequently_asked_questions, only: [:index]
  end

  # Old URL using redirection to new URL structure
  # TODO: Disable when not needed anymore
  get "/evaluations/chapters_list", to: redirect("/editors/list_chapters")
  get "/evaluations/problems_list/:chapter_id", to: redirect("/editors/list_problems/%{chapter_id}")
  get "/problem/:id", to: redirect("/editors/problem/%{id}")
  # --- End legacy routes redirection

  get "/evaluation/:evaluation_attempt_id/problem/:problem_attempt_id", to: "problem_solving#ask", as: :problem_solving_ask
  get "/evaluation/:evaluation_attempt_id/problem/:problem_attempt_id/review_section", to: "problem_solving#show_review_section", as: :show_review_section
  get "/evaluation/:evaluation_attempt_id/problem/:problem_attempt_id/hide_review_section", to: "problem_solving#hide_review_section", as: :hide_review_section
  post "/evaluation/answer_problem", to: "problem_solving#answer", as: :problem_solving_answer
  post "/evaluation/show_solution", to: "problem_solving#show_solution", as: :problem_solving_show_solution
  get "/evaluation/:id/start", to: "evaluations#start", as: :evaluation_start
  get "/evaluation/:attempt_id/continue", to: "evaluations#continue", as: :evaluation_continue
  post "/evaluation/:attempt_id/current_section", to: "evaluations#mark_current_section_as_timed_out", as: :evaluation_mark_current_section_as_timed_out
  delete "/evaluation/:id", to: "evaluations#destroy", as: :evaluation_destroy

  get "/evaluation/:evaluation_attempt_id/problem/:problem_attempt_id/pause", to: "problem_solving#pause", as: :problem_solving_pause
  get "/evaluation/:evaluation_attempt_id/problem/:problem_attempt_id/resume", to: "problem_solving#resume", as: :problem_solving_resume
  get "/evaluation/:evaluation_attempt_id/final_page", to: "problem_solving#final_page", as: :problem_solving_final_page
  get "/evaluation/:evaluation_attempt_id/end_intermediate_section", to: "problem_solving#end_intermediate_section", as: :problem_solving_end_intermediate_section
  get "/evaluation/:evaluation_attempt_id/review_center", to: "problem_solving#review_center", as: :problem_solving_review_center
  put "/evaluation/finish", to: "problem_solving#finish", as: :problem_solving_finish

  get "/evaluations/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, to: "evaluations#index", as: :evaluations
  get "/evaluations/custom", to: "evaluations#custom", as: :custom_evaluations
  get "/evaluation/diagnostic_list", to: "evaluations#diagnostic_list", as: :diagnostic_list

  get "custom_test", to: "evaluations#custom_test", as: :new_custom_test
  post "custom_test", to: "evaluations#create_custom_test", as: :create_custom_test

  post "chapter_test", to: "evaluations#create_chapter_test", as: :create_chapter_test
  post "diagnostic_test", to: "evaluations#create_diagnostic_test", as: :create_diagnostic_test

  post "study_module_item_test", to: "evaluations#create_study_module_item_test", as: :create_study_module_item_test

  post "create_homework_custom_test", to: "evaluations#create_homework_custom_test", as: :create_homework_custom_test

  get "resources", to: "users#resources", as: :resources
  get "study_plan", to: "users#study_plan", as: :study_plan
  get "home", to: "users#home", as: :home
  get "settings", to: "users#settings", as: :settings
  get "active_review", to: "users#active_review", as: :active_review
  get "online_tutoring", to: "users#online_tutoring", as: :online_tutoring
  post "delete_account", to: "users#delete_account"

  get "home/prep_toolbox", to: "dashboards#prep_toolbox", as: :dashboards_prep_toolbox
  get "home/self_study", to: "dashboards#self_study", as: :dashboards_self_study
  get "home/online_classes", to: "dashboards#online_classes", as: :dashboard_online_classes
  get "home/next_liveteach_class", to: "dashboards#next_liveteach_class", as: :dashboard_next_liveteach_class
  get "home/admissions", to: "dashboards#admissions", as: :dashboards_admissions
  get "home/ondemand", to: "dashboards#ondemand", as: :dashboards_ondemand

  post "ai_assist/chat", to: "ai_assist/chatbots#chat", as: :ai_assist
  namespace :ai_assist do
    resources :chat_messages, only: %i(edit update)
    resources :summaries do
      collection do
        get :load_ai_assist_summaries
      end
    end
    resources :tutors
    resources :exercises, only: :create do
      collection do
        get :load_ai_assist_exercises
      end
    end
    resources :flashcards, only: %w(create show update) do
      collection do
        post :save
        get :finish
      end
    end
  end

  get "ai_assist/remaining_messages", to: "ai_assist/chatbots#remaining_messages"

  resources :referrals, only: %w(index create)
  resources :background_tasks, only: %w(show)

  # TODO: Extract Study Plan to it own controller
  get "/users/study_plan/modals", to: "users#show_study_plan_modal"
  get "/users/adjust_study_plan", to: "users#adjust_study_plan", as: :adjust_study_plan
  post "/users/customize_study_plan", to: "users#customize_study_plan", as: :customize_study_plan
  post "/users/set_guide_year", to: "users#set_guide_year", as: :set_guide_year
  patch "/users/update_settings", to: "users#update_settings", as: :update_settings
  patch "/users/toggle_sms_enabled", to: "users#toggle_sms_enabled", as: :toggle_sms_enabled

  post "/users/update_nps", to: "users#update_nps", as: :update_nps

  namespace :users do
    get :load_ga4_data
  end

  get "users/set_password", to: "users#set_password", as: :set_password
  post "users/update_password", to: "users#update_password", as: :update_password
  post "remove_expiring_soon_alert", to: "users#remove_expiring_soon_alert", as: :remove_expiring_soon_alert

  get "thank_you", to: "users#thank_you", as: :thank_you

  post "users/finish_tour", to: "users#finish_tour", as: :finish_tour
  post "users/toggle_study_plan_length", to: "users#toggle_study_plan_length", as: :toggle_study_plan_length

  post "/problem/:id/update", to: "problems#update", as: :problem_update
  get "/problem/:id", to: "problems#show", as: :problem_show
  get "/problems/:chapter_id", to: "problems#from_chapter", as: :problems_from_chapter

  get "must_knows/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, to: "must_knows#index", as: :must_knows

  post "topics/search", as: :topics_search

  post "/exports/download_active_review_pdf/:chapter_id", to: "exports#download_active_review_pdf", as: :download_active_review_pdf
  post "/exports/download_active_review_zip_all_chapters/:track_id", to: "exports#download_active_review_zip_all_chapters", as: :download_active_review_zip_all_chapters

  get "/subscribed", to: "subscriptions#subscribed", as: :subscribed
  get "/subscribe/:plan_code(/:email)", to: "subscriptions#checkout", as: :checkout, constraints: { email: /[^\/]+/ }
  get "/upgrade", to: "subscriptions#upgrade", as: :upgrade
  post "/subscribe/online_class/:online_class_id", to: "subscriptions_online_classes#subscribe_online_class_plan", as: :subscribe_online_class_plan
  post "/create_trial_user", to: "subscriptions#create_trial_user", as: :create_trial_user
  post "/update_trial_user", to: "subscriptions#update_trial_user", as: :update_trial_user
  post "/enable_free_trial", to: "subscriptions#enable_free_trial", as: :enable_free_trial

  match "/404", to: "content_pages#not_found", via: :all
  match "/500", to: "content_pages#error", via: :all

  resources :subscriptions, only: %i(new create) do
    collection do
      get :manage
      get :verify_coupon
      post :renew
      post :update_card
      post :remove_card
      post :cancel
    end
  end

  resources :study_plan_settings, only: %i(create update)
  resource :user_calendar, only: %i(update) do
    collection do
      get "progress"
      get "load_tasks_modal"
    end
  end

  resources :welcome

  resources :flash_card_decks, path: "flash_card_decks/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, only: %i(new index create)
  resources :flash_card_decks, only: %i(show destroy edit update)

  resources :flash_cards, only: %i(new create edit update destroy)

  resources :flash_card_sessions, only: %i(show create) do
    resources :flash_card_attempts, only: %i(show create update)
  end

  resources :evaluation_attempts, path: "evaluation_results", only: [:show] do
    member do
      get :ilp_by_chapter
    end
  end
  resources :exercise_attempts, only: %i(create update)
  resources :exam_scores, path: "scores", only: %i(create update destroy index new edit)
  resources :lesson_feedbacks, only: %i(create update)

  resources :failure_reasons, path: "error_tracker/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, only: %i(index show) do
    collection do
      post :clear_from_attempt
    end
  end

  resources :targeted_practices, only: [] do
    collection do
      get  :score_card
      post :review_questions
    end
  end

  resources :notes, only: %i(create update destroy)

  post "/homework_task_takeaways", to: "homework_task_takeaways#create", as: :create_homework_task_takeaways

  resources :leads, only: [:create] do
    get "unsubscribe/:encoded_email", on: :collection, action: :unsubscribe, as: :unsubscribe

    collection do
      post :manage_verification
    end

    member do
      post :send_confirmation_email
      post :confirm_email
    end
  end

  resources :problem_attempts, only: %i(update show)
  resources :questionnaire_attempts, only: %i(index show new create destroy)
  resources :questionnaire_categories, only: %i(show)

  resources :problem_submissions, only: %i(new create) do
    post :validate, on: :collection
  end

  resources :user_times, only: [:create]
  resources :messages, only: %i(index create destroy) do
    post :mark_as_read, on: :collection
  end
  resources :videos, only: %i(show index)

  get "online_classes", to: "online_classes#index"
  get "online_classes/:id/checkout", to: "online_classes#checkout", as: :online_class_checkout
  get "online_classes/confirm_registration", to: "online_classes#confirm"

  get "online_class_resources/library", to: "online_class_resources#library", as: :online_class_resources_library

  get "online_class_resources/cohort_video_repository", to: "online_class_resources#cohort_video_repository", as: :cohort_video_repository

  resources :flags, path: "bookmarks/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, only: [] do
    collection do
      get :chapter_list
      get :chapter, path: "chapters/:chapter_id"
      get :details, path: "chapters/:chapter_id/:flaggable_type/all", constraints: { flaggable_type: /#{FLAG_TYPES_WITH_NOTE.join('|')}/ }
      get :liveteach_bookmark_details, path: "instructors/:instructor_id/:flaggable_type/all", constraints: { flaggable_type: /#{FLAG_TYPES_WITH_NOTE.join('|')}/ }
      get :index, path: ":flaggable_type", constraints: { flaggable_type: /#{FLAG_TYPES.join('|')}/ }
      get :notes
    end
  end

  post "/flag/:type/:label/:id/flag", to: "flags#flag", as: :flag
  post "/flag/:type/:label/:id", to: "flags#toggle_flag", as: :toggle_flag
  delete "/flags/:type/:label/:id", to: "flags#destroy", as: :delete_flag

  resources :analytics, only: [:index] do
    collection do
      get :ontarget, path: "ontarget/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }
      get :chapter_details, path: "ontarget/chapters/:chapter_id"
      get :review_topic, path: "ontarget/review_topic/:topic_id"
      get :practice_by_topic, path: "ontarget/practice_by_topic/:topic_id"
      get :start_practice, path: "ontarget/start_practice/:topic_id"
    end
  end

  resources :evaluations, only: [] do
    collection do
      get :personalized, path: "personalized/:section", constraints: { section: /quant/ }
      post :create_personalized_test
    end
  end

  resources :word_definitions, path: "word_definitions/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, only: %i(index show) do
    collection do
      get :fetch_for_exercise
    end
  end

  resource :cart, only: %i(create show) do
    collection do
      get "adjust_ondemand/:action_type/:product_code", to: "carts#adjust_ondemand", as: :adjust_ondemand
    end
  end

  resources :ondemand_resources, path: "ondemand", only: [] do
    collection do
      get "repository/:section", constraints: { section: /(quant|verbal|di|ir|awa|bwa)/ }, to: "ondemand_resources#repository", as: :repository
    end
  end

  namespace "admin", path: "new_admin" do
    resource :impersonation
    resources :referrals, only: %i(index show)

    resources :topics, only: %i(index new edit create update destroy) do
      collection do
        get :feedback
        get :order
        post :update_order
        get :tracks
        post :save_tracks
        get :import
        post :upload
        post :save_lesson
      end

      member do
        get :feedback_details
      end
    end

    namespace :topics do
      resources :question_analytics, only: %i(index)
    end

    resources :menu, only: [:create]

    resources :chapters

    resource :dashboard, only: [:show] do
      get :sales
      get :users
      get :conversions
      get :conversion_rate
      get :revenue_per_user
      get :recurring_revenue
      get :non_recurring_revenue
      get :revenue_per_plan
      get :users_per_plan
      get :user_sections
      get :revenue_per_country
      get :adwords_performance
    end

    resources :examples do
      collection do
        get :analytics
        get :videos
      end
    end

    resources :problems do
      collection do
        get :analytics
        get :editable_list
        get :counts
        get :examples, to: "problems/examples#index"
        get "uploads/new/", to: "problems/uploads#new"
        post "uploads/preview/", to: "problems/uploads#preview"
        post "uploads/create/", to: "problems/uploads#create"
      end
    end

    resources :diagnostic_problems, only: %i(index destroy) do
      collection do
        get :chapters
        get :chapter_problems
        post "update_positions"
        post :add_problems
      end
    end

    namespace :problems do
      resources :examples, only: %i(show) do
        collection do
          get :chapters
          get :lessons
        end
      end
    end

    namespace :users do
      resources :duplicates, only: :index
    end

    resources :roles

    resources :sale_banners

    resources :users do
      get :cancellations, on: :collection

      member do
        get :feedbacks
        put :reset_account
      end
    end

    resources :leads, only: %i(index destroy) do
      collection do
        get :import
        post :preview
        post :upload
      end
    end

    resources :exercises, only: [] do
      post :toggle_type, on: :collection
    end

    resources :failure_reasons do
      get :update_select, on: :collection
    end

    resources :tracks

    resources :messages, only: %i(create) do
      collection do
        get :user_feedback
        get :reply

        resources :announcements, only: %i(index new create show), controller: "messages/announcements"
        resources :marketing_modals
      end
    end

    resources :lesson_feedbacks, only: [] do
      member do
        put :resolve
        put :unresolve
      end
    end

    resources :settings, only: %i(index) do
      collection do
        put :update
      end
    end

    resources :sections

    resources :en_translations, only: %i(index new create) do
      collection do
        get :edit
        put :update
        delete :destroy
      end
    end

    resources :study_plans

    resources :flash_cards

    resources :flash_card_decks

    resources :online_classes do
      resources :messages, only: %i(create show), controller: "online_classes/messages"

      collection do
        get :generate_class_automatically
        post ':online_class_session_id/add_class_recording', to: "online_classes#add_class_recording", as: :add_class_recording
      end

      member do
        get :download_class_participants
        delete :delete_participant
        post :add_user
        post :add_without_removing
        post :remove_from_other_class
      end
    end

    resources :ondemand do
      collection do
        post :add_instructor
        delete "destroy_instructor/:id", to: "ondemand#destroy_instructor", as: :destroy_instructor
        delete "destroy_office_hours", to: "ondemand#destroy_office_hours", as: :destroy_office_hours
      end
    end

    scope module: :online_classes, path: "homework", as: :homework do
      resources :problems, controller: "sessions/problems" do
        collection do
          patch :update_homework_status
          post :add_problems
          get :chapters
          get :chapter_test
          get :homework_task_type
          get :edit_homework_task
        end
      end
    end
    resources :instructors

    resources :videos do
      collection do
        resources :video_sections, controller: "videos/video_sections"
      end
    end

    resources :daily_questions do
      collection do
        get :chapters
        get :chapter_problems
        post :add_problems
        put :add_image_to_exercise
        post "update_positions"

        resources :contacts, only: %i(index destroy), controller: "daily_questions/contacts" do
          collection do
            get :download_registered_users
          end
        end
      end
    end

    resources :landing, only: :index
    resources :word_definitions

    root to: redirect("/new_admin/landing")
  end

  if Setting.for("ttp_api_enabled")
    devise_scope :user do
      root to: "sessions#new"
    end

    namespace "api" do
      namespace "v1" do
        resources :users, only: [:create] do
          collection do
            get "", to: "users#show"
            delete "", to: "users#destroy"
            post :authenticate, to: "users#authenticate_user"
            post :reset_password, to: "users#reset_password"
          end
        end
      end
    end
  else
    root to: "content_pages_external#index"
  end

  post "/api/v1/users/gmatclub/:api_key", to: "api/v1/users#gmatclub_co_registration"

  namespace "api" do
    namespace "v1" do
      resources :transactions, only: [] do
        collection do
          get :revenue
          get :daily_report_condensed
          get :yesterday_daily_report_condensed
        end
      end
    end
  end

  namespace "api" do
    namespace "v1" do
      resources :external_pages, only: [] do
        collection do
          post :request_tutoring_consultation
          post :request_liveteach_consultation
        end
      end
    end
  end

  constraints NotApiConstraint.new do
    if EXAM_NAME == "gmat"
      get "/mobile", to: "content_pages_external#mobile", as: :mobile
      get "/gmat-focus", to: "content_pages_external#gmat_focus", as: :gmat_focus
      get "/online_whiteboard", to: "content_pages#online_whiteboard", as: :online_whiteboard

      namespace "demo" do
        resources :lessons, param: :index, only: :show
      end
    end

    get "/liveteach", to: "content_pages_external#liveteach", as: :liveteach if %w(gmat gre ea).include?(EXAM_NAME)

    get "/ondemand-score-guarantee", to: "content_pages_external#ondemand_score_guarantee", as: :ondemand_score_guarantee if %w(gmat ea).include?(EXAM_NAME)

    get "/company", to: "content_pages_external#company", as: :company, constraints: { subdomain: "www" }
    get "/about", to: "content_pages_external#about", as: :about
    get "/score-guarantee", to: "content_pages_external#score_guarantee", as: :score_guarantee
    get "/tutoring", to: "content_pages_external#tutoring", as: :tutoring

    get "/plans", to: "content_pages#plans", as: :plans
    get "/ondemand", to: "content_pages#ondemand", as: :ondemand
    get "/ondemand_preview_video", to: "content_pages#ondemand_preview_video", as: :ondemand_preview_video
    get "/btg_plans", to: "content_pages#btg_plans", as: :btg_plans
    get "/gmatclub_plans", to: "content_pages#gmatclub_plans", as: :gmatclub_plans
    get "/marketplace", to: "content_pages#marketplace_plans", as: :marketplace_plans
    get "/greprepclub_plans", to: "content_pages#greprepclub_plans", as: :greprepclub_plans
    get "/contact", to: "content_pages#contact", as: :contact
    post "/contact", to: "content_pages#contact_send", as: :contact_send
    get "/confirm/email_sent", to: "content_pages#welcome_inactive_user", as: :welcome_inactive_user
    get "/riordan", to: "content_pages#rpaa", as: :rpaa
    get "/mlt", to: "content_pages#mlt", as: :mlt
    get "/forte", to: "content_pages#forte", as: :forte
    get "/terms", to: "content_pages#terms_and_conditions", as: :terms
    get "/privacy", to: "content_pages#privacy", as: :privacy
    get "/webinar", to: "content_pages#webinar", as: :webinar
    get "/score_chart", to: redirect("/gmat_focus_score_chart_and_calculator")
    get "/gmat_focus_score_chart_and_calculator", to: "content_pages#gmat_focus_score_chart_and_calculator", as: :gmat_focus_score_chart_and_calculator
    get "/berkeley", to: "content_pages#berkeley", as: :berkeley
    get "/ucberkeley", to: redirect("/berkeley")
    get "/free_practice_test", to: redirect("/free_diagnostic")

    get "/gmatclub_landing", to: redirect("/gmatclub_plans"), constraints: { subdomain: "gmat" }
    get "/verbal_beta_signup", to: redirect("/plans"), constraints: { subdomain: "gmat" }
    get "/mobile_landing", to: redirect("/"), constraints: { subdomain: "gmat" }
    get "/rpaa", to: redirect("/riordan")
    get "/admissions", to: "content_pages#admissions", as: :admissions
    post "/free_admission_consultation", to: "content_pages#free_admission_consultation", as: :free_admission_consultation

    if %w(gre).include?(EXAM_NAME)
      get "/giveaway", to: "content_pages_external#contest", as: :contest
    else
      get "/giveaway", to: "content_pages#giveaway", as: :giveaway
    end

    if %w(ea gmat).include?(EXAM_NAME)
      get "/testimonials", to: "content_pages_external#testimonials", as: :testimonials
    elsif EXAM_NAME != "sat"
      get "/testimonials", to: "content_pages#testimonials", as: :testimonials
    end

    # Page for A/B test variant in GRE
    get "/homepage", to: "content_pages_external#variant", as: :variant if %w(gre).include?(EXAM_NAME)

    resources :free_accounts, only: %i(new create)

    resources :guest_requests, only: %i(new create), path: "free_diagnostic"

    post "/free_diagnostic/validate", to: "guest_requests#validate", as: :validate_guest_request
    get "/free_diagnostic", to: "guest_requests#take_diagnostic", as: :take_diagnostic_guest_request
    post "/free_diagnostic/access_diagnostic_test", to: "guest_requests#access_diagnostic_test", as: :access_diagnostic_test_guest_request
    get "/free_diagnostic/start_free_trial", to: "guest_requests#start_free_trial", as: :start_free_trial_guest_request

    resources :daily_questions, param: :date, path: "question_of_the_day", only: [:show]
    get :question_of_the_day, to: "daily_questions#registration"
    get "/question_of_the_day/unsubscribe/:email", to: "daily_questions#unsubscribe", as: :unsubscribe_daily_question, param: :email, constraints: { email: /.*/ }

    namespace "daily_questions" do
      resources :contacts, only: %i(create)
    end

    namespace :online_class do
      resources :contacts, only: %i(create)
    end

    # This must be the last route
    get ":static_page", to: "content_pages#static_page", constraints: { format: :html }
  end
end
