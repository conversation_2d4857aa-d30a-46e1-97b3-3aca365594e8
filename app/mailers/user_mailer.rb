class UserMailer < ActionMailer::Base
  CONSOLIDATED_REPORTS_EMAILS = %w(<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>).freeze

  CONDENSED_REPORTS_TOTALS_EMAILS = %w(<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>).freeze

  DAILY_REVENUE_EMAILS = %w(<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>).freeze

  DAILY_ONDEMAND_EMAILS = %w(<EMAIL> <EMAIL> <EMAIL> <EMAIL>).freeze

  helper ViewHelper
  helper CountryHelper

  default from: "TTP Team <<EMAIL>>"

  def personal_mail_dev_logs(user, task_name, extra_attributes = nil)
    @user = user
    @extra_attributes = extra_attributes

    mail to: recipients_for(:personal_mail_dev_logs), subject: "Personal Mailer Logs - #{task_name} - #{ENV['APP_NAME']}"
  end

  def daily_revenue
    @start_date = Date.yesterday.beginning_of_month.beginning_of_day
    @end_date = Date.yesterday.end_of_day

    transactions = Transaction.for_course.where(closed_at: @start_date..@end_date)
    @total_revenue = transactions.sum(:total_amount_cents).to_f / 100
    @total_revenue_recurring = transactions.where(is_recurring: true).sum(:total_amount_cents).to_f / 100

    transactions = Transaction.for_course.where(closed_at: Date.yesterday.beginning_of_day..@end_date)
    @yesterday_revenue = transactions.sum(:total_amount_cents).to_f / 100
    @yesterday_revenue_recurring = transactions.where(is_recurring: true).sum(:total_amount_cents).to_f / 100

    transactions = Transaction.for_ondemand.where(closed_at: @start_date..@end_date)
    @total_ondemand = transactions.sum(:total_amount_cents).to_f / 100
    transactions = Transaction.for_ondemand.where(closed_at: Date.yesterday.beginning_of_day..@end_date)
    @yesterday_ondemand = transactions.sum(:total_amount_cents).to_f / 100

    transactions = Transaction.for_tutoring.where(closed_at: @start_date..@end_date)
    @total_tutoring = transactions.sum(:total_amount_cents).to_f / 100
    transactions = Transaction.for_tutoring.where(closed_at: Date.yesterday.beginning_of_day..@end_date)
    @yesterday_tutoring = transactions.sum(:total_amount_cents).to_f / 100

    transactions = Transaction.for_online_class.where(closed_at: @start_date..@end_date)
    @total_online_class = transactions.sum(:total_amount_cents).to_f / 100
    transactions = Transaction.for_online_class.where(closed_at: Date.yesterday.beginning_of_day..@end_date)
    @yesterday_online_class = transactions.sum(:total_amount_cents).to_f / 100

    mail to: recipients_for(:daily_revenue), subject: "Daily Revenue Report - #{Date.yesterday.strftime('%-m/%-d/%Y')}"
  end

  def daily_ondemand_report
    @start_date = Date.yesterday.beginning_of_month.beginning_of_day
    @end_date = Date.yesterday.end_of_day

    @ondemand_transactions = Transaction.joins(:user).for_ondemand.where.not("users.recurly_account_code ilike ?", "%@targettestprep.com")

    @mtd_ondemand_transactions = @ondemand_transactions.where(transactions: { closed_at: @start_date..@end_date })
    @total_ondemand = @mtd_ondemand_transactions.sum(:total_amount_cents).to_f / 100

    @yesterday_ondemand_transactions = @ondemand_transactions.where(transactions: { closed_at: Date.yesterday.beginning_of_day..@end_date })
    @yesterday_ondemand = @yesterday_ondemand_transactions.sum(:total_amount_cents).to_f / 100

    mail to: recipients_for(:daily_ondemand_report), subject: "Daily OnDemand Report (#{EXAM_NAME.upcase}) - #{Date.yesterday.strftime('%-m/%-d/%Y')}"
  end

  def consolidated_daily_report
    return unless EXAM_NAME == "gmat"

    report_service = TransactionReportService.new(consolidated_report: true)
    report_service.call

    @report = report_service.report

    mail to: recipients_for(:consolidated_daily_report), subject: "Consolidated Daily Revenue Report - #{Date.yesterday.strftime('%-m/%-d/%Y')}"
  end

  def daily_report_condensed
    return unless EXAM_NAME == "gmat"

    report_service = ::ReportServices::YesterdayCondensedReport.new(consolidated_report: true)
    @report = report_service.call

    mail to: recipients_for(:daily_report_condensed), subject: "Rev - #{Date.yesterday.strftime('%-m/%-d/%Y')}"
  end

  def daily_report_condensed_totals
    return unless EXAM_NAME == "gmat"

    report_service = RevenueReportService.new(consolidated_report: true)

    @report = report_service.call

    mail to: recipients_for(:daily_report_condensed_totals), subject: "Rev (totals) - #{Date.yesterday.strftime('%-m/%-d/%Y')}"
  end

  def new_users_report
    @start_date = Date.yesterday.beginning_of_month
    @end_date = Date.yesterday.end_of_day

    users = User.where(created_at: @start_date..@end_date)
    @total_registered = users.verified_only.count
    @total_diagnostic = users.where(guest: true).count

    users = User.where(created_at: Date.yesterday.beginning_of_day..@end_date)
    @yesterday_registered = users.verified_only.count
    @yesterday_diagnostic = users.where(guest: true).count

    mail to: recipients_for(:new_users_report), subject: "Daily New Users Report - #{Date.yesterday.strftime('%-m/%-d/%Y')}"
  end

  def user_report_india(file_name)
    attachments[file_name] = File.read(file_name)

    mail to: recipients_for(:user_report_india), subject: "User Report India - #{Date.yesterday.strftime('%-m/%-d/%Y')}"
  end

  def diagnostic_result(user, attempt_id)
    @user = user
    @attempt_id = attempt_id
    mail to: user.email, subject: t("user_mailer.diagnostic_result.subject")
  end

  def resume_diagnostic(user, attempt_id)
    @user = user
    @attempt_id = attempt_id
    mail to: user.email, subject: t("user_mailer.resume_diagnostic.subject")
  end

  def guest_sign_up(user)
    @user = user
    mail to: user.email, bcc: recipients_for(:guest_sign_up), subject: t("user_mailer.guest_sign_up.subject", exam_name: (EXAM_NAME || "gmat").upcase)
  end

  def expired_trial(user)
    @user = user
    mail to: user.email, bcc: recipients_for(:expired_trial), subject: t("user_mailer.expired_trial.subject", exam_name: (EXAM_NAME || "gmat").upcase)
  end

  def intercom_trial_email_day_4(user)
    @user = user
    mail to: user.email, subject: "Your Personalized Feedback"
  end

  def gmatclub_co_registration(email)
    @email = email
    @encoded_email = Base64.urlsafe_encode64(email)
    mail to: email, bcc: ["<EMAIL>"], subject: t("user_mailer.gmatclub_co_registration.subject")
  end

  def problem_submission(problem_submission)
    @problem_submission = problem_submission

    problem_submission.screenshots.each do |screenshot|
      attachments[screenshot.original_filename] = File.read(screenshot.path)
    end

    mail(
      to: recipients_for(:problem_submission),
      from: "<EMAIL>",
      subject: t(
        "problem_submission.mail.subject",
        section: @problem_submission.section.capitalize
      )
    )
  end

  def free_admissions_consultation(params)
    @attributes = params
    file_format = @attributes[:resume].original_filename.split(".").last
    attachment_title = "Resume_#{@attributes[:name]}_#{@attributes[:last_name]}.#{file_format}"

    attachments[attachment_title] = File.read(@attributes[:resume].tempfile)

    if qa_email_enabled?
      mail to: qa_email_recipients, subject: "Free admissions consultation"
    else
      mail to: recipients_for(:free_admissions_consultation), bcc: recipients_for(:free_admissions_consultation_bcc), subject: "Free admissions consultation"
    end
  end

  private

  def qa_email_enabled?
    Setting.for(:qa_email_enabled)
  end

  def qa_email_recipients
    Setting.for("qa_email_recipients").to_s.split(",").map(&:strip)
  end

  def recipients_for(email)
    return qa_email_recipients if qa_email_enabled? and qa_email_recipients.present?

    case email
    when :personal_mail_dev_logs
      %w(<EMAIL>)
    when :daily_revenue
      emails = DAILY_REVENUE_EMAILS.dup
      emails << "<EMAIL>" if EXAM_NAME == "sat"
      emails
    when :daily_ondemand_report
      DAILY_ONDEMAND_EMAILS
    when :consolidated_daily_report
      CONSOLIDATED_REPORTS_EMAILS
    when :daily_report_condensed
      %w(<EMAIL> <EMAIL>).freeze
    when :daily_report_condensed_totals
      CONDENSED_REPORTS_TOTALS_EMAILS
    when :new_users_report
      emails = %w(<EMAIL> <EMAIL> <EMAIL>)
      emails << "<EMAIL>" if EXAM_NAME == "sat"
      emails
    when :user_report_india
      %w(<EMAIL> <EMAIL>)
    when :guest_sign_up
      %w(<EMAIL> <EMAIL> <EMAIL> <EMAIL>)
    when :expired_trial
      %w(<EMAIL> <EMAIL>)
    when :problem_submission
      %w(<EMAIL> <EMAIL>)
    when :free_admissions_consultation_bcc
      %w(<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>)
    when :free_admissions_consultation
      %w(<EMAIL>)
    else
      []
    end
  end
end
