class Flag < ApplicationRecord
  include Exportable

  TYPES = %w(topics examples problems notes must_knows chapters).freeze
  EXPORT_ATTRIBUTES = %i(label flaggable_id flaggable_type).freeze
  LABELS = %w(
    completed
    review
    approved
    resume_diagnostic_notification_sent
    marked
    not_following_study_plan_acknowledged
    must_knows_modal_shown
    notes_disabled
    next_chapter_test_tour_disabled
    verbal_beta_modal_displayed
    new_quant_evaluations_acknowledged
    new_critical_reasoning_evaluations_acknowledged
    new_sentence_correction_evaluations_acknowledged
    new_reading_comprehension_evaluations_acknowledged
    new_text_completion_evaluations_acknowledged
    new_sentence_equivalence_evaluations_acknowledged
    new_chapter_acknowledged
    subscription_expiring_soon_noticed
    not_using_feedback_alert_showed
    show_additional_section_upgrade_acknowledge
    skipped_personalized_evaluation
    concept_masteries_acknowledged
    gre_verbal_banner_acknowledged
    setup_study_plan_settings_acknowledged
    want_more_tasks_for_day_modal_acknowledged
    flashcard_adapt_algorithm_tooltip_acknowledged
    year_end_sale_acknowledged
    integrated_reasoning_modal_acknowledged
    signup_day_1_modal_acknowledged
    signup_day_2_modal_acknowledged
    signup_day_3_modal_acknowledged
    signup_day_4_modal_acknowledged
    signup_day_5_modal_acknowledged
    tutoring_modal_acknowledged
    flip_card_acknowledged
    marketing_modal_acknowledged
    coordinate_geometry_acknowledged
    ondemand_congratulations_modal_acknowledged
    ondemand_read_chapter_or_watch_video_acknowledged
    ondemand_tour_completed
    first_visit_for_chat_ai_acknowledged
    first_visit_for_chat_ai_modal_acknowledged
    first_visit_for_ai_learning_disclaimer_modal
    tutoring_modal_for_14_days
    ondemand_sign_up_email_sent
    cohort_video_completed
    calendar_study_plan_extends_banner
    gmat_focus_edition_update_banner
  ).freeze

  SUMMARY_LIMITS = { topic: 10, problem: 5, example: 5, note: 3, must_know: 3, concept_mastery: 5 }.freeze

  belongs_to :user
  belongs_to :flaggable, polymorphic: true

  validates :user, presence: true
  validates :flaggable, presence: true
  validates :label, inclusion: { in: LABELS }

  after_create :update_study_plan_item
  after_destroy :update_study_plan_item

  scope :by_user_type_and_label, (lambda do |user, type, label|
    by_label(label).where(flaggable_type: type, user_id: user.id)
  end)

  scope :by_label, (lambda do |label|
    where(label: label)
  end)

  scope :by_lesson, (lambda do |user, lesson|
    where(flaggable_id: lesson.id, flaggable_type: "Lesson", user_id: user.id)
  end)

  scope :by_exercise, (lambda do |user|
    where(flaggable_type: "Exercise", user_id: user.id)
  end)

  scope :by_must_know, (lambda do |user|
    where(flaggable_type: "MustKnow", user_id: user.id)
  end)

  scope :by_online_class_session, (lambda do |user|
    where(flaggable_type: "OnlineClassSession", user_id: user.id, label: "review")
  end)

  def self.toggle(user, flaggable, label)
    was_flagged = Flag.flagged?(user, flaggable, label)

    if was_flagged
      flags = Flag.where user_id: user.id, flaggable: flaggable, label: label.to_s
      flags.destroy_all
    else
      Flag.create(user_id: user.id, flaggable: flaggable, label: label.to_s)
    end

    !was_flagged
  end

  def self.flag(user, flaggable, label, force = false)
    return unless force or !Flag.flagged?(user, flaggable, label)

    Flag.create(user_id: user.id, flaggable: flaggable, label: label.to_s)
  end

  def self.unflag(user, flaggable, label)
    return unless user.present?

    flags = Flag.where user_id: user.id, flaggable: flaggable, label: label.to_s
    flags.destroy_all
  end

  def self.flagged?(user, flaggable, label)
    return false if user.blank?

    Flag.where(user_id: user.id, flaggable: flaggable, label: label.to_s).exists?
  end

  def self.flagged_after?(user, flaggable, label, start_date)
    Flag.where(user_id: user.id, flaggable: flaggable, label: label.to_s, created_at: start_date..Time.zone.now).exists?
  end

  def update_study_plan_item
    user.mission_study_plan_completion.invalidate! if flaggable_type == "StudyModuleItem" or (flaggable_type == "Lesson" and label == "completed")
  end
end
