# rubocop:disable Layout/LineLength

class UserCalendarTask < ApplicationRecord
  belongs_to :user_calendar
  belongs_to :study_module_item

  validates :user_calendar, :study_module_item, presence: true

  default_scope { order("id") }

  scope :review_quizzes, -> { includes(:study_module_item).where(study_module_items: { item_type: "review_quiz" }) }

  scope :past_incomplete, ->(user_calendar) { where("user_calendar_tasks.user_calendar_id = ? AND user_calendar_tasks.is_skipped = false AND user_calendar_tasks.is_completed = false", user_calendar.id).where("(user_calendar_tasks.schedule_date < ? AND user_calendar_tasks.rescheduled_date IS NULL) OR user_calendar_tasks.rescheduled_date < ?", Date.current, Date.current) }

  scope :future_incomplete, ->(user_calendar) { where("user_calendar_tasks.user_calendar_id = ? AND user_calendar_tasks.is_skipped = false AND user_calendar_tasks.is_completed = false", user_calendar.id).where("(user_calendar_tasks.schedule_date > ? AND user_calendar_tasks.rescheduled_date IS NULL) OR user_calendar_tasks.rescheduled_date > ?", Date.current, Date.current) }

  def completed_by_user?(user)
    completion_for(user)[:percentage] == 100
  end

  def progress_by_user?(user)
    return false if completed_by_user?(user)

    (completion_for(user)[:percentage]).positive?
  end

  def accuracy_for(user)
    completion_for(user)[:accuracy]
  end

  def completion_for(user)
    raise StandardError unless user.calendar_study_plan_completion.data[id]

    user.calendar_study_plan_completion.data[id]
  rescue StandardError
    user.calendar_study_plan_completion.regenerate_data[id]
  end

  def next_topic_for_reading(user, chapter)
    topics = chapter.topics.in_track(user)[topic_from..(topic_to || -1)]

    last_access = LessonAccess.where(user_id: user.id, chapter_id: chapter.id, lesson_id: topics.map(&:id)).last
    last_access.present? ? last_access.lesson : topics.first
  end

  def review_quiz_number
    user_calendar.user_calendar_tasks.review_quizzes.order("user_calendar_tasks.id ASC").ids.index(id) + 1
  end

  def exam_score_for(user)
    if (exam_score = user.exam_scores_model.find_or_initialize_by(study_module_item: study_module_item)) and exam_score.persisted?
      exam_score.update(user_calendar_task: self)
    end

    user.exam_scores_model.find_or_initialize_by(user_calendar_task: self, study_module_item: study_module_item)
  end

  def state
    if is_completed?
      "completed"
    elsif rescheduled_date?
      "rescheduled"
    elsif is_skipped?
      "skipped"
    elsif study_module_item.guide?
      "optional-task"
    end
  end
end

# rubocop:enable Layout/LineLength
