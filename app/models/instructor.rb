class Instructor < ApplicationRecord
  mount_uploader :image, InstructorPictureUploader
  mount_uploader :large_image, InstructorPictureUploader
  mount_uploader :medium_image, InstructorPictureUploader

  has_many :online_classes
  has_many :ondemand_weekly_hour_instructors
  has_many :homework_task_comments, dependent: :destroy

  has_many :exercises, dependent: :nullify

  validates :name, presence: true
  validates :title, presence: true
  validates :default_instructor, uniqueness: { message: "A default instructor is already selected" }, if: :default_instructor?
  validates :email, uniqueness: true, allow_blank: true, allow_nil: true, format: { with: EMAIL_REGEX }

  def self.default_instructor
    find_by(default_instructor: true)
  end

  def class_videos_available
    OnlineClassSession.with_recording.where(online_class_id: online_classes.ids).count
  end
end
