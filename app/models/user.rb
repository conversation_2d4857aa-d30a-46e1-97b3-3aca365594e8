# rubocop:disable Naming/PredicateName

class User < ApplicationRecord
  include PgSearch::Model
  include UserScopes
  include IntercomUtilities
  include AdminPermissions

  pg_search_scope :search_by_names_and_email, against: %i(first_name last_name email id recurly_account_code), using: { tsearch: { prefix: true } }

  REFERRED_BY_OPTIONS = Setting.for("referred_by_options").split(",")
  PROGRAM_APPLYING_FOR_OPTIONS = Setting.for("program_applying_for_options").split("||")
  SCORES_FORMAT = ExamScore::SCORES_FORMAT

  rolify

  serialize :exam_scores, type: Array
  serialize :active_sections, type: Array
  serialize :session_data

  before_create :set_default_role, unless: :guest?

  before_save { email.downcase! }
  before_save :fix_active_sections

  before_create :skip_confirmation!
  after_create :create_default_bookmarks, unless: :guest?
  after_create :check_guest_request
  after_create :delete_lead
  after_save :enqueue_mission_study_plan_data_regeneration, if: -> { saved_change_to_attribute?(:track_id) || saved_change_to_attribute?(:active_sections) }
  after_save :enqueue_user_calendar_generation, if: -> { user_calendar.present? and (saved_change_to_attribute?(:track_id) or saved_change_to_attribute?(:active_sections) or saved_change_to_attribute?(:short_study_plan) or saved_change_to_attribute?(:show_og_questions)) }
  after_save :update_transactions_and_billing_info, if: -> { saved_change_to_attribute?(:current_plan) or saved_change_to_attribute?(:subscription_ends_at) }
  after_save :process_user_reward, if: -> { saved_change_to_attribute?(:current_plan) and referrer.present? and !referrer.was_reward_granted? }
  after_save :generate_recurly_coupons
  after_save -> { delay.update_intercom_sms_subscriptions }, if: -> { saved_change_to_attribute?(:sms_enabled) }
  after_save -> { delay.send_ondemand_signup_email }, if: -> { saved_change_to_attribute?(:ondemand_enabled) and ondemand_enabled? }

  # after_save :send_trial_email, if: :current_plan_changed? # Disable free consultation email
  # after_create :send_guest_welcome_email, if: :guest?

  # Include default devise modules. Others available are:
  # :confirmable, :registerable, :lockable, :timeoutable and :omniauthable
  devise :authy_authenticatable, :database_authenticatable, :recoverable, :rememberable, :trackable, :validatable, :lockable, :confirmable

  belongs_to :cancel_reason
  belongs_to :track

  has_one :user_analysis_data, dependent: :destroy
  has_one :billing_info, dependent: :destroy
  has_one :credit_card_detail, dependent: :destroy
  has_one :authy_account, autosave: :admin?, dependent: :destroy
  has_one :full_contact_detail, dependent: :destroy
  has_one :study_plan_setting, dependent: :destroy
  has_one :user_calendar, dependent: :destroy
  has_many :user_calendar_tasks, through: :user_calendar
  has_many :online_classes_users
  has_many :online_classes, through: :online_classes_users
  has_many :flags, dependent: :destroy
  has_many :lesson_accesses, dependent: :destroy
  has_many :evaluation_attempts, dependent: :destroy
  has_many :problem_attempts, through: :evaluation_attempts
  has_many :exercise_attempts, dependent: :destroy
  has_many :ai_assist_exercise_attempts, class_name: "AiAssist::ExerciseAttempt", dependent: :destroy
  has_many :evaluations, class_name: "UserEvaluation", dependent: :destroy
  has_many :ai_assist_summaries, class_name: "AiAssist::Summary", dependent: :destroy
  has_many :adaptive_evaluations, dependent: :destroy
  has_many :chapter_evaluations, -> { ordered_by_number_and_levels }, dependent: :destroy
  has_many :flash_card_sessions, dependent: :destroy
  has_many :flash_card_attempts, dependent: :destroy
  has_many :flash_card_masteries, dependent: :destroy
  has_many :personalized_evaluations, dependent: :destroy
  has_many :user_stats, dependent: :destroy
  has_many :notes, dependent: :destroy
  has_many :homework_task_takeaways, dependent: :destroy
  has_many :exam_scores_model, class_name: "ExamScore"
  has_many :possible_duplicates, dependent: :destroy
  has_many :lesson_feedbacks, dependent: :destroy
  has_many :user_diagnostics, dependent: :destroy
  has_many :user_times, dependent: :destroy
  has_many :transactions, dependent: :destroy
  has_many :notification_statuses, dependent: :destroy
  has_many :test_dates, dependent: :destroy, inverse_of: :user
  has_many :sent_messages, class_name: "Message", foreign_key: "sender_id"
  has_many :received_messages, class_name: "Message", foreign_key: "recipient_id"
  has_many :flash_cards, dependent: :destroy
  has_many :data, dependent: :destroy, class_name: "User::Data"
  has_many :referrals, dependent: :destroy, class_name: "User::Referral", foreign_key: "referrer_user_id"
  has_one :referrer, dependent: :destroy, class_name: "User::Referral", foreign_key: "referral_user_id"
  has_many :chat_messages, class_name: "AiAssist::ChatMessage", dependent: :destroy
  has_many :ai_assist_exercises, class_name: "AiAssist::Exercise", dependent: :destroy
  attr_accessor :from_admin, :validation_scenario, :email_confirmation

  validates_confirmation_of :password
  validates :password, length: { minimum: 8 }, allow_blank: true, on: :update
  validates :nps, numericality: { greater_than_or_equal_to: 1, less_than_or_equal_to: 10 }, allow_blank: true
  validates :email, uniqueness: true
  validates :email, confirmation: true
  validates :first_name, presence: true, if: -> { first_name_changed? and first_name_was.present? and !from_admin }
  validates :track, presence: true, if: -> { validation_scenario == "welcome_wizard_setup_account" }
  validates :referred_by, presence: true, if: -> { validation_scenario == "welcome_wizard_setup_account" }
  validates :time_zone, presence: true
  validates :phone, numericality: { only_integer: true, allow_blank: true }, length: { minimum: 10, allow_blank: true, message: I18n.t("user.errors.phone.ten_digits_at_least_msg") }
  validates :phone, presence: true, if: -> { sms_enabled? }
  validate :validate_email_format, if: -> { email.present? }
  validates :failed_attempts, presence: true

  with_options if: :from_admin do |user|
    user.validates :email, presence: true
    user.validates :password, presence: true, on: :create
    user.validates :password, length: { minimum: 8, allow_blank: true }
  end

  accepts_nested_attributes_for :test_dates, allow_destroy: true, reject_if: proc { |att| att["next_exam_date"].blank? }
  accepts_nested_attributes_for :study_plan_setting

  scope :verified_only, -> {
    where(guest: false)
      .where.not(confirmed_at: nil)
      .where.not("(signed_up_for_free_account = ? AND current_plan IS NULL AND previous_plan IS NULL)", true)
  }

  delegate :study_plan, to: :track
  delegate :for_legacy_course, to: :track, allow_nil: true

  delegate \
    :current_period_ends_at,
    :active?,
    :trial_subscription?,
    :current_subscription,
    :future_subscription,
    :recurly_email,
    :has_plan?,
    :recurly_login_token,
    :recurly_first_name,
    :recurly_last_name,
    :recurly_current_plan,
    :recurly_current_plan_cost,
    :recurly_transactions_total_value_cents,
    :recurly_current_subscription_total_price,
    :recurly_future_subscription_total_price,
    :available_coupon_for,
    :calculate_discount,
    :invoices,
    :last_transaction,
    :coupons_redeemed,
    :onlineclass_subscription?,
    to: :recurly_proxy

  delegate \
    :authy_id,
    :authy_id=,
    :last_sign_in_with_authy,
    :last_sign_in_with_authy=,
    :authy_enabled,
    :authy_enabled=,
    to: :authy_account,
    allow_nil: true

  delegate :referrer_user, to: :referrer, allow_nil: true

  delegate \
    :add_intercom_subscription,
    :delete_intercom_subscription,
    to: :intercom_proxy

  after_initialize do
    authy_account || build_authy_account if admin_panel_access?
  end

  after_initialize do
    self.guide_year = "#{Date.current.year - 1}-#{Date.current.year}" if guide_year.blank?
  end

  def study_plan_setting
    super || build_study_plan_setting
  end

  def send_reset_password_instructions
    return false if free_diagnostic_user?

    super
  end

  def validate_email_format
    return if email.include?("@targettestprep.com")

    errors.add(:email, I18n.t("user.errors.email.should_not_contain_plus_symbol")) if email.include?("+")
  end

  def first_name
    format_name_component(read_attribute(:first_name))
  end

  def name
    format_name_component(read_attribute(:name))
  end

  def last_name
    format_name_component(read_attribute(:last_name))
  end

  def full_name
    "#{first_name} #{last_name}"
  end

  def create_default_bookmarks
    lesson = Lesson.find_by_full_number("1.1") || Lesson.first
    problem = Problem.find_by_id(4133) || Problem.first
    Flag.toggle(self, lesson, "review") if lesson
    Flag.toggle(self, problem, "review") if problem
  end

  def enqueue_mission_study_plan_data_regeneration
    @mission_study_plan_completion = nil
    user_stats.delete_all

    mission_study_plan_completion.delay.regenerate_data
  end

  def enqueue_user_calendar_generation
    @calendar_study_plan_completion = nil

    user_calendar.generate_calendar_tasks if calendar_study_plan_enabled?
  end

  def update_transactions_and_billing_info
    billing_info ? billing_info.delay.update_fields : create_billing_info
    delay.update_columns(transactions_total_value_cents: recurly_transactions_total_value_cents)
  end

  def check_guest_request
    return unless (guest_request = GuestRequest.find_by_email(email))

    guest_request.fetch_guest_information self
  end

  def send_guest_welcome_email
    UserMailer.guest_sign_up(self).deliver_now
  end

  def exam_taken
    exam_scores_model.any? ? exam_scores_model.last.score : nil
  end

  def analysis_data
    self.user_analysis_data = UserAnalysisData.create(user: self) if user_analysis_data.nil?

    user_analysis_data
  end

  def update_subscription_data
    return if id.nil?
    return if subscription_free?

    @recurly_proxy = nil # Reinitialize RecurlyProxy to fech latest data.

    attributes = { current_plan: recurly_current_plan, on_trial: (trial_subscription? && future_subscription.blank?) }

    if access_online_class?
      attributes[:current_plan] = RecurlyProxy::ONLINE_CLASS_PLAN_CODE
      attributes[:subscription_ends_at] = OnlineClass.current_class(self).access_ends_at(user: self)
    end

    attributes.merge!(previous_plan: current_plan) if attributes[:current_plan] != current_plan and current_plan.present?

    if active? and attributes[:subscription_ends_at].blank?
      attributes.merge!(subscription_ends_at: current_period_ends_at)
    elsif subscription_ends_at.present? and subscription_ends_at > Time.zone.now
      attributes.merge!(subscription_ends_at: 1.day.ago)
    end

    assign_attributes(attributes)

    self.full_ondemand_access = access_ondemand?
    self.ondemand_enabled = true if full_ondemand_access

    save
  end

  def delete_account
    recurly_proxy.remove_billing_info
    recurly_proxy.terminate_current_subscription
    recurly_proxy.cancel_future_subscription

    billing_info&.destroy
    online_classes.destroy_all

    update_deleted_account_attributes
  end

  def valid_membership?
    return true if available_current_onlineclass?

    if subscription_ends_at.blank? or (subscription_ends_at < Time.zone.now)
      return false unless subscription_free? || active?

      update_subscription_data
    end

    true
  end

  def subscription_free?
    free_emails = Setting.for("subscription_free_emails") || []

    free_emails.split("|").include?(email) || admin_panel_access?
  end

  def subscription_onlineclass?
    @subscription_onlineclass ||= current_plan.blank? && onlineclass_subscription?
  end

  def ondemand_subscription?
    current_plan.in?(RecurlyProxy::ONDEMAND_PLAN_CODES)
  end

  def access_ondemand?
    ondemand_subscription? || access_online_class? || subscription_free?
  end

  # Devise
  def active_for_authentication?
    super && !(email.include? "-unsubscribed") && !free_diagnostic_user?
  end

  def attempt_set_password(params)
    data = {}
    data[:password]              = params[:password]
    data[:password_confirmation] = params[:password_confirmation]
    update(data)
  end

  def has_no_password?
    encrypted_password.blank?
  end

  def password_required?
    # Password is required if it is being set, but not for new records
    if persisted?
      !password.nil? || !password_confirmation.nil?
    else
      false
    end
  end

  def study_plan_completion
    calendar_study_plan_enabled? ? calendar_study_plan_completion : mission_study_plan_completion
  end

  def mission_study_plan_completion
    @mission_study_plan_completion ||= MissionStudyPlanCompletion.new(self)
  end

  def calendar_study_plan_completion
    @calendar_study_plan_completion ||= CalendarStudyPlanCompletion.new(self)
  end

  def calendar_study_plan_enabled?
    study_plan_setting.calendar_view?
  end

  def mission_study_plan_enabled?
    study_plan_setting.mission_view?
  end

  def calendar_month_view_type?
    study_plan_setting.calendar_month_view?
  end

  def calendar_week_view_type?
    study_plan_setting.calendar_week_view?
  end

  # Ideally we could fetch these values asyncrhonously, with $.getScript()
  def course_completion
    User::Data.get_stat(user: self, key: "course_completion", allow_outdated: false).to_i
  end

  def diagnostics_for_current_course
    user_diagnostics.joins(:track).where(tracks: { for_legacy_course: for_legacy_course })
  end

  def diagnostic
    diagnostics = diagnostics_for_current_course

    diagnostics.find_by(track: track)&.diagnostic || diagnostics.first&.diagnostic || track&.diagnostic
  end

  def diagnostic_attempt
    return nil if diagnostic.blank?

    evaluation_attempts.where(evaluation: diagnostic).order("created_at DESC").first
  end

  def welcome_finished?
    welcome_showed and track.present? and referred_by.present?
  end

  def welcome_incomplete_step
    if welcome_showed == false
      "greeting"
    else
      "setup_account"
    end
  end

  def percentage_problems_in_track_attempted
    problems = Problem.in_track(self).not_for_personalized_evaluation.exclude_group_parents.count

    if problems.positive?
      (problems_in_track_attempted_count.to_f / problems * 100).round
    else
      0
    end
  end

  def problems_in_track_attempted_count(section = nil)
    ProblemAttempt.last_finished_for_problems_and_user(
      Problem.in_track(self, section).not_for_personalized_evaluation.exclude_group_parents.pluck(:id), self
    ).count
  end

  def problems_in_track_trained_count(section = nil)
    ProblemAttempt.last_finished_for_problems_and_user(
      Problem.in_track(self, section).not_for_personalized_evaluation.exclude_group_parents.pluck(:id), self
    ).where.not(failure_reason_id: nil).count
  end

  def problems_in_track_attempted_by_question_type_count(question_type, section = nil)
    problem_ids = Problem.in_track(self, section)
      .not_for_personalized_evaluation
      .exclude_group_parents
      .where(question_type: question_type)
      .pluck(:id)

    ProblemAttempt.last_finished_for_problems_and_user(problem_ids, self).count
  end

  def create_intercom_event(event)
    intercom.events.create event.merge!(user_id: app_id, created_at: Time.now.to_i) if Rails.env.production?
  rescue Intercom::ResourceNotFound
    intercom.users.create(user_id: app_id, email: email)
    intercom.events.create event.merge!(user_id: app_id, created_at: Time.now.to_i)
  rescue Intercom::BlockedUserError
    # Do nothing
  end

  def was_trial?
    RecurlyProxy::TRIAL_PLAN_CODES.include?(previous_plan)
  end

  def app_id
    if Setting.for("ttp_api_enabled")
      "#{EXAM_NAME || 'gmat'}-trial-#{id}"
    else
      "#{EXAM_NAME || 'gmat'}-#{id}"
    end
  end

  def generate_second_commission_in_tapfiliate(sale_value)
    return unless tapfiliate_conversion_id

    values = [{ sub_amount: sale_value, commission_type: "Second Commission" }].to_json
    headers = {
      content_type: "application/json",
      api_key: Setting.for("tapfiliate_api_key")
    }
    RestClient.post "https://api.tapfiliate.com/1.4/conversions/#{tapfiliate_conversion_id.to_i}/commissions/", values, headers
  end

  def generate_first_commission_in_shareasale?
    current_plan.present? && shareasale_trial_order_number.blank?
  end

  def generate_second_commission_in_shareasale?
    return false if shareasale_trial_order_number.blank?
    return false if shareasale_trial_order_number == "commissions-generated"

    current_plan.present? && !RecurlyProxy::TRIAL_PLAN_CODES.include?(current_plan)
  end

  def update_personal_data(guest_user = false)
    self.email = recurly_email
    self.first_name = recurly_first_name
    self.last_name = recurly_last_name

    return unless guest_user || guest?

    self.guest = false
    set_default_role
    create_default_bookmarks
  end

  def average_time_per_question
    times = problem_attempts.finished.pluck(:total_elapsed_seconds)

    if times.length.positive?
      (times.reduce(:+).to_f / times.length).round
    else
      0
    end
  end

  def average_time_per_first_question
    DashboardAnalytics.avg_time_per_first_question_for(self)
  end

  def percentage_careless_mistakes
    if (failure_reason = FailureReason.careless_mistake)
      if problem_attempts.count.positive?
        (problem_attempts.where(failure_reason: failure_reason).count.fdiv(problem_attempts.count) * 100).round
      else
        0
      end
    else
      "N/A"
    end
  end

  def editor?
    @is_editor ||= (roles.pluck(:name) & %w(editor admin)).present?
  end

  def admin?
    @is_admin ||= has_role?(:admin)
  end

  def free_account_access?
    return false if subscription_free?

    !(recurly_account_code.present? && valid_membership?)
  end

  def trial_available?
    free_account_access? && [nil, "free_diagnostic"].include?(current_plan) && [nil, "free_diagnostic"].include?(previous_plan)
  end

  def first_time_free_account?
    signed_up_for_free_account? && trial_available?
  end

  def problem_editor?
    @is_problem_editor ||= has_role?(:problem_editor)
  end

  def free_diagnostic_user?
    guest? and current_plan == "free_diagnostic"
  end

  def has_old_notes?
    @has_old_notes ||= notes.old.any?
  end

  def notes_disabled?
    @notes_disabled ||= flags.where(flaggable: self, label: "notes_disabled").exists?
  end

  def about_to_expire?
    return false # Temporarily disable feature
    return false unless subscription_ends_at
    return false unless current_plan&.include?("resubscribe")
    return false unless billing_info&.complete?

    subscription_ends_at < 1.week.from_now && future_subscription.nil?
  end

  def previous_noteable_path(noteable, options = {})
    current_index = noteables.find_index(noteable)

    Note.noteable_path(noteables[current_index - 1], options) if current_index.positive?
  end

  def next_noteable_path(noteable, options = {})
    current_index = noteables.find_index(noteable)

    Note.noteable_path(noteables[current_index + 1], options) if current_index < noteables.length - 1
  end

  def noteables
    @noteables ||= notes.sort_by(&:position).map(&:noteable).uniq
  end

  def export_data
    {
      intercom_id: intercom_id,
      user_attributes: {
        encrypted_password: encrypted_password,
        sign_in_count: sign_in_count,
        created_at: created_at,
        welcome_showed: welcome_showed,
        exam_taken: exam_taken,
        is_next_exam_date_undefined: is_next_exam_date_undefined,
        time_zone: time_zone,
        track_id: track.id,
        short_study_plan: short_study_plan,
        exam_scores: exam_scores,
        previous_plan: current_plan, # Because the new plan will be the plan upgraded to
        tapfiliate_conversion_id: tapfiliate_conversion_id,
        referred_by: referred_by
      },
      flags: flags.map(&:export_data),
      lesson_accesses: lesson_accesses.map(&:export_data),
      evaluations: evaluations.started.map(&:export_data),
      chapter_evaluations: chapter_evaluations.started.map(&:export_data),
      notes: notes.map(&:export_data),
      exercise_attempts: exercise_attempts.map(&:export_data),
      exam_scores_model: exam_scores_model.map(&:export_data)
    }
  end

  def intercom_id
    @intercom_id ||= intercom.users.find(user_id: app_id).id
  rescue StandardError => e
    intercom_user = intercom.users.create(email: email, user_id: app_id)
    @intercom_id ||= intercom_user.id

    Rails.logger.error("Error: #{e}")
    @intercom_id
  end

  def current_or_previous_plan_is_a_trial?
    RecurlyProxy::TRIAL_PLAN_CODES.include?(current_plan) || RecurlyProxy::TRIAL_PLAN_CODES.include?(previous_plan)
  end

  def needs_2fa?
    Rails.env.production? && admin_panel_access? && !authy_enabled
  end

  def enabled_2fa?
    !Rails.env.production? || authy_enabled
  end

  def subscription_expiring_soon?
    valid_membership? and
      !on_trial and
      !current_plan.to_s.include?("monthly") and
      !subscription_free? and
      subscription_ends_at.present? and
      subscription_ends_at < 1.week.from_now and
      future_subscription.blank? and
      !Flag.flagged_after?(self, self, "subscription_expiring_soon_noticed", 1.month.ago)
  end

  def date_for_exercise_levels
    admin_panel_access? ? Time.now : created_at
  end

  def not_using_feedback?
    created_at < 1.week.ago &&
      on_trial.blank? &&
      lesson_feedbacks.count.zero? &&
      (
        not_using_feedback_alert_showed_flags.empty? ||
        (
          not_using_feedback_alert_showed_flags.size < 3 &&
          not_using_feedback_alert_showed_flags.last.created_at < 1.week.ago
        )
      )
  end

  def letters_for_avatar
    "#{(first_name || '')[0]}#{(last_name || '')[0]}".upcase
  end

  def more_than_15_negative_feedback?
    lesson_feedbacks.negative_scores.count >= 15
  end

  def create_sticky_messages!
    Announcement.sticky.find_in_batches do |sticky_announcement|
      new_messages = []

      sticky_announcement.each do |announcement|
        next unless id.in?(announcement.recipients.ids)

        new_message = Message.new(
          content: announcement.content,
          sender: announcement.sender,
          recipient: self,
          announcement: announcement
        )

        new_messages << new_message
      end

      Message.import(new_messages)
    end
  end

  def next_exam_date
    @next_exam_date ||= test_dates.where("next_exam_date >= ?", Date.current).first&.next_exam_date
  end

  def last_exam_date
    @last_exam_date ||= test_dates.where("next_exam_date < ?", Date.current).last&.next_exam_date
  end

  def farthest_future_exam_date
    @farthest_future_exam_date ||= test_dates.where("next_exam_date >= ?", Date.current).last&.next_exam_date
  end

  def weeks_until_next_exam
    return unless next_exam_date.present?

    (next_exam_date - Date.current).to_i / 7
  end

  def exam_number(date)
    test_dates.pluck(:next_exam_date).sort.index(date) + 1
  end

  def setup_study_plan_settings_acknowledged?
    Flag.where(user: self, flaggable: self, label: "setup_study_plan_settings_acknowledged").present?
  end

  def integrated_reasoning_modal_acknowledged?
    Flag.where(user: self, flaggable: self, label: "integrated_reasoning_modal_acknowledged").present?
  end

  def deny_register_to_onlineclass?
    online_classes.map { |online_class| online_class.last_session_available(self) }.flatten.compact.any?
  end

  def available_current_onlineclass?
    OnlineClass.current_class(self).present?
  end

  def access_online_class?
    available_current_onlineclass? and OnlineClass.access_online_class?(self)
  end

  def wait_starting_onlineclass?
    (available_current_onlineclass? and OnlineClass.wait_starting_onlineclass?(self))
  end

  def generate_recurly_coupons
    return false unless RecurlyProxy::TRIAL_PLAN_CODES.include?(current_plan)

    delay.grant_recurly_coupon!
  end

  def last_adaptive_evaluation_attempt_finished?
    return false unless track.for_legacy_course

    adaptive_evaluations.last&.evaluation_attempts&.last&.finish? || false
  end

  def last_diagnostic_attempt_finished?
    return false unless track.for_legacy_course

    diagnostic_attempt&.finish? || false
  end

  def toggle_ondemand!(value)
    update(ondemand_enabled: ActiveModel::Type::Boolean.new.cast(value))
  end

  def ondemand_active?
    return false if for_legacy_course

    @ondemand_setting ||= Setting.for("ondemand_enabled")
    @ondemand_setting && ondemand_enabled?
  end

  def update_deleted_account_attributes
    user_email = "#{email}-unsubscribed"
    recurly_email = recurly_account_code.nil? ? nil : "#{recurly_account_code}-unsubscribed"

    n = 1
    n += 1 while User.exists?(email: "#{user_email}-#{n}") or (recurly_email and User.exists?(recurly_account_code: "#{recurly_email}-#{n}"))

    update_columns(email: "#{user_email}-#{n}", recurly_account_code: recurly_email ? "#{recurly_email}-#{n}" : nil)
  end

  private

  def format_name_component(name)
    name&.split(/(?=[A-Z])/)&.map(&:capitalize)&.join("")
  end

  def process_user_reward
    referrer.delay.grant_reward!
  end

  def set_default_role
    add_role :member if roles.empty?
  end

  def recurly_proxy
    @recurly_proxy ||= if guest?
      fake_recurly_proxy = OpenStruct.new(
        active?: (subscription_ends_at.blank? or subscription_ends_at > Time.zone.now),
        current_period_ends_at: (subscription_ends_at.present? ? subscription_ends_at : 10.days.from_now),
        trial_subscription?: true,
        recurly_current_plan: current_plan,
        recurly_email: email,
        billing_info: OpenStruct.new(first_name: first_name, last_name: last_name),
        has_plan?: false,
        recurly_login_token: nil,
        recurly_first_name: first_name,
        recurly_last_name: last_name,
        recurly_current_plan_cost: 0
      )

      fake_recurly_proxy.define_singleton_method(:available_coupon_for) { |_plan_code| "" }
      fake_recurly_proxy
    else
      RecurlyProxy.new(self)
    end
  end

  def send_ondemand_signup_email
    return unless %w(gmat ea).include?(EXAM_NAME)
    return if Flag.flagged?(self, self, "ondemand_sign_up_email_sent")

    Flag.flag(self, self, "ondemand_sign_up_email_sent")
    UserPersonalMailer.ondemand_sign_up(self).deliver_now
  end

  def send_trial_email
    return if !(RecurlyProxy::TRIAL_PLAN_CODES + ["free_diagnostic"]).include?(current_plan) or previous_plan == "free_diagnostic"

    require "countries/global"

    country = billing_info&.country

    is_asia = if country.present? and Country[country].try(:continent).present?
      Country[country].continent.downcase == "asia"
    else
      true
    end

    # Only send the email to asian users
    UserPersonalMailer.india_trial_user(self).deliver if is_asia
  end

  def delete_lead
    return unless (leads = Lead.where(email: email).order(created_at: :desc))

    referred_by = leads.map(&:referred_by).select(&:present?).uniq.first
    UtmEvent.create(utmable: self, source: referred_by) if referred_by.present?
    leads.destroy_all
  end

  def not_using_feedback_alert_showed_flags
    @not_using_feedback_alert_showed_flags ||= flags.where(flaggable: self, label: "not_using_feedback_alert_showed")
  end

  def fix_active_sections
    return unless EXAM_NAME == "gmat"
    return unless track.present?
    return unless active_sections.present?

    if track.for_legacy_course and active_sections.include?("di")
      self.active_sections = %w(quant verbal ir)
    elsif !track.for_legacy_course and active_sections.include?("ir")
      self.active_sections = %w(quant verbal di)
    end
  end

  def grant_recurly_coupon!
    if Setting.for("recurly_bulk_coupon_code").present? and recurly_coupon_code.blank?
      recurly_coupon = RecurlyProxy.create_recurly_coupon_for_user(Setting.for("recurly_bulk_coupon_code"))
      update_column(:recurly_coupon_code, recurly_coupon.coupon_code) if recurly_coupon&.persisted?
    end

    if Setting.for("recurly_bulk_coupon_code_2").present? and recurly_coupon_code_2.blank?
      recurly_coupon = RecurlyProxy.create_recurly_coupon_for_user(Setting.for("recurly_bulk_coupon_code_2"))
      update_column(:recurly_coupon_code_2, recurly_coupon.coupon_code) if recurly_coupon&.persisted?
    end
  end

  def intercom_proxy
    @intercom_proxy ||= IntercomProxy.new(self)
  end

  def update_intercom_sms_subscriptions
    if sms_enabled
      add_intercom_subscription(Setting.for("intercom_sms_marketing_subscription_id"))
    else
      delete_intercom_subscription(Setting.for("intercom_sms_marketing_subscription_id"))
    end
  end
end

# rubocop:enable Naming/PredicateName
