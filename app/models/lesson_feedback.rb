class LessonFeedback < ApplicationRecord
  include StatisticsCalculations

  belongs_to :lesson
  belongs_to :user
  belongs_to :resolved_by, class_name: "User"

  has_many :messages, dependent: :destroy

  delegate :chapter, to: :lesson

  validates :lesson, presence: true
  validates :user, presence: true, uniqueness: { scope: %i(lesson) }
  validates :score, presence: true, numericality: { greater_than_or_equal_to: 1, less_than_or_equal_to: 5 }

  enum :status, { unresolved: "unresolved", resolved: "resolved" }

  NEGATIVE_SCORES_ARRAY = [1, 2].freeze
  POSITIVE_SCORES_ARRAY = [3, 4, 5].freeze

  scope :filter_by_comment, ->(comment) do
    case comment
    when "Feedback" then where("comment <> ''")
    when "No Feedback" then where("comment = '' OR comment IS NULL")
    end
  end

  scope :filter_by_status, ->(status) do
    case status
    when "Resolved" then resolved
    when "Unresolved" then unresolved
    end
  end

  scope :order_by_param, ->(param) do
    case param
    when "score-desc" then order(score: :desc, created_at: :desc)
    when "date-asc" then order(updated_at: :asc, created_at: :desc)
    when "date-desc" then order(updated_at: :desc, created_at: :desc)
    else order(score: :asc, created_at: :desc)
    end
  end

  scope :by_chapter, ->(chapter_id) { where(lesson: Lesson.where(chapter_id: chapter_id)) }
  scope :by_status, ->(status) { status == "" ? all : where(status: status) }
  scope :by_user_name_or_user_email, ->(query) { where(user: User.by_name_or_email(query)) }
  scope :negative_scores, -> { where(score: NEGATIVE_SCORES_ARRAY) }
  scope :positive_scores, -> { where(score: POSITIVE_SCORES_ARRAY) }
  scope :by_score, ->(score) do
    return all if score == ""

    score == "positive" ? positive_scores : negative_scores
  end

  def self.analytics_for(topic, start_date, end_date)
    # Don't use function default values to allow empty params
    start_date = start_date.blank? ? "2020-01-01".to_date : start_date.to_date
    end_date = end_date.blank? ? Date.current : end_date.to_date
    date_range = (start_date.beginning_of_day..end_date.end_of_day)

    scores = topic.lesson_feedbacks.unresolved.where(created_at: date_range).pluck(:score)

    {
      mean: mean(scores),
      median: median(scores),
      mode: mode(scores),
      standard_deviation: standard_deviation(scores),
      total: scores.length
    }
  end

  def score_to_s
    score_names = {
      1 => "very_unhelpful",
      2 => "unhelpful",
      3 => "neutral",
      4 => "helpful",
      5 => "very_helpful"
    }

    score_names[score]
  end

  def score_to_class
    score.in?([1, 2]) ? "unhelpful" : "helpful"
  end

  def resolved!
    self.resolved_at = Time.current

    super
  end

  def unresolved!
    self.resolved_at = nil
    self.resolved_by = nil

    super
  end
end
