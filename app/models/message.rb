# rubocop:disable Style/FormatStringToken

class Message < ApplicationRecord
  belongs_to :announcement
  belongs_to :lesson_feedback, touch: true
  belongs_to :sender, class_name: "User"
  belongs_to :recipient, class_name: "User"

  validates :content, presence: true

  scope :read, -> { where.not(read_at: nil).where(deleted_at: nil) }
  scope :unread, -> { where(read_at: nil, deleted_at: nil) }
  scope :not_deleted, -> { where(deleted_at: nil) }
  scope :deleted, -> { where.not(deleted_at: nil) }

  def self.mark_group_as_read!
    all.each(&:mark_as_read!)
  end

  def self.mark_group_as_deleted!
    update_all(deleted_at: Time.current)
  end

  def mark_as_read!
    update(read_at: Time.current) unless read_at.present?
  end

  def mark_as_deleted!
    update(deleted_at: Time.current) unless deleted_at.present?
  end

  def read?
    read_at.present?
  end

  def build_recipient_name(user)
    content.gsub("%{first_name}", user.first_name)
  end
end

# rubocop:enable Style/FormatStringToken
