module ActAsSearchable
  extend ActiveSupport::Concern

  included do
    scope :search, (lambda do |term, case_sensitive|
      like = case_sensitive ? "LIKE" : "ILIKE"

      query = ""
      searchable_fields.each_with_index do |field, _index|
        field += "::text" if field.eql?("id")
        query << "#{field} #{like} :term"
        query << " OR " unless field.eql? searchable_fields.last
      end

      where(query, term: "%#{term}%")
    end)
  end
end
