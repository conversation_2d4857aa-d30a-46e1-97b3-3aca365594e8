class OnlineClassSession < ApplicationRecord
  belongs_to :online_class
  has_many :homework_groups, dependent: :destroy
  has_many :online_class_session_documents, dependent: :destroy
  has_many :flags, as: :flaggable, dependent: :destroy

  validates :date, presence: true
  validates :description, presence: true
  validate :valid_end_time

  before_save :set_default_time, if: -> { start_time.nil? || end_time.nil? }

  enum :homework_status, { not_ready: 0, ready_for_release: 1, released: 2 }

  serialize :homework_task_data, type: Array

  accepts_nested_attributes_for :online_class_session_documents, allow_destroy: true

  scope :with_recording, -> { where(show_in_library: true).where.not(recording_url: "") }

  scope :filter_sessions_by_name, ->(query) {
    where("regexp_replace(description, '<[^>]*>', '', 'g') ILIKE ?", "%#{query}%")
  }

  scope :bookmarked_by_user, ->(user) {
    where id: Flag.by_online_class_session(user).pluck(:flaggable_id)
  }

  def starts_at
    @starts_at ||= begin
      start_time_est = Time.strptime(start_time, "%I:%M %P")
      DateTime.new(date.year, date.month, date.day, start_time_est.hour, start_time_est.min, start_time_est.sec, "EST")
    end
  end

  def ends_at
    @ends_at ||= begin
      end_time_est = Time.strptime(end_time, "%I:%M %P")
      DateTime.new(date.year, date.month, date.day, end_time_est.hour, end_time_est.min, end_time_est.sec, "EST")
    end
  end

  def number_of_session
    OnlineClassSession.includes(:online_class).where(online_class_id: online_class_id).sort_by(&:date).map(&:id).index(id) + 1
  end

  def self.next_session(user, online_class_ids = nil)
    time_zone = user.time_zone
    current_datetime = DateTime.current.in_time_zone(time_zone)
    online_class_ids ||= user.online_classes.ids

    where(online_class_id: online_class_ids)
      .order("date ASC")
      .select { |online_class_session| current_datetime < online_class_session.ends_at.in_time_zone(time_zone) }
      .first
  end

  def self.last_session_date(user, online_class_ids = nil)
    online_class_ids ||= user.online_classes.ids

    where(online_class_id: online_class_ids).order("date ASC").last.ends_at.in_time_zone(user.time_zone)
  end

  def homework_released?
    homework_status == "released"
  end

  def recording_not_added?
    recording_url.blank?
  end

  def recording_added?
    recording_url.present? && !show_in_library
  end

  def recording_released?
    recording_url.present? && show_in_library
  end

  private

  def valid_end_time
    start_at = ActiveSupport::TimeZone["Eastern Time (US & Canada)"].parse("#{Date.current} #{start_time}")
    end_at = ActiveSupport::TimeZone["Eastern Time (US & Canada)"].parse("#{Date.current} #{end_time}")

    errors.add(:end_time, "should be later than the start time") if start_at > end_at || start_at == end_at
  end

  def set_default_time
    return unless start_time.nil? || end_time.nil?

    self.start_time = online_class.start_time
    self.end_time = online_class.end_time
  end
end
