# rubocop:disable Naming/PredicateName

class Evaluation < ApplicationRecord
  include Exportable

  enum :test_mode, { quiz_mode: 0, practice_mode: 1 }

  DIFFICULTY_LEVEL = %w(easy medium hard challenge).freeze
  HIDDEN_FILTERS = %w(weakest_topics unanswered_first).freeze

  EXPORT_ATTRIBUTES = %i(
    level
    archived
    chapter_id
    name
    time_per_question
    practice_topic_id
    problems_filtered_by
    evaluations_exercises_attributes evaluation_attempts_attributes
  ).freeze

  belongs_to :user
  belongs_to :study_module_item
  belongs_to :homework_task
  has_many :evaluations_exercises, -> { order "evaluations_exercises.position ASC, evaluations_exercises.id ASC" }, dependent: :destroy
  has_many :problems, through: :evaluations_exercises
  has_many :evaluation_attempts, -> { order "created_at DESC" }, dependent: :destroy
  has_many :user_diagnostics, dependent: :destroy

  has_one :track # diagnostic

  accepts_nested_attributes_for :evaluations_exercises
  accepts_nested_attributes_for :evaluation_attempts

  validates :user, presence: true, unless: :is_diagnostic
  validate :problems_filtered_by_are_in_list

  before_validation :assign_problems, if: -> { evaluations_exercises.blank? }

  serialize :problems_filtered_by

  attr_accessor :number_of_problems, :chapter_levels, :is_diagnostic

  scope :published, (lambda do
    where(archived: false)
  end)

  scope :started, (lambda do
    joins(:evaluation_attempts)
  end)

  scope :in_section, (lambda do |section|
    joins(:chapter).merge(Chapter.in_section(section))
  end)

  scope :ordered_by_chapter_number, -> do
    joins(:chapter).order(number: :asc)
  end

  def self.problem_type_filters
    {
      quant: Problem.question_types_per_section[:quant],
      verbal: [],
      ir: [],
      di: Problem.question_types_per_section[:di]
    }
  end

  def self.problems_filters
    @problems_filters ||= {
      quant: %w(bookmarked new_questions_only) + problem_type_filters[:quant] + %w(answered_wrong over_two_minutes),
      verbal: %w(bookmarked new_questions_only answered_wrong over_two_minutes),
      ir: %w(bookmarked new_questions_only answered_wrong over_two_minutes),
      di: %w(bookmarked new_questions_only answered_wrong over_two_minutes)
    }
  end

  def self.exam_time_per_question
    @time_per_question ||= Setting.for("time_per_question").to_i
  end

  def self.number_of_questions_for_review_test
    return 20 if EXAM_NAME == "gre"
    return 20 if EXAM_NAME == "sat"
    return 14 if EXAM_NAME == "ea"

    31
  end

  def problems_filtered_by_are_in_list
    return unless problems_filtered_by.present?

    problems_filtered_by.each do |section, filter|
      filters = Evaluation.problems_filters[section.to_sym] + HIDDEN_FILTERS + [""]

      next if filters.include?(filter)

      errors[:problems_filtered_by] << I18n.t("user.custom_test.errors.problems_filtered_by")
    end
  end

  def problem(id)
    problems.where(id: id).first
  end

  def level_enum
    DIFFICULTY_LEVEL
  end

  def review_test?
    study_module_item&.review_test?
  end

  def review_quiz?
    study_module_item&.review_quiz?
  end

  def is_study_plan_test?
    study_module_item.present?
  end

  def is_homework_task_test?
    homework_task.present?
  end

  def challenge_test?
    instance_of?(ChapterEvaluation) and level == "challenge"
  end

  def on_target_test?
    practice_topic_id.present?
  end

  %w(chapter personalized user).each do |evaluation_name|
    define_method("#{evaluation_name}_test?") do
      type == "#{evaluation_name.humanize}Evaluation"
    end
  end

  def adaptive_evaluation?
    is_a?(AdaptiveEvaluation)
  end

  private

  def order_problems_for_section(problems, section)
    single_problems = problems.to_a.select { |p| p.group_size == 1 }
    grouped_problems = problems.to_a.select { |p| p.group_size > 1 }

    if problems_filtered_by and problems_filtered_by[section] == "unanswered_first"
      problem_set = Problem.where(id: single_problems.map(&:id))
      ps_problems = (
        problem_set.unanswered_by_user(user).to_a.keep_if(&:problem_solving?).shuffle +
        problem_set.answered_by_user(user).to_a.keep_if(&:problem_solving?).shuffle
      ).in_groups_of(2)

      other_problems = problem_set.unanswered_by_user(user).to_a.keep_if(&:not_problem_solving?).shuffle +
        problem_set.answered_by_user(user).to_a.keep_if(&:not_problem_solving?).shuffle
    else
      ps_problems = single_problems.select(&:problem_solving?).shuffle.in_groups_of(2)
      other_problems = single_problems.select(&:not_problem_solving?).shuffle
    end

    (ps_problems.zip(other_problems).flatten | other_problems).flatten.compact +
      grouped_problems.sort { |a, b| "#{a.group_parent_id} #{a.id}" <=> "#{b.group_parent_id} #{b.id}" }
  end
end

# rubocop:enable Naming/PredicateName
