class Product
  include ActiveModel::Model

  attr_accessor :title, :description, :product_code, :current_price, :sale_price, :product_image_url, :display_quantity

  def self.all
    study_products.map do |product_attributes|
      Product.new(product_attributes)
    end
  end

  def self.find(product_code)
    all.find { |product| product.product_code == product_code }
  end

  def self.subscription_plan_codes
    case EXAM_NAME
    when "sat"
      { c: "monthly-sat", a: "6months-sat", b: "1year-sat" }
    when "gre"
      { c: Setting.for("recurly_monthly_plan_code"), a: Setting.for("recurly_4months_plan_code"), b: Setting.for("recurly_6months_plan_code") }
    else
      { c: Setting.for("recurly_monthly_plan_code"), a: Setting.for("recurly_4months_plan_code"), b: Setting.for("recurly_6months_plan_code") }
    end
  end

  def self.partner_plan_codes
    (RecurlyProxy::PARTNER_ORG_PLAN_CODES + Setting.for("recurly_other_plan_codes").to_s.split(",").map(&:strip)).select { |plan_code| plan_code.include?("1year") or plan_code.include?("gmatclub") or plan_code.include?("greprepclub") }
  end

  def self.trial_plan_codes
    RecurlyProxy::TRIAL_PLAN_CODES.select { |plan_code| plan_code.include?("5day") or plan_code.include?("gmatclub") }
  end

  def self.ondemand_plan_codes
    RecurlyProxy::ONDEMAND_PLAN_CODES
  end

  def self.admission_plan_codes
    RecurlyProxy::ADMISSIONS_PLAN_CODES
  end

  def self.tutoring_plan_codes
    RecurlyProxy::TUTORING_PLAN_CODES
  end

  def self.online_class_plan_codes
    RecurlyProxy::ONLINE_CLASS_PLAN_CODE.to_s.split(",").map(&:strip).uniq.compact
  end

  def self.study_products
    study_products = []

    subscription_plan_codes.each do |plan_key, plan_code|
      study_products << { title: I18n.t("user.static_pages.plans.plan_options.plan_#{plan_key}.title", exam_name: EXAM_NAME.upcase), description: I18n.t("user.plans.plan_#{plan_key}.intro", exam_name: EXAM_NAME.upcase), product_code: plan_code, current_price: I18n.t("user.plans.plan_#{plan_key}.price"), sale_price: I18n.t("user.plans.plan_#{plan_key}.sale_price"), product_image_url: "layout/controllers/content_pages/plans/ttp_self_study.png" }
    end

    (partner_plan_codes + trial_plan_codes + ondemand_plan_codes + admission_plan_codes + tutoring_plan_codes + online_class_plan_codes).each do |plan_code|
      plan = Recurly::ALL_PLANS.find { |recurly_plan| recurly_plan.plan_code == plan_code }
      next unless plan

      option = trial_plan_codes.include?(plan.plan_code)

      study_products << {
        title: plan.name,
        description: plan_description(plan, option),
        product_code: plan.plan_code,
        current_price: ((plan.plan_code == "5daystrial") ? I18n.t("user.plans.plan_d.sale_price") : plan.unit_amount_in_cents.to_i / 100),
        sale_price: ((option && plan.plan_code == "5daystrial") ? I18n.t("user.plans.plan_d.sale_price") : plan.unit_amount_in_cents.to_i / 100),
        product_image_url: "layout/controllers/content_pages/plans/ttp_self_study.png",
        display_quantity: plan.display_quantity
      }
    end

    study_products
  end

  def self.plan_description(plan, option = nil)
    if RecurlyProxy::TUTORING_PLAN_CODES.include?(plan.plan_code) and RecurlyProxy::COMPLIMENTARY_SUBSCRIPTIONS.key?(plan.plan_code)
      I18n.t("user.plans.tutoring_package_info.#{plan.plan_code.split('-', 3).last.gsub('-', '_')}_plan.subtitle", exam_name: EXAM_NAME.upcase)
    else
      option ? I18n.t("user.plans.plan_d.intro", exam_name: EXAM_NAME.upcase) : I18n.t("user.plans.partner_plan_code.description", exam: EXAM_NAME.upcase)
    end
  end
end
