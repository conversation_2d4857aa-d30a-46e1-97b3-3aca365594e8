class HomeworkTask < ApplicationRecord
  belongs_to :homework_group
  belongs_to :chapter
  has_one :homework_task_takeaway, dependent: :destroy
  has_one :homework_task_comment, dependent: :destroy

  alias comment homework_task_comment

  TASK_TYPES = {
    custom_test: "custom_test",
    chapter_test: "chapter_test",
    prereading: "pre_reading"
  }.freeze

  validates :task_type, inclusion: { in: TASK_TYPES.values }

  scope :custom_tests, -> { where(task_type: TASK_TYPES[:custom_test]) }
  scope :chapter_tests, -> { where(task_type: TASK_TYPES[:chapter_test]) }
  scope :prereadings, -> { where(task_type: TASK_TYPES[:prereading]) }

  def custom_test?
    task_type == TASK_TYPES[:custom_test]
  end

  def chapter_test?
    task_type == TASK_TYPES[:chapter_test]
  end

  def prereading?
    task_type == TASK_TYPES[:prereading]
  end

  def generate_completion_for(user)
    case task_type
    when TASK_TYPES[:prereading]
      total_tasks = data.count
      completed_tasks = (Topic.completed_for(user).ids & data).size

      return { total: 1, completed: 0, total_tasks: total_tasks, percentage: 0 } if total_tasks.zero?

      percentage = (completed_tasks.to_f / total_tasks).round(2) * 100
      { total: 1, completed: completed_tasks, total_tasks: total_tasks, percentage: percentage }
    when TASK_TYPES[:custom_test]
      evaluation, evaluation_attempt = find_evaluation_for(user)

      completed = if evaluation_attempt.present?
        evaluation_attempt.finish? ? 1 : 0.5
      else
        0
      end

      accuracy = evaluation_attempt&.finish? ? evaluation_attempt.percentage_correct : nil
      last_access = evaluation_attempt&.problem_attempts&.last&.created_at

      {
        total: 1,
        completed: completed,
        percentage: (completed * 100),
        accuracy: accuracy,
        evaluation_link: generate_evaluation_link_for(user),
        last_access: last_access,
        to_start: evaluation.present? && completed.zero?
      }
    else
      flag = Flag.where(flaggable: self, user: user, label: "completed").last
      completed = flag.present? ? 1 : 0

      { total: 1, completed: completed, percentage: (completed * 100), last_access: flag&.created_at }
    end
  end

  def find_evaluation_for(user)
    evaluation = user.evaluations.where(homework_task_id: id).first
    evaluation_attempt = evaluation.evaluation_attempts.first if evaluation.present?

    [evaluation, evaluation_attempt]
  end

  def generate_evaluation_link_for(user)
    evaluation, evaluation_attempt = find_evaluation_for(user)

    if evaluation.present? and evaluation_attempt.blank?
      Rails.application.routes.url_helpers.evaluation_start_path(evaluation)
    elsif evaluation_attempt&.finish?
      Rails.application.routes.url_helpers.evaluation_attempt_path(evaluation_attempt)
    elsif evaluation_attempt.present?
      Rails.application.routes.url_helpers.evaluation_continue_path(evaluation_attempt)
    end
  end
end
