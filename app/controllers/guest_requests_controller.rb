class GuestRequestsController < ApplicationController
  before_action :validate_guest_request
  before_action :set_meta_data, only: %i(new take_diagnostic)
  layout "public", only: %i(new take_diagnostic)

  def new
    @guest_request = GuestRequest.new
    @test_type = test_type
  end

  def create
    @guest_request = GuestRequest.new(guest_request_params)

    if @guest_request.save
      user = User.find_by_email(@guest_request.email)
      cart = Cart.new(user: user, plan_code: Setting.for("recurly_free_5days_plan_code"))
      session[:shopping_cart] = Cart.dump(cart)
      respond_to do |format|
        format.html { redirect_to guest_request_redirect_url }
        format.js { render "create_success" }
      end
    else
      @error_message = @guest_request.errors.full_messages.join(", ")

      respond_to do |format|
        format.html do
          if @guest_request.diagnostic
            flash.now[:alert] = @error_message
            render :new
          else
            flash[:alert] = @error_message
            redirect_to :back
          end
        end
        format.js { render "create_failure" }
      end
    end
  end

  def validate
    @guest_request = GuestRequest.new guest_request_params

    if @guest_request.valid? and @guest_request&.track&.for_legacy_course
      @test_type = :gmat
    end
  end

  def access_diagnostic_test
    user = User.find_by_email(params[:guest_request][:email])

    if user.present? and user.free_diagnostic_user?
      attempt = user.evaluation_attempts.first

      evaluation_path = attempt.finish? ? evaluation_attempt_path(attempt) : evaluation_continue_path(attempt)

      redirect_to evaluation_path
    elsif user.present?
      redirect_to new_user_session_path
    else
      flash[:alert] = t("guests.errors.diagnostic_modal_error")
      redirect_to params[:url] == "free_practice_test" ? practice_test_path : take_diagnostic_guest_request_path
    end
  end

  def start_free_trial
    user = User.find_by_email(params[:user_email])

    if user.blank? or user.encrypted_password.present?
      redirect_to new_user_session_path
    else
      subscription = RecurlyProxy.create_recurly_account_and_free_subscription(user)
      user.update_personal_data
      user.update_subscription_data
      user.save!

      sign_in user

      redirect_to set_password_path
    end
  end

  private

  def guest_request_params
    params.require(:guest_request).permit(:first_name, :last_name, :email, :track_id, :diagnostic)
  end

  def validate_guest_request
    return unless current_user

    flash[:alert] = t "guests.errors.can_not_access"
    redirect_to analytics_path
  end

  def guest_request_redirect_url
    return sign_in_with_token_url(host: Setting.for("ttp_trial_server_url"), token: @guest_request.auth_token) if Setting.for("ttp_trial_server_enabled")

    user = User.find_by_email(@guest_request.email)

    if user&.free_diagnostic_user?
      evaluation_attempt = user.evaluation_attempts.not_finished.first

      return evaluation_continue_path(attempt_id: evaluation_attempt.id) if evaluation_attempt

      test_order = params[:test_order]&.split(",") || %w(verbal quant)
      evaluation = if user&.track&.for_legacy_course
        AdaptiveEvaluation.get_cat_for(track: user.track, test_order: test_order, user: user)
      else
        UserEvaluation.get_diagnostic_for(user.track, test_order, user)
      end

      evaluation_starter = EvaluationStarter.call(user, evaluation)
      return problem_solving_ask_path(evaluation_starter.evaluation_attempt, evaluation_starter.problem_attempt)
    end

    sign_in_with_token_path(token: auth_token)
  end

  def test_type
    return :gmat_focus unless params[:test_type]

    return :gmat if params[:test_type] == "gmat"

    :gmat_focus
  end

  def set_meta_data
    @allow_robot_indexing = true
  end
end
