module Admin
  class LessonFeedbacksController < Admin::AdminController
    load_and_authorize_resource

    def resolve
      ActiveRecord::Base.transaction do
        @lesson_feedback.resolved!
        @lesson_feedback.update!(resolved_by: current_user)
      end
    end

    def unresolve
      ActiveRecord::Base.transaction do
        @lesson_feedback.unresolved!
        @lesson_feedback.update!(resolved_by: nil)
      end

      render "resolve"
    end
  end
end
