module Admin
  class OnlineClassesController < Admin::AdminController
    load_and_authorize_resource except: %i(create)
    before_action :find_user_by_email, only: %i(add_user add_without_removing remove_from_other_class)
    before_action :bootcamp_page?
    before_action :set_online_classes, except: %i(create)

    def index
      return unless params[:schedule].present?

      @online_classes = filter_by_schedule(params[:schedule])
    end

    def new; end

    def edit; end

    def create
      @online_class = OnlineClass.new(online_class_params)

      if online_class_params[:syllabus].present? && !online_class_params[:syllabus].content_type&.in?("application/pdf")
        flash[:error] = "Invalid file type - You need to add a pdf file"
        redirect_to redirect_path and return
      end

      if @online_class.save
        flash[:notice] = @is_bootcamp_page ? "Bootcamp cohort created successfully" : "Online class created successfully"
        redirect_to redirect_path
      else
        @online_class.syllabus.cache! if @online_class.syllabus.present?
        render :new
      end
    end

    def update
      if online_class_params[:syllabus].present? && !online_class_params[:syllabus].content_type&.in?("application/pdf")
        flash[:error] = "Invalid file type - You need to add a pdf file"
        redirect_to redirect_path and return
      end

      if @online_class.update(online_class_params)
        flash[:notice] = @is_bootcamp_page ? "Bootcamp cohort updated successfully" : "Online class updated successfully"
        redirect_to redirect_path
      else
        @online_class.syllabus.cache! if @online_class.syllabus.present?

        render :edit
      end
    end

    def show
      @filter = params[:filter]
      @online_class = if @is_bootcamp_page
        OnlineClass.bootcamp_classes.find(params[:id])
      else
        OnlineClass.liveteach_classes.find(params[:id])
      end
    end

    def destroy
      @online_class.destroy

      respond_to do |format|
        format.html { redirect_to redirect_path, notice: "Online Class deleted successfully" }
      end
    end

    def generate_class_automatically; end

    def download_class_participants
      @participants = @online_class.users

      respond_to do |format|
        format.csv do
          send_data OnlineClass.to_csv(@participants),
            filename: "participants_list_#{@online_class.id}.csv",
            content_type: "application/csv"
        end
      end
    end

    def delete_participant
      @online_classes_user = @online_class.online_classes_users.find_by(user_id: params[:online_class_user_id])
      @online_classes_user.destroy
    end

    def add_user
      if @user.nil?
        @error = "User account with that email does not exist."
      elsif @online_class.users.include?(@user)
        @error = "User is already enrolled in this class."
      else
        @other_classes = @user.online_classes.where.not(id: @online_class.id)

        add_user_to_class if @other_classes.empty?
      end
    end

    def add_without_removing
      add_user_to_class
    end

    def remove_from_other_class
      @user.online_classes.clear
      add_user_to_class
    end

    def add_class_recording
      @online_class_session = OnlineClassSession.find(params[:online_class_session_id])
      @online_class_session.assign_attributes(
        recording_url: params[:recording_url],
        show_in_library: params[:recording_url].present? && params[:show_in_library] == "1"
      )

      if @online_class_session.save
        @message = "Class recording status updated successfully."
        @status = :success
      else
        @message = "Failed to update class recording status."
        @status = :error
      end

      respond_to do |format|
        format.js
      end
    end

    private

    def filter_by_schedule(schedule)
      case schedule
      when "past"
        @online_classes.select { |online_class| online_class.last_class < Date.current }
      when "current"
        @online_classes.select { |online_class| online_class.first_class <= Date.current and online_class.last_class >= Date.current }
      when "future"
        @online_classes.select { |online_class| online_class.first_class > Date.current }
      end
    end

    def online_class_params
      params.require(:online_class).permit(:start_time, :end_time, :spots, :test_type, :instructor_id, :syllabus, :syllabus_cache, :zoom_link, :class_type,
        online_class_sessions_attributes: %i(id start_time end_time date description zoom_link _destroy))
    end

    def find_user_by_email
      @user = User.find_by(email: params[:email])
    end

    def add_user_to_class
      @online_class.users << @user
      @success = "User successfully added to the class."
    end

    def bootcamp_page?
      @is_bootcamp_page = request.path.include?("/bootcamp")
    end

    def set_online_classes
      @online_classes = bootcamp_page? ? OnlineClass.bootcamp_classes : OnlineClass.liveteach_classes
    end

    def redirect_path
      @is_bootcamp_page ? admin_bootcamp_classes_path : admin_online_classes_path
    end
  end
end
