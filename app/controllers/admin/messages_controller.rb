# rubocop:disable Style/TernaryParentheses

module Admin
  class MessagesController < Admin::AdminController
    include Pagy::Backend

    before_action :lesson_feedbacks, only: %i(user_feedback)

    def user_feedback; end

    def reply
      @lesson_feedback = LessonFeedback.find_by(id: params[:id])
    end

    def create
      @lesson_feedback = LessonFeedback.find_by(id: params[:lesson_feedback_id])
      return unless @lesson_feedback.unresolved?

      @messages = @lesson_feedback.messages.order(:created_at)

      @message = Message.new(message_params)
      @message.content.strip!
      @message.lesson_feedback = @lesson_feedback
      @message.sender = current_user
      @message.recipient = @lesson_feedback.user

      ActiveRecord::Base.transaction do
        @message.save!
        @lesson_feedback.resolved!
        @lesson_feedback.update!(resolved_by: current_user)
      end
    end

    private

    def lesson_feedbacks
      params_status = (params[:status] == "" or params[:status].present?) ? params[:status] : "unresolved"
      params_score = (params[:score] == "" or params[:score].present?) ? params[:score] : "negative"

      @chapters = Chapter.in_section(current_section).ordered.select(:id, :name)
      @lesson_feedbacks = LessonFeedback.joins(:lesson).order(Arel.sql("lesson_feedbacks.updated_at DESC")).includes(:user)
      @lesson_feedbacks = @lesson_feedbacks.by_chapter(params[:chapter_id]) if params[:chapter_id].present?
      @lesson_feedbacks = @lesson_feedbacks.by_user_name_or_user_email(params[:query]) if params[:query].present?
      @lesson_feedbacks = @lesson_feedbacks.by_status(params_status)
      @lesson_feedbacks = @lesson_feedbacks.by_score(params_score)

      @chapter_id = params[:chapter_id]
      @status = params_status
      @score = params_score
      @query = params[:query]

      @pagy, @lesson_feedbacks = pagy(@lesson_feedbacks.order(:created_at))
    end

    def message_params
      params.require(:message).permit(:content)
    end
  end
end

# rubocop:enable Style/TernaryParentheses
