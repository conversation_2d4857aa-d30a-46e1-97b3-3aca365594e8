module AiAssist
  class Chat<PERSON>sController < ApplicationController
    protect_from_forgery with: :null_session

    def chat
      raise StandardError if current_user.on_trial? and current_user.ai_assist_message_count > 5

      message_history = chatbot_params[:message_history].present? ? JSON.parse(chatbot_params[:message_history].to_json) : []
      chatbot_service = AiAssist::ChatbotService.new(chatbot_params.except(:message_data), options: { controller: "chat_bot", user: current_user })

      response = chatbot_service.call
      chat_feedback = current_user.chat_messages.create!(prompt: chatbot_params[:prompt], response: response, messageable_type: "Lesson", messageable_id: chatbot_params[:lesson_id], user_id: current_user.id) if response.present?

      render json: { content: response, message_data: params[:message_data], chat_feedback_id: chat_feedback&.id, limit_reached: check_user_limit }
    rescue StandardError => error
      Rails.logger.error error.message.to_s
      render json: { error: error.message }, status: :internal_server_error
    end

    def remaining_messages
      current_user.increment!(:ai_assist_message_count) if current_user.ai_assist_message_count < 5 && params[:user_message] == "true"

      remaining_messages = current_user.ai_assist_message_count > 5 ? 0 : 5 - current_user.ai_assist_message_count
      render json: { remaining_messages: remaining_messages, on_trial: current_user.on_trial? }
    rescue StandardError => error
      Rails.logger.error error.message.to_s
      Rails.logger.error error.backtrace.join("\n")
      render json: { error: error.message }, status: :internal_server_error
    end

    private

    def check_user_limit
      return false unless current_user.on_trial?

      current_user.ai_assist_message_count >= 5
    end

    def chatbot_params
      params.permit(:prompt, :lesson_id, targeted_practice_info: {}, message_data: {}, message_history: {}).to_h
    end
  end
end
