module AiAssist
  class ChatMessagesController < ApplicationController
    before_action :find_chat_message, only: %i(edit update)

    def edit; end

    def update
      if @chat_message.update(chat_message_params)
        @success = true
        @message = "Thank you for providing your feedback!"
      else
        @success = false
        @errors = @chat_message.errors.full_messages.join(", ")
      end

      render "update"
    end

    private

    def find_chat_message
      @chat_message = current_user.chat_messages.find(params[:id])
    end

    def chat_message_params
      params.require(:ai_assist_chat_message).permit(:prompt, :response, :score, :comment, :messageable_type, :messageable_id)
    end
  end
end
