class SubscriptionsController < ApplicationController
  skip_before_action :check_welcome_wizard_finished, only: %i(new manage renew update_card cancel verify_coupon remove_card)

  before_action :authenticate_user!, only: %i(new manage update_card renew cancel)
  before_action :verify_user_session!, only: %i(new manage update_card renew cancel)
  before_action :set_variables, only: %i(new manage update_card renew cancel)
  before_action :set_online_class, only: %i(manage)

  def checkout
    return render "content_pages/not_found", status: 404, layout: "public" unless RecurlyProxy.valid_plan_code?(params[:plan_code])
    return redirect_to plans_path(trial_signup: true) if RecurlyProxy::TRIAL_PLAN_CODES.include?(params[:plan_code]) and Setting.for("free_trial_enabled") and !cardless_trial_test?

    @user = User.find_by_id(params[:user_id]) if params[:user_id].present?
    @user ||= User.find_by_email(params[:email].downcase) if params[:email].present?

    if @user.present?
      cart = Cart.new(user: @user, plan_code: params[:plan_code])
      session[:shopping_cart] = Cart.dump(cart)
      redirect_to cart_path(referrer: request.referrer)
    else
      @user = User.new
      @plan = RecurlyProxy.plan(params[:plan_code])
      render "checkout", layout: "public"
    end
  end

  def manage
    redirect_to new_subscription_path if @recurly.send(:account).blank?

    return unless session[:track_sale].present?

    @sale_value = current_user.last_transaction&.amount_in_cents.to_i / 100.00
    @transaction_id = current_user.last_transaction&.uuid
    @plan_code = session[:track_sale]

    session[:track_sale] = nil
  end

  def update_card
    card = @recurly.update_account_card(params[:recurly_token], params[:three_d_secure_action_result_token_id])
    return render("three_d_secure", locals: { token: card.token }) if card.try(:require_three_d_secure)

    if card
      current_user.billing_info ? current_user.billing_info.update_fields : current_user.update_transactions_and_billing_info
    else
      @errors = @recurly.errors
    end

    respond_to do |format|
      format.js
      format.html do
        if @errors
          flash[:error] = @errors
        else
          flash[:notice] = "Card Updated Successfully"
        end
        redirect_to manage_subscriptions_path
      end
    end
  end

  def upgrade
    redirect_to current_user.present? ? new_subscription_path : plans_path
  end

  def remove_card
    recurly = current_user.send(:recurly_proxy)
    @success = recurly.remove_billing_info

    current_user.billing_info.destroy if @success

    respond_to do |format|
      format.html do
        if @success
          flash[:notice] = "Credit Card removed successfully"
        else
          flash[:error] = "Credit Card could not be removed. Please try again later."
        end

        redirect_to manage_subscriptions_path
      end
    end
  end

  def cancel
    @success = @recurly.cancel_current_subscription

    respond_to do |format|
      format.js
      format.html do
        if @errors
          flash[:error] = "Subscription Could not Be Canceled. Please Try Again Later."
        else
          flash[:notice] = "Subscription Canceled Successfully"
        end

        redirect_to manage_subscriptions_path
      end
    end
  end

  def subscribed
    account_code = subscribed_params[:account_code]
    plan_code    = subscribed_params[:plan]
    attributes   = {
      recurly_account_code: account_code,
      email: account_code,
      is_going_to_apply_next_mlt_cycle: params[:is_going_to_apply_next_mlt_cycle],
      business_school_admission_application: params[:business_school_admission_application]
    }

    attributes.merge!(organization: subscribed_params[:plan].split("-")[-2]) if subscribed_params[:plan].split("-").last == "org"

    user = User.find_by_email(account_code)
    user = User.new(attributes) unless user.present?
    user.session_identifier = experiment_user_key
    is_guest_user = false
    if user.guest? or !user.confirmed? or user.recurly_account_code.blank?
      user.recurly_account_code = account_code
      user.guest = false
      user.confirm unless user.confirmed?
      is_guest_user = true
    end

    if RecurlyProxy.valid_plan_code?(plan_code) && user.has_plan?(plan_code)
      if (user.new_record? and (RecurlyProxy.onlineclass_code?(plan_code) or RecurlyProxy.app_plan_code?(plan_code))) or is_guest_user
        user.update_personal_data(is_guest_user)
        update_ondemand_data(user, plan_code)

        if user.save
          track_utm_event_for(user, "sign-up")
          track_ga_subscription(user.current_subscription) if session[:ga_subscription_uuid].blank? and user.current_subscription.present?
          create_intercom_event(user, user_id: user.app_id, event_name: "subscribed-to-plan")
        end
      end

      if user.persisted?
        if subscribed_params[:online_class_id]
          user.update_transactions_and_billing_info
          OnlineClassesUser.create(user_id: user.id, online_class_id: subscribed_params[:online_class_id])
          update_ondemand_data(user)
        end

        User::Referral.track(session[:referrer_id], user.id)
        session.delete(:referrer_id)

        sign_out current_user if current_user

        user.update_subscription_data

        sign_in user

        revenue_cents = user.last_transaction&.amount_in_cents || 0

        user.delay.generate_second_commission_in_tapfiliate(revenue_cents / 100.00) if RecurlyProxy::TRIAL_PLAN_CODES.include?(user.previous_plan)

        redirect_to set_password_path
      else
        redirect_to root_path
      end
    else
      flash[:success] = "Invalid plan for that user"
      redirect_path = subscribed_params[:online_class_id] ? online_class_checkout_path(id: subscribed_params[:online_class_id]) : root_path

      redirect_to redirect_path
    end
  end

  def add_tapfiliate_conversion_id_to_user
    customer_id = params["customer_id"]
    user = User.find(customer_id.to_i)
    user.update(tapfiliate_conversion_id: params["conversion_id"])
    head :no_content
  end

  def renew
    @success = true

    if @future_subscription.present?
      @success = false
      @errors = "You have an active subscription already."
    end

    if @success
      first_time_free_account = current_user.first_time_free_account?

      @subscription = if current_user.recurly_account_code.nil?
        RecurlyProxy.create_subscription(params.merge!(email: current_user.email))
      else
        @recurly.create_subscription(subscription_params)
      end

      return render("three_d_secure", locals: { token: @subscription.token }) if @subscription.try(:require_three_d_secure)

      validate_credit_card_usage if @subscription&.persisted?

      if @subscription&.persisted? and @success
        track_ga_subscription(@subscription)
        session[:track_sale] = @subscription.plan.plan_code

        manage_complimentary_subscription

        is_guest_user = current_user.guest?
        current_user.update(recurly_account_code: current_user.email, guest: false) if current_user.recurly_account_code.nil?

        create_intercom_event(user_id: current_user.app_id, event_name: "subscribed-to-plan")

        current_user.update_personal_data(is_guest_user)

        current_user.update_subscription_data
        update_ondemand_data(current_user)

        current_user.billing_info.update_fields if current_user.billing_info.present? and (current_user.billing_info.payment_method != params[:payment_method] or params[:use_new_card] == "yes")

        session.delete(:shopping_cart)

        flash[:notice] = "Subscription created successfully." if !(RecurlyProxy::ADMISSIONS_PLAN_CODES.include?(@subscription.plan.plan_code) or RecurlyProxy::TUTORING_PLAN_CODES.include?(@subscription.plan.plan_code))


        @redirect_url = first_time_free_account ? root_path : manage_subscriptions_path
      else
        @success = false
        errors = ((current_user.guest? || first_time_free_account) ? (@subscription.errors || @subscription.account.errors) : @recurly.errors)
        @errors = errors.map { |_a, b| b }.join(", ") if errors.present?
      end
    end

    respond_to do |format|
      format.js { render "subscribe" }
      format.html do
        if @success
          redirect_to @redirect_url
        else
          flash[:error] = @errors
          redirect_to new_subscription_path
        end
      end
    end
  end

  def new
    @billing_info = current_user.billing_info
    @billing_info&.update_fields if @billing_info

    @hide_monthly = @current_subscription.try(:auto_renew) && @current_subscription.state != "canceled"
  end

  def create
    plan_code = params[:subscription][:plan_code]

    return head :unprocessable_entity if free_trial_and_no_lead?(plan_code) and plan_code != RecurlyProxy::TRIAL_ONDEMAND_PLAN_CODE

    if trial_plan_codes_except_ondemand(plan_code) and Setting.for("free_trial_enabled") and !cardless_trial_test?
      UserMailer.personal_mail_dev_logs(User.new, "Trial Signup", request.env).deliver
      head :ok
    end

    if RecurlyProxy::ONLINE_CLASS_PLAN_CODE != plan_code
      @cart = Cart.load(session[:shopping_cart])

      if @cart.blank? and plan_code == Setting.for("recurly_5days_plan_code")
        @user = User.find_by_email(params[:email])
        @cart = Cart.new(user: @user, plan_code: plan_code)
      end

      return redirect_to plans_path if @cart.blank?
      return redirect_to cart_path, error: "You must confirm your email first" if @cart.require_email_confirmation? and !@cart.email_confirmed?
    end

    @success = manage_existing_account

    if @success
      @subscription = if @existing_user.present? and @existing_user.recurly_account_code.present? and !@existing_user.free_account_access? and !@existing_user.guest?
        @existing_user.send(:recurly_proxy).create_subscription(subscription_params)
      else
        RecurlyProxy.create_subscription(params)
      end

      return render("three_d_secure", locals: { token: @subscription.token }) if @subscription.try(:require_three_d_secure)

      validate_credit_card_usage if @subscription&.persisted?

      if @subscription&.persisted? and @success
        track_ga_subscription(@subscription)

        manage_complimentary_subscription

        url_params = { account_code: @subscription.account.account_code, plan: @subscription.plan_code }
        url_params.merge!(is_going_to_apply_next_mlt_cycle: params[:is_going_to_apply_next_mlt_cycle]) if params[:is_going_to_apply_next_mlt_cycle].present?

        url_params.merge!(business_school_admission_application: params[:business_school_admission_application]) if params[:business_school_admission_application].present?

        url_params[:online_class_id] = params[:online_class_id] if params[:online_class_id].present?
        url_params[:host] = Setting.for("ttp_master_server_url") if Setting.for("ttp_api_enabled")

        @redirect_url = subscribed_path(url_params)

        session.delete(:shopping_cart)

        sign_out current_user if current_user

        create_intercom_event(@existing_user, user_id: @existing_user.app_id, event_name: "subscribed-to-plan") if @existing_user.present?
      else
        @success = false

        if @subscription.errors.present?
          @errors = @subscription.errors.map do |attribute, error|
            if attribute.to_s == "account" and error == ["is invalid"]
              t("user.subscriptions.existing_account_error", email: @existing_user.email)
            elsif attribute.to_s == "base"
              error
            else
              "#{attribute.to_s.humanize} #{error.first}"
            end
          end.compact.join(", ")
        end
      end
    end

    respond_to do |format|
      format.js { render :subscribe }

      format.html do
        if @success
          redirect_to @redirect_url
        else
          flash[:error] = @errors
          redirect_url = params[:online_class_id].present? ? online_class_checkout_path(id: params[:online_class_id]) : new_subscription_path
          redirect_to redirect_url
        end
      end
    end
  end

  def verify_coupon
    recurly = RecurlyProxy.new(User.new(email: params[:email]))
    recurly = current_user.send(:recurly_proxy) if current_user.present? and current_user.recurly_account_code.present?
    @discount = recurly.calculate_discount(params[:plan], params[:coupon_code], true, false, params[:quantity].to_i)
  end

  def create_trial_user
    if (@user = User.find_by(email: params[:user][:email]))&.free_diagnostic_user?
      free_diagnostic_user = @user
      RecurlyProxy.create_recurly_account_and_free_subscription(@user)

      @user.update_personal_data
    else
      @user = User.new(trial_user_params.merge(session_identifier: experiment_user_key))
    end

    if duplicate_free_trial_user_exists and !free_diagnostic_user
      @user.errors.add(:base, t("user.subscriptions.only_one_free_trial_user_allowed"))
    else
      return unless @user.save

      plan_code = params[:plan_code] || "5daystrial"

      cart = Cart.new(user: @user, plan_code: plan_code)
      session[:shopping_cart] = Cart.dump(cart)

      User::Referral.track(session[:referrer_id], @user.id)
      track_utm_event_for(@user, "sign-up")

      session.delete(:referrer_id)

      PossibleDuplicate.delay.detect_possible_duplicates(@user)

      @user.update(confirmed_at: nil)
      @user.resend_confirmation_instructions
    end
  end

  def update_trial_user
    @user = current_user

    if current_user.on_trial? or current_user.about_to_expire? or current_user.valid_membership?
      @user.errors.add(:base, t("user.subscriptions.already_logged_in"))
    elsif @user.current_or_previous_plan_is_a_trial?
      @user.errors.add(:base, t("user.subscriptions.multiple_trial_error"))
    elsif current_user.free_account_access?
      track_ga_subscription(RecurlyProxy.create_recurly_account_and_free_subscription(current_user))
    end
  end

  def enable_free_trial
    if Setting.for("free_trial_enabled") and current_user.trial_available?
      subscription = RecurlyProxy.create_recurly_account_and_free_subscription(current_user)
      track_ga_subscription(subscription)
    end

    redirect_to root_path
  end

  private

  def duplicate_free_trial_user_exists
    return false if @user.email.ends_with?("@targettestprep.com")

    User.where(session_identifier: @user.session_identifier).where("created_at > ?", 3.months.ago).any?
  end

  def subscribed_params
    params.permit(:account_code, :plan, :first_name, :last_name, :online_class_id)
  end

  def subscription_params
    additional_params = {
      currency: "USD",
      account: {
        account_code: current_user&.recurly_account_code || @existing_user.recurly_account_code,
        billing_info: {}
      }
    }

    if @existing_user and !@existing_user.confirmed?
      additional_params[:account][:account_code] ||= params[:email]
      additional_params[:account][:email] ||= params[:email]
      additional_params[:account][:first_name] ||= params[:first_name]
      additional_params[:account][:last_name] ||= params[:last_name]
    end

    additional_params[:account][:billing_info][:three_d_secure_action_result_token_id] = params[:three_d_secure_action_result_token_id] if params[:three_d_secure_action_result_token_id].present?
    additional_params[:account][:billing_info][:token_id] = params[:recurly_token] if params[:recurly_token].present? and params[:use_new_card] == "yes"
    additional_params.merge!(quantity: params[:quantity]) if params[:quantity].present?
    params.require(:subscription).permit(:plan_code, :coupon_code).merge!(additional_params)
  end

  def manage_url(login_token)
    recurly_domain_url("account/#{login_token}")
  end

  def recurly_domain_url(path)
    "https://#{Setting.for('recurly_subdomain')}.recurly.com/" + path
  end

  def set_variables
    @recurly = current_user.send(:recurly_proxy)
    @current_subscription = current_user.current_subscription
    @future_subscription = current_user.future_subscription
    @current_plan = @current_subscription ? @current_subscription.plan : nil
    @future_plan = @future_subscription ? @future_subscription.plan : nil
    @current_date_label = if !@current_subscription.try(:auto_renew) or @current_subscription.state == "canceled" or @future_subscription.present?
      "ends_on"
    else
      "next_end"
    end
  end

  def set_online_class
    @online_class = OnlineClass.current_class(current_user)
  end

  def update_ondemand_data(user, plan_code = nil)
    return unless Setting.for("ondemand_enabled")

    user.update(ondemand_enabled: true) if user.ondemand_subscription? or subscribed_params[:online_class_id] or plan_code == Setting.for("recurly_5days_ondemand_plan_code")
  end

  def manage_existing_account
    if Rails.env.production?
      if params[:email].include?("+") && !params[:email].include?("@targettestprep.com")
        @errors = t("user.subscriptions.email_contains_plus_sign_error")
        return false
      else
        params[:email].downcase!
      end
    end

    plan_code_included = RecurlyProxy::TRIAL_PLAN_CODES.include?(params[:subscription][:plan_code])
    @existing_user = User.find_by(email: params[:email])
    if @existing_user
      if @existing_user.active? && !@existing_user.on_trial? && !@existing_user.free_diagnostic_user?
        @errors = t("user.subscriptions.existing_subscription_error", email: @existing_user.email)
      elsif @existing_user.current_or_previous_plan_is_a_trial? && plan_code_included
        @errors = t("user.subscriptions.multiple_trial_error")
      end
    end

    @errors.nil?
  end

  def manage_complimentary_subscription
    complimentary_subscription = RecurlyProxy.create_complimentary_subscription(@subscription)
    @subscription = complimentary_subscription if complimentary_subscription.present?
  end

  def eligible_for_multiple_trials?
    params[:email].include?("@targettestprep.com") or Setting.for("recurly_subdomain") == "ttptest"
  end

  def validate_credit_card_usage
    was_credit_card_used_in_last_6months = CreditCardDetail.credit_used_in_last_6months?(@subscription.account.billing_info)
    is_trial_plan_code = RecurlyProxy::TRIAL_PLAN_CODES.include?(params[:subscription][:plan_code])
    is_amex_card = @subscription.account.billing_info&.card_type == "American Express"

    if !is_amex_card and was_credit_card_used_in_last_6months and !eligible_for_multiple_trials? and is_trial_plan_code
      @subscription.terminate
      @success = false
      @errors = t("user.subscriptions.duplicate_credit_card_error")
    end
  end

  def trial_user_params
    params.require(:user)
      .permit(:first_name, :last_name)
      .merge(email: params[:user][:email]&.downcase, email_confirmation: params[:user][:email_confirmation]&.downcase)
  end

  def free_trial_and_no_lead?(plan_code)
    return false if User.find_by(email: params[:email]&.downcase) or current_user

    RecurlyProxy::TRIAL_PLAN_CODES.include?(plan_code) and !Lead.find_by(email: params[:email]&.downcase)
  end

  def trial_plan_codes_except_ondemand(plan_code)
    (RecurlyProxy::TRIAL_PLAN_CODES - [RecurlyProxy::TRIAL_ONDEMAND_PLAN_CODE]).include?(plan_code)
  end
end
