class ProblemSolvingController < ApplicationController
  before_action :set_variables, only: %i(ask answer pause resume show_review_section hide_review_section show_solution end_intermediate_section review_center)
  before_action :authenticate_user!, unless: :guest_evaluation?
  before_action :verify_user_session!, except: :answer, unless: :guest_evaluation?
  before_action :verify_user_session_for_answer!, only: :answer, unless: :guest_evaluation?
  before_action :authorize_user!
  before_action :authorize_user_for_evaluation!, only: %i(ask answer show_solution)
  before_action :load_evaluation, only: %i(final_page finish)
  skip_after_action :intercom_rails_auto_include, raise: false, only: %i(ask answer)

  def ask
    @problem_attempt.update_start unless @problem_attempt.answer? and adaptive_evaluation?
    authorize! :read, @problem unless adaptive_evaluation?

    @show_fullscreen_button = true
    @hide_navbar = true

    @navigator = ::ProblemNavigatorAdapter.new(evaluation: @evaluation, evaluation_attempt: @evaluation_attempt, problem: @problem).navigator
    @problem_flagged = Problem.flagged_by_user(current_user).exists?(@problem.id) if current_user
    @skip_questions_enabled = Setting.for("skip_questions_enabled")
    @problem_attempt_flagged = ProblemAttempt.flagged_by_user(current_user).exists?(@problem_attempt.id) if current_user
    @evaluation_problems = @navigator.problems
    @problem_attempt = ProblemAttemptPresenter.new(@problem_attempt, view_context)
    @problem_attempt.navigator = @navigator
    @review_question = params[:review_action] == "review_question" if params[:action].present?

    session[:hide_timer] = params[:hide_timer] == "true" if params[:hide_timer].present?

    respond_to do |format|
      format.html
      format.js { render :ask }
    end
  end

  def show_review_section
    @problems_ids = @navigator.problems.ids
    @problems_ids_for_review = @evaluation.is_diagnostic? ? @navigator.problems_in_section.map(&:id) : @problems_ids
    @first_question_index = @problems_ids.index(@problems_ids_for_review.first)

    respond_to do |format|
      format.js
    end
  end

  def show_solution
    authorize! :read, @problem

    @problem_attempt.solve(parsed_answer)
  end

  def answer
    authorize! :read, @problem unless adaptive_evaluation?

    solved = if params[:solve_problem] == "yes"
      @problem_attempt.solve(parsed_answer, params[:current_et].to_i)
    else
      true
    end

    @evaluation_attempt.reload

    return redirect_to problem_solving_ask_path(problem_attempt_id: @problem_attempt, evaluation_attempt_id: @evaluation_attempt) if !solved and !Setting.for("skip_questions_enabled")

    return finish if params[:exit_and_score] == "yes" or (EXAM_NAME == "sat" and params[:end_review] == "yes")

    if params[:navigate_to_question] == "yes"
      next_problem_attempt = @evaluation_attempt
        .problem_attempts
        .find_or_create_by(problem_id: params[:navigate_to_question_id].squish)

        if params[:is_review_page] == "yes"
          redirect_to show_review_section_path(@evaluation_attempt, next_problem_attempt)
        else
          redirect_to problem_solving_ask_path(problem_attempt_id: next_problem_attempt, evaluation_attempt_id: @evaluation_attempt)
        end

      return
    end

    next_problem_attempt = calculate_next_url

    @evaluation_attempt.update!(start_time: Time.now, total_seconds_paused: 0) if adaptive_evaluation? and @is_the_last_one_of_intermediate_section

    respond_to do |format|
      format.html { redirect_to @next_url }
      format.js do
        if @is_review_question or @is_the_last_one or @is_the_last_one_of_intermediate_section
          render js: "window.location.replace('#{@next_url}');"
        else
          @problem_attempt = next_problem_attempt
          @problem = @problem_attempt.problem
          ask
        end
      end
    end
  end

  def pause
    @problem_attempt.pause!
    evaluation = @problem_attempt.evaluation_attempt.evaluation

    respond_to do |format|
      format.html do
        if evaluation.is_diagnostic? or evaluation.is_a?(AdaptiveEvaluation)
          if session[:diagnostic_from_welcome]
            redirect_to study_plan_path
          elsif !user_signed_in?
            redirect_to root_path
          else
            redirect_to diagnostic_list_path
          end
        elsif evaluation.on_target_test?
          redirect_to practice_by_topic_analytics_path(topic_id: evaluation.practice_topic_id)
        elsif evaluation.is_a?(UserEvaluation) and evaluation.is_study_plan_test?
          redirect_to study_plan_path
        elsif evaluation.is_a?(UserEvaluation) and evaluation.is_homework_task_test?
          redirect_to dashboard_next_liveteach_class_path(session_id: evaluation.homework_task.homework_group.online_class_session_id)
        elsif evaluation.is_a?(UserEvaluation)
          redirect_to custom_evaluations_path
        elsif evaluation.is_a?(PersonalizedEvaluation)
          redirect_to personalized_evaluations_path(section: evaluation&.chapter&.section, subsection: evaluation.chapter&.subsection)
        else
          redirect_to evaluations_path(section: evaluation.chapter&.section, subsection: evaluation.chapter&.subsection)
        end
      end

      format.js { head :ok }
      format.json { head :ok } # TODO: Remove when no json requests are being made
    end
  end

  def resume
    @problem_attempt.resume!

    head :ok
  end

  def final_page
    @show_fullscreen_button = true
    @hide_navbar = true

    @problem_attempt = @evaluation_attempt.problem_attempts.last
    @navigator = ::ProblemNavigatorAdapter.new(evaluation: @evaluation_attempt.evaluation, evaluation_attempt: @evaluation_attempt, problem: @problem_attempt.problem).navigator
  end

  def end_intermediate_section
    @problem_attempt.pause!
    @show_fullscreen_button = true
    @hide_navbar = true
  end

  def review_center
    @problems_ids_for_review = (@evaluation.custom_test? or @evaluation.is_diagnostic?) ? @navigator.problems_in_section.map(&:id) : @navigator.problems.ids

    @first_question_index = @navigator.problems.ids.index(@problems_ids_for_review.first)

    @show_fullscreen_button = true
    @hide_navbar = true
  end

  def finish
    @evaluation_attempt.finish!
    redirect_to evaluation_attempt_path(@evaluation_attempt.id)
  end

  private

  def calculate_next_url
    @is_the_last_one = false

    if @navigator.show_review_center_page?(params)
      @is_review_question = true
      @next_url = problem_solving_review_center_path(@evaluation_attempt.id, problem_attempt_id: @problem_attempt.id)
    elsif @navigator.last_problem? and (params[:next_question_is_forward] == "yes")
      @is_the_last_one = true

      if EXAM_NAME == "gre"
        @next_url = problem_solving_final_page_path(@evaluation_attempt.id)
      elsif EXAM_NAME == "sat"
        @is_review_question = true
        @next_url = problem_solving_review_center_path(@evaluation_attempt.id, problem_attempt_id: @problem_attempt.id)
      else
        @evaluation_attempt.finish!
        @next_url = evaluation_attempt_path(problem_solving_params[:evaluation_attempt_id])
      end
    elsif @navigator.show_intermediate_section_pause?(params[:next_question_is_forward])
      @is_the_last_one_of_intermediate_section = true
      next_problem_attempt = @navigator.next_problem_attempt(params[:next_question_is_forward])
      @next_url = problem_solving_end_intermediate_section_path(@evaluation_attempt.id, problem_attempt_id: next_problem_attempt.id)
    else
      next_problem_attempt = @navigator.next_problem_attempt(params[:next_question_is_forward])
      @next_url = problem_solving_ask_path(evaluation_attempt_id: @evaluation_attempt.id, problem_attempt_id: next_problem_attempt.id)
    end

    next_problem_attempt
  end

  def problem_solving_pause_resume_params
    params.permit(:problem_attempt_id)
  end

  def problem_solving_params
    params.permit(:evaluation_attempt_id, :problem_attempt_id, :answer, :denominator)
  end

  def parsed_answer
    if @problem.fill_in_the_blank?
      params[:answer]
    elsif @problem.fill_in_the_blank? and @problem.double_input?
      "#{problem_solving_params[:answer]}/#{problem_solving_params[:denominator]}"
    else
      params[:parsed_answer]
    end
  end

  def set_variables
    @evaluation_attempt = if current_user
      current_user.evaluation_attempts.find params[:evaluation_attempt_id]
    else
      EvaluationAttempt.from_guests.find params[:evaluation_attempt_id]
    end

    @problem_attempt = @evaluation_attempt.problem_attempts.find params[:problem_attempt_id]
    @problem = @problem_attempt.problem
    @evaluation = @evaluation_attempt.evaluation
    @navigator = ::ProblemNavigatorAdapter.new(evaluation: @evaluation, evaluation_attempt: @evaluation_attempt, problem: @problem).navigator
  end

  def verify_user_session_for_answer!
    return if current_user.has_role?("admin") or impersonating?

    @problem_attempt.pause! if current_user.session_data.present? and (current_user.session_data[:session_hash] != cookies.permanent.encrypted[:session_hash])

    verify_user_session!
  end

  def authorize_user_for_evaluation!
    return unless @evaluation_attempt.finish?

    redirect_to evaluation_attempt_path(@evaluation_attempt)
  end

  def authorize_user!
    authorize! :read, current_user if current_user.present?
  end

  def guest_evaluation?
    return false if current_user and !current_user.guest?

    EvaluationAttempt.find(params[:evaluation_attempt_id]).user.guest?
  end

  def load_evaluation
    @evaluation_attempt = if current_user
      current_user.evaluation_attempts.find params[:evaluation_attempt_id]
    else
      EvaluationAttempt.from_guests.find params[:evaluation_attempt_id]
    end
  end

  def adaptive_evaluation?
    @evaluation_attempt&.adaptive_evaluation?
  end
end
