class ContentPagesController < ApplicationController
  PUBLIC_EXAMPLES = %w(gmat_quant_challenge_question_4 gmat_quant_challenge_question_8 gmat_quant_challenge_question_1 difference_of_squares_examples gmat_verbal_challenge_question_1 gmat_verbal_challenge_question_4 gmat_quant_challenge_question_10 sample_triangle_data_sufficiency_question gmat_quant_challenge_question_2 sample_total_digits_question gmat_verbal_challenge_question_5 gmat_verbal_challenge_question_7 difference_of_squares_sample_question gmat_quant_examples gmat_verbal_challenge_question_2 gmat_quant_challenge_question_9 gmat_verbal_challenge_question_6 gmat_verbal_challenge_question_3 gmat_quant_challenge_question_5 gmat_quant_challenge_question_3 gmat_quant_challenge_question_7 gmat_ds_example gmat_quant_challenge_question_6 sample_units_digit_pattern_questions sample_gcf_and_lcm_question gre_qc_example gre_sample_units_digit_pattern_questions gre_numeric_entry_example gre_qc_example catchup_and_pass_example gre_quant_challenge difference_of_squares_examples difference_of_squares_sample_question gre_multiple_answer_example gre_ps_example gre_quant_examples gre_quant_examples_2 gre_di_example ea_verbal_challenge_question_3 ea_verbal_challenge_question_4 ea_quant_challenge_question_3 ea_quant_challenge_question_5).freeze

  before_action :set_custom_page_title, only: %i(terms_and_conditions privacy contact online_whiteboard)
  before_action :allow_robot_indexing, only: %i(testimonials plans giveaway webinar gmat_focus_score_chart_and_calculator admissions)
  before_action :set_session_for_utm_event
  before_action :skip_canonical_url, only: %i(giveaway mlt rpaa)
  before_action :show_sale_banner, only: %i(plans gmatclub_plans marketplace_plans greprepclub_plans btg_plans)
  before_action :skip_mathjax, only: %i(gmat_focus_score_chart_and_calculator plans privacy terms_and_conditions contact webinar)
  before_action :disable_redirect_banner, only: %i(plans gmatclub_plans marketplace_plans greprepclub_plans btg_plans)
  before_action :set_session_referrer_id, only: :plans
  skip_before_action :check_welcome_wizard_finished, only: %i(terms_and_conditions privacy)

  layout :set_layout

  def contact
    @form = ContactForm.new
    @success_message = ""
    @error_message = ""
  end

  def contact_send
    @form = ContactForm.new(contact_send_params)

    name_for_translation = @form.name.present? ? ", #{@form.name}" : ""

    if verify_recaptcha(model: @form)
      if @form.deliver
        @success_message = t("user.static_pages.contact_form.notifications.message_sent")
        @form = ContactForm.new
      else
        @error_message = t("user.static_pages.contact_form.notifications.message_not_sent_form_error", name: name_for_translation)
      end
    else
      @error_message = t("user.static_pages.contact_form.notifications.message_not_sent_recaptcha_error", name: name_for_translation)
    end

    respond_to do |format|
      format.html { render "contact" }
      format.js { render "contact_send" }
    end
  end

  def testimonials
    return unless EXAM_NAME == "gre"

    @custom_page_title = t("user.static_pages.plans.testimonials.meta.title", exam: EXAM_NAME.upcase)
    @custom_page_description = t("user.static_pages.plans.testimonials.meta.description", exam: EXAM_NAME.upcase)
  end

  def about; end

  def plans
    redirect_to main_app.new_subscription_path if current_user&.has_role?(:member) && !current_user.valid_membership?

    @custom_page_title = t("user.static_pages.plans.meta_data.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.plans.meta_data.description", exam: exam_name_display_title(include_exam_type: true))
    @plans_mobile_view = params[:new_plan] == "true"
  end

  def btg_plans
    render "not_found", status: 404 and return unless EXAM_NAME == "gmat"

    @custom_page_title = t("user.static_pages.btg_plans.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.btg_plans.meta.description", exam: exam_name_display_title(include_exam_type: true))
  end

  def gmatclub_plans
    render "not_found", status: 404 and return unless EXAM_NAME == "gmat"

    @custom_page_title = t("user.static_pages.gmatclub_plans.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.gmatclub_plans.meta.description", exam: exam_name_display_title(include_exam_type: true), exam_name: exam_name_display_title(include_exam_type: false))

    render "gmatclub_plans", locals: { gmatclub: true }
  end

  def marketplace_plans
    render "not_found", status: 404 and return unless EXAM_NAME == "gmat"

    @custom_page_title = t("user.static_pages.marketplace.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.marketplace.meta.description", exam: exam_name_display_title(include_exam_type: true))

    render "gmatclub_plans", locals: { gmatclub: false }
  end

  def greprepclub_plans
    render "not_found", status: 404 and return unless EXAM_NAME == "gre"

    @custom_page_title = t("user.static_pages.greprepclub_plans.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.greprepclub_plans.meta.description", exam: exam_name_display_title(include_exam_type: true))
  end

  def terms_and_conditions; end

  def privacy; end

  def giveaway
    @custom_page_title = t("user.static_pages.giveaway.meta.title", exam: exam_name_display_title(include_exam_type: true))
  end

  def welcome_inactive_user; end

  def mlt
    render "not_found", layout: "public", status: 404 and return unless only_gmat_or_gre_or_ea

    @custom_page_title = t("user.static_pages.partner_organizations.mlt.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.partner_organizations.mlt.meta.description", exam: exam_name_display_title(include_exam_type: true))
  end

  def rpaa
    render "not_found", layout: "public", status: 404 and return unless only_gmat_or_gre

    @custom_page_title = t("user.static_pages.partner_organizations.rpaa.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.partner_organizations.rpaa.meta.description", exam: exam_name_display_title(include_exam_type: true))
  end

  def webinar
    render "not_found", layout: "public", status: 404 and return unless only_gmat_or_gre

    @custom_page_title = t("user.static_pages.webinar.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.webinar.meta.description", exam: exam_name_display_title(include_exam_type: true))
  end

  def online_whiteboard; end

  def berkeley
    @custom_page_title = t("user.static_pages.partner_organizations.berkeley.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.partner_organizations.berkeley.meta.description", exam: exam_name_display_title(include_exam_type: true))
  end

  def free_practice_test
    render "not_found", status: 404
  end

  def gmat_focus_score_chart_and_calculator
    @custom_page_title = t("user.static_pages.score_chart.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.score_chart.meta.description", exam: exam_name_display_title(include_exam_type: true))

    @sections = %w(quant verbal di)
    render "not_found", status: 404 and return unless EXAM_NAME == "gmat"

    render "gmat_focus_score_chart_and_calculator"
  end

  def static_page
    @page_key = params[:static_page].gsub("-", "_")
    key = "user.static_pages.#{@page_key}"

    begin
      @static_page_content = I18n.t(key, raise: true)
      render "static_page" if PUBLIC_EXAMPLES.include?(@page_key)
    rescue I18n::MissingTranslationData
      render "not_found", status: 404
    end
  end

  def forte
    render "not_found", layout: "public", status: 404 and return unless only_gmat_or_gre_or_ea
  end

  def admissions
    render "not_found", layout: "public", status: 404 and return unless only_gmat_or_gre_or_ea

    @custom_page_title = t("user.static_pages.plans.admissions.meta.title")
    @custom_page_description = t("user.static_pages.plans.admissions.meta.description")
  end

  def ondemand
    return render "not_found", layout: "public", status: 404 if !%w(gmat ea).include?(EXAM_NAME) or !Setting.for("ondemand_enabled")

    @custom_page_title = t("user.static_pages.ondemand.meta.title", exam: exam_name_display_title(include_exam_type: true))
    @custom_page_description = t("user.static_pages.ondemand.meta.content", exam: exam_name_display_title(include_exam_type: true))
    @allow_robot_indexing = true
  end

  def ondemand_preview_video
    case params[:video_section]
    when "quant"
      @video_id = "https://player.vimeo.com/video/1012432724"
      @video_title = "Probability"
    when "verbal"
      @video_id = "https://player.vimeo.com/video/1011435781"
      @video_title = "Weaken the Argument"
    when "di"
      @video_id = "https://player.vimeo.com/video/1013147636"
      @video_title = "Graphics Interpretation"
    when "ondemand-section"
      @video_id = "https://player.vimeo.com/video/1043723404"
      @video_title = "What is TTP OnDemand?"
    end
  end

  def free_admission_consultation
    unless verify_recaptcha
      redirect_to admissions_path, alert: "There was an error with the reCAPTCHA. Please try again."
      return
    end

    email = params[:email]

    Lead.create(first_name: params[:name], email: email, referred_by: "admissions") unless lead_or_user_exists_by_contact_email?(email)

    if (user = User.find_by_email(params[:email]))
      user.update_columns(mba_admission: true, goal_matriculation_year: params[:plan_to_start])
    end

    UserMailer.free_admissions_consultation(params).deliver_now

    redirect_to admissions_path, notice: "Your free consultation request was received successfully!"
  end

  private

  def lead_or_user_exists_by_contact_email?(email)
    Lead.exists?(email: email) or User.exists?(email: email)
  end

  def set_session_referrer_id
    if params[:referral_code].present? and session[:referrer_id].blank?
      session[:referrer_id] = begin
        Base64.urlsafe_decode64(params[:referral_code])
      rescue StandardError => e
        Rails.logger.error e.message
        nil
      end
    end

    session.delete(:referrer_id) if current_user&.id.to_i == session[:referrer_id].to_i
  end

  def set_layout
    case params[:action]
    when "online_whiteboard", "gmat_focus_score_chart_and_calculator", "plans", "gmatclub_plans", "marketplace_plans", "greprepclub_plans", "btg_plans", "mlt", "rpaa",
      "privacy", "terms_and_conditions", "contact", "coming_soon", "webinar", "forte", "berkeley", "admissions"
      "public"
    when "testimonials", "giveaway"
      "homepage"
    else
      current_user ? "application" : "public"
    end
  end

  def skip_page_title
    @skip_page_title = true
  end

  def skip_canonical_url
    @skip_canonical_url = true
  end

  def set_custom_page_title
    @custom_page_title = "#{t("user.content_pages.#{action_name}.page_title")} - #{t('user.content_pages.common_title', exam: EXAM_NAME.upcase)}"
  end

  def allow_robot_indexing
    @allow_robot_indexing = true
  end

  def contact_send_params
    params.require(:contact_form).permit(:name, :lastname, :message, :phone, :email)
  end

  def show_sale_banner
    @show_sale_banner = true
  end

  def disable_redirect_banner
    @disable_redirect_banner = true
  end

  def skip_mathjax
    @skip_mathjax = true
  end

  def only_gmat_or_gre
    %w(gmat gre).include? EXAM_NAME
  end

  def only_gmat_or_gre_or_ea
    %w(gmat gre ea).include? EXAM_NAME
  end

  def exam_name_display_title(include_exam_type: false)
    EXAM_NAME.upcase
  end
end
