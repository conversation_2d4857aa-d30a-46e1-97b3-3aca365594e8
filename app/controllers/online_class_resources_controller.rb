class OnlineClassResourcesController < ApplicationController
  before_action :authenticate_user!
  before_action :verify_user_session!
  before_action :set_back_to_param, only: %i(library cohort_video_repository)
  before_action :user_with_online_class_access?

  def library
    @online_classes_session = OnlineClassSession.next_session(current_user)
    sessions_with_recording = OnlineClassSession.with_recording
    @videos_by_instructor = []
    @videos_by_instructor = sessions_with_recording
    @instructors = Instructor.where(id: OnlineClass.where(id: sessions_with_recording.select(:online_class_id)).select(:instructor_id)).uniq

    respond_to do |format|
      format.html
      format.js
    end
  end

  def cohort_video_repository
    @instructor = Instructor.find_by(id: params[:instructor_id])

    return render "content_pages/not_found" unless @instructor.present?

    @online_class_sessions = OnlineClassSession.with_recording.where(online_class_id: @instructor.online_classes.ids).sort_by { |session| session[:date] }
  end

  private

  def set_back_to_param
    @back_to_params = params[:back_to].presence || (request.referer&.include?("back_to") ? URI.decode_www_form(URI.parse(request.referer).query || "").assoc("back_to")&.last : "")
  end

  def user_with_online_class_access?
    render "content_pages/not_found" unless current_user.access_online_class?
  end
end
