module EvaluationCommon
  private

  def start_evaluation(evaluation, evaluation_restart = false)
    params[:time_per_question] ||= session.delete(:time_per_question)

    evaluation_starter = EvaluationStarter.call(
      current_user, evaluation, params[:time_per_question], impersonating?, evaluation_restart
    )

    redirect_to problem_solving_ask_path(
      evaluation_starter.evaluation_attempt, evaluation_starter.problem_attempt
    )
  end
end
