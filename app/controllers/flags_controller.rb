class FlagsController < ApplicationController
  before_action :authenticate_user!, unless: :guest_evaluation?
  before_action :verify_user_session!, unless: :guest_evaluation?
  before_action :authorize_user!, unless: :guest_evaluation?
  before_action :set_chapter, only: %i(chapter details)
  before_action :set_instructor, only: :liveteach_bookmark_details
  before_action :set_flaggable_type, only: %i(details index liveteach_bookmark_details)
  before_action :set_facade, only: %i(chapter_list chapter index details notes chapter_notes liveteach_bookmark_details)
  before_action :set_flaggable_object, only: %i(toggle_flag flag)

  def chapter_list; end

  def chapter; end

  def index
    @flag_type = @flaggable_type

    @flag_type = :topic if @flaggable_type == "concept_mastery" and current_section == "verbal"
  end

  def details
    @flags = @facade.flags_by_chapter_and_type(@chapter, @flaggable_type)

    @back_path =
      case params[:from]
      when "notes"
        notes_flags_path(section: current_section, subsection: current_subsection)
      when "bookmarks"
        flags_path(section: current_section, flaggable_type: @flaggable_type, subsection: current_subsection)
      else
        chapter_flags_path(section: @chapter.section, chapter_id: @chapter)
      end
  end

  def liveteach_bookmark_details
    @flags = @facade.flags_by_instructor(@instructor)

    @back_path =
      case params[:from]
      when "notes"
        notes_flags_path(section: current_section, subsection: current_subsection)
      when "bookmarks"
        flags_path(section: current_section, flaggable_type: @flaggable_type, subsection: current_subsection)
      else
        chapter_flags_path(section: @chapter.section, chapter_id: @chapter)
      end
  end

  def notes; end

  def toggle_flag
    label = params[:label]
    if label.nil?
      render json: {
        status: "error",
        message: t("toggle_tag_bad_parameters")
      }
    end

    type = if @object.is_a?(UserCalendarTask)
      Flag.toggle(user, @object.study_module_item, label) ? "flag" : "unflag"
    else
      Flag.toggle(user, @object, label) ? "flag" : "unflag"
    end

    if label == "review"
      create_intercom_event(
        user_id: user.app_id,
        event_name: (type == "flag" ? "bookmarked" : "removed-bookmark").to_s,
        bookmarkable_type: @object.class.to_s,
        bookmarkable_id: @object.id
      )
    end

    response = { status: "ok", message: t("user.lessons.#{type}_success") }

    if @object.is_a?(Lesson)
      completion = @object.chapter.completion_for(user)
      response[:percentage] = completion[:percentage]
      response[:progress] = "#{completion[:completed]} / #{completion[:total]}"
      response[:label] = label
      response[:type] = type
    end

    response[:next_item_id] = user.mission_study_plan_completion.next_item(flag: "controller").id if @object.is_a?(StudyModuleItem)
    response[:next_task_id] = user.calendar_study_plan_completion.next_task.id if @object.is_a?(UserCalendarTask)

    if @object.is_a?(HomeworkTask)
      next_homework_task = find_next_homework_task(@object)
      response[:next_task_id] = next_homework_task&.id
    end

    render json: response
  end

  def flag
    Flag.flag(current_user, @object, params[:label])
    respond_to do |format|
      format.html { redirect_to(params[:label] == "skipped_personalized_evaluation" ? study_plan_path : :back) }
      format.js
    end
  end

  def destroy
    @flag = current_user.flags.where(
      flaggable_type: params[:type],
      flaggable_id: params[:id],
      label: params[:label]
    ).first

    return unless @flag

    @flag.destroy
    set_facade
    @chapter = @flag.flaggable.try(:chapter)
    @instructor = @flag.flaggable.try(:online_class).try(:instructor)
    @flaggable_type = @flag.flaggable.class.name.underscore
    @flags_count = if @flaggable_type == "online_class_session"
      @facade.flags_count_by_instructor(@instructor)
    else
      @facade.flags_count_by_chapter_and_type(@chapter, @flaggable_type)
    end
  end

  private

  def set_flaggable_object
    id = params[:id]
    @object =
      case params[:type]
      when "lesson", "study_module_item",
        "video", "example",
        "problem", "must_know",
        "chapter", "concept_mastery",
        "user_calendar_task", "online_class_session", "homework_task"
        params[:type].classify.constantize.find(id)
      when "user" then current_user
      else ProblemAttempt.find(id)
      end
  end

  def set_chapter
    @chapter = Chapter.in_track(current_user, current_section).find(params[:chapter_id])
  end

  def set_instructor
    @instructor = Instructor.find(params[:instructor_id])
  end

  def set_facade
    @facade = FlagsFacade.new(current_user, current_section)
  end

  def set_flaggable_type
    @flaggable_type = params[:flaggable_type]
  end

  def authorize_user!
    user = guest_evaluation? ? @problem_attempt.user : current_user

    authorize! :read, user
  end

  def guest_evaluation?
    return false if current_user or params[:type] != "problem_attempt"

    @problem_attempt = ProblemAttempt.find(params[:id])
    @problem_attempt.user.guest?
  end

  def find_next_homework_task(current_task)
    current_session = current_task.homework_group.online_class_session
    all_tasks = current_session.homework_groups.includes(:homework_tasks).flat_map(&:homework_tasks)
    current_index = all_tasks.find_index(current_task)
    return nil unless current_index

    all_tasks.find do |task|
      completion = task.generate_completion_for(user)
      completion[:percentage] < 100
    end
  end

  def user
    @user ||= guest_evaluation? ? @problem_attempt.user : current_user
  end
end
