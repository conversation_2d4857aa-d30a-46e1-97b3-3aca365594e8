class HomeworkTaskTakeawaysController < ApplicationController
  before_action :authenticate_user!
  before_action :verify_user_session!

  def create
    homework_task = HomeworkTask.find_by(id: task_takeaways_params[:homework_task_id])
    @takeaway = current_user.homework_task_takeaways.find_or_initialize_by(homework_task_id: homework_task.id)
    @takeaway.assign_attributes(task_takeaways_params)
    @success = @takeaway.save

    respond_to do |format|
      format.js
    end
  end

  private

  def task_takeaways_params
    params.require(:homework_task_takeaway).permit(:homework_task_id, :content)
  end
end
