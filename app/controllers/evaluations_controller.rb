class EvaluationsController < ApplicationController
  include <PERSON><PERSON><PERSON>mon

  before_action :authenticate_user!, unless: :guest_evaluation?
  before_action :verify_user_session!, unless: :guest_evaluation?
  before_action :authorize_user!
  before_action :authorize_module_access!, only: %w(index custom custom_test create_custom_test create_chapter_test)
  before_action :set_evaluations_vars, only: %i(index custom)

  def start
    evaluation = Evaluation.find(start_params[:id])
    authorize! :read, evaluation
    session[:diagnostic_from_welcome] = params[:diagnostic_from_welcome].present?

    start_evaluation(evaluation, params[:evaluation_restart])
  end

  def continue
    session[:diagnostic_from_welcome] = false
    load_and_authorize_evaluation_attempt

    @evaluation_attempt.continue

    redirect_to continue_url
  end

  def mark_current_section_as_timed_out
    load_and_authorize_evaluation_attempt

    @background_task = BackgroundTask.find_by(evaluation_attempt: @evaluation_attempt, name: :mark_current_section_as_timed_out, status: :in_progress)

    if @background_task.nil?
      @background_task = BackgroundTask.create!(evaluation_attempt: @evaluation_attempt, name: :mark_current_section_as_timed_out, status: :in_progress)
      @background_task.delay.mark_evaluation_attempt_current_section_as_timed_out
    end

    respond_to do |format|
      format.json do
        render json: { background_task_id: @background_task.id, background_task_status: @background_task.status, background_task_url: background_task_path(@background_task) }
      end
    end
  end

  def index
    return redirect_to evaluations_path(testable_sections.first) unless testable_sections.include?(current_section)

    @evaluations_facade = EvaluationsFacade.new(current_user, current_section)
  end

  def personalized
    @facade = Evaluations::PersonalizedEvaluationsFacade.new(current_user, current_section)
  end

  def custom
    @facade = Evaluations::CustomEvaluationsFacade.new(current_user)
  end

  def custom_test
    @facade = Evaluations::CustomTestFacade.new(current_user)
    @custom_test = current_user.evaluations.build(custom_test_params)
  end

  def create_custom_test
    @custom_test = current_user.evaluations.build(custom_test_params)

    if @custom_test.save
      if params[:save_only] == "true"
        flash[:notice] = "The test has been successfully saved"
        redirect_to custom_evaluations_path
      else
        start_evaluation(@custom_test)
      end
    else
      @custom_test.errors.full_messages.each_with_index do |message, index|
        flash[index.to_s] = message
      end
      redirect_to new_custom_test_path
    end
  end

  def create_personalized_test
    @personalized_evaluation = current_user.personalized_evaluations.create(personalized_evaluation_params)

    if @personalized_evaluation.persisted?
      start_evaluation(@personalized_evaluation)
    else
      redirect_to study_plan_path, alert: "Test could not be created. Please contact support if the problem persists."
    end
  end

  def create_chapter_test
    @chapter_test = ChapterEvaluation.create(
      user: current_user, chapter_id: params[:chapter_id], level: params[:level]
    )

    if @chapter_test.persisted?
      start_evaluation(@chapter_test)
    else
      redirect_to evaluations_path(section: current_section),
        alert: "Test could not be created. Please contact support if the problem persists."
    end
  end

  def create_study_module_item_test
    study_module_item = StudyModuleItem.find(params[:id])
    evaluation, evaluation_attempt = study_module_item.find_evaluation_for current_user, true

    if evaluation_attempt.present?
      redirect_to evaluation_continue_path(evaluation_attempt)
    elsif evaluation.persisted?
      start_evaluation(evaluation)
    else
      redirect_to study_plan_path, alert: "Test could not be created. Please contact support if the problem persists."
    end
  end

  def create_homework_custom_test
    session_homework = HomeworkTask.find_by(id: params[:session_homework_id])
    online_class_session = session_homework&.homework_group&.online_class_session
    return redirect_to_class_session_dashboard(online_class_session) unless session_homework&.custom_test?

    custom_test = build_custom_test_homework(session_homework, online_class_session)

    custom_test.problems = Problem.where(id: session_homework.data).order(Arel.sql("CASE exercises.id #{session_homework.data.map.with_index { |id, index| "WHEN #{id} THEN #{index}" }.join(' ')} END"))

    if custom_test.save
      start_evaluation(custom_test)
    else
      redirect_to_class_session_dashboard(online_class_session, "Test could not be created. Please contact support if the problem persists.")
    end
  end

  def create_diagnostic_test
    return redirect_to diagnostic_list_path, alert: "Test could not be created. Diagnostic already exists." if current_user.diagnostics_for_current_course.any? and !current_user.track.for_legacy_course

    test_order = params[:test_order]&.split(",") || %w(verbal quant)

    diagnostic = if current_user.track.for_legacy_course
      AdaptiveEvaluation.get_cat_for(track: current_user.track, test_order: test_order, user: current_user)
    else
      UserEvaluation.get_diagnostic_for(current_user.track, test_order, current_user)
    end

    if diagnostic.persisted?
      start_evaluation(diagnostic)
    else
      redirect_to diagnostic_list_path, alert: "Test could not be created. Please contact support if the problem persists."
    end
  end

  def diagnostic_list
    @facade = if current_user.track.for_legacy_course
      Evaluations::AdaptiveEvaluationsFacade.new(current_user)
    else
      Evaluations::DiagnosticEvaluationsFacade.new(current_user)
    end
  end

  def destroy
    evaluation = current_user.evaluations.find params[:id]
    evaluation.destroy

    redirect_to custom_evaluations_path, notice: "Evaluation deleted successfully"
  end

  private

  def redirect_to_class_session_dashboard(online_class_session, alert_message = "Homework tasks not found.")
    redirect_to dashboard_next_liveteach_class_path(session_id: online_class_session&.id), alert: alert_message
  end

  def build_custom_test_homework(session_homework, online_class_session)
    session_date = Time.zone.parse(online_class_session.date.to_s).strftime("%b %-d %Y")

    UserEvaluation.new(
      user: current_user,
      name: "LiveTeach #{session_date} (#{online_class_session.id}) - Homework",
      number_of_problems: session_homework.data.count,
      time_per_question: params[:time_per_question] || Evaluation.exam_time_per_question,
      homework_task_id: session_homework.id
    )
  end

  def start_params
    params.permit(:id, :time_per_question)
  end

  def for_chapter_by_level_params
    params.permit(:level, :id)
  end

  def custom_test_params
    return unless params[:user_evaluation].present?

    params[:user_evaluation][:test_order] = params[:user_evaluation][:test_order].blank? ? [params[:user_evaluation][:chapter_levels].keys.first] : params[:user_evaluation][:test_order].split(",")

    params.require(:user_evaluation).permit(
      :name, :time_per_question, :is_study_plan_test, :test_mode,
      chapter_levels: params[:user_evaluation][:chapter_levels].keys.map { |chapter_id| { chapter_id => params[:user_evaluation][:chapter_levels][chapter_id].keys.map { |level| { level => [] } } } },
      number_of_problems: params[:user_evaluation][:number_of_problems].keys,
      problems_filtered_by: params[:user_evaluation][:problems_filtered_by].keys,
      test_order: []
    )
  end

  def personalized_evaluation_params
    params.require(:personalized_evaluation).permit(
      :time_per_question, :chapter_id, number_of_problems: params[:personalized_evaluation][:number_of_problems].keys
    )
  end

  def authorize_user!
    return unless current_user.present?

    authorize! :read, current_user
  end

  def authorize_module_access!
    authorize! :read, Chapter
  end

  def load_and_authorize_evaluation_attempt
    @evaluation_attempt = if current_user && !current_user.guest?
      current_user.evaluation_attempts.find params[:attempt_id]
    else
      EvaluationAttempt.from_guests.find params[:attempt_id]
    end

    authorize! :read, @evaluation_attempt
  end

  def guest_evaluation?
    return false if current_user && !current_user.guest?
    return false if params[:attempt_id].blank?

    load_and_authorize_evaluation_attempt
    @evaluation_attempt.user.guest?
  end

  def set_evaluations_vars
    session[:diagnostic_from_welcome] = false
  end

  def continue_url
    next_problem = @evaluation_attempt.next_problem

    navigator = ::ProblemNavigatorAdapter.new(evaluation: @evaluation_attempt.evaluation, evaluation_attempt: @evaluation_attempt, problem: next_problem.problem).navigator

    if navigator.show_review_center_page?({ continue_exam: true }) and next_problem.answer.present?
      problem_solving_review_center_path(@evaluation_attempt, problem_attempt_id: next_problem.id)
    else
      problem_solving_ask_path(@evaluation_attempt, next_problem)
    end
  end
end
