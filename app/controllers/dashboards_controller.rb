class DashboardsController < ApplicationController
  before_action :redirect_to_password_set_page
  before_action :authenticate_user!
  before_action :verify_user_session!
  before_action :redirect_to_home_screen, if: -> { current_user.for_legacy_course }, only: :ondemand
  after_action :mark_notifications_as_read, only: :prep_toolbox, if: -> { current_user.free_account_access? }

  def prep_toolbox
    return redirect_to home_path if current_user.valid_membership?

    @messages = current_user.received_messages.not_deleted.order(created_at: :desc)
    exam_scores = current_user.exam_scores_model

    @exam_scores = exam_scores.where(for_legacy_course: current_user.for_legacy_course)
    @facade = FlashCardDecks::IndexFacade.new(current_user, current_section)
    @lesson_feedbacks = current_user.lesson_feedbacks.order(created_at: :desc)

    @facade_evalutions = if current_user.track.for_legacy_course?
      Evaluations::AdaptiveEvaluationsFacade.new(current_user)
    else
      Evaluations::DiagnosticEvaluationsFacade.new(current_user)
    end
  end

  def self_study
    return redirect_to home_path if current_user.valid_membership?
  end

  def online_classes
    return render "content_pages/not_found", status: 404 unless Setting.for("navbar_onlineclasses_enabled")

    @online_classes = current_user.online_classes
  end

  def next_liveteach_class
    @online_classes_session = OnlineClassSession.find_by(id: params[:session_id])
    return render "content_pages/not_found", status: 404 if @online_classes_session and !current_user.access_online_class?

    last_session_date = OnlineClassSession.last_session_date(current_user, @online_classes_session.online_class.id)
    @next_session_by_class = OnlineClassSession.next_session(current_user, @online_classes_session.online_class.id) if last_session_date && last_session_date + 1.day >= DateTime.current.in_time_zone(current_user.time_zone)
    @online_class_sessions = @online_classes_session.online_class.sessions.sort_by { |session| session[:date] }
    @weekly_hours = OndemandWeeklyHour.order(created_at: :asc).first
    @on_demand_weekly_hour_instructors = OndemandWeeklyHourInstructor.all
    @first_five_weekly_hour_instructors = @on_demand_weekly_hour_instructors.first(5) || nil
  end

  def admissions
    return render "content_pages/not_found", status: 404 if EXAM_NAME == "sat"
  end

  def ondemand
    return redirect_to home_path if current_user.ondemand_active? and current_user.access_ondemand?
  end

  private

  def mark_notifications_as_read
    @messages.mark_group_as_read!
  end

  def redirect_to_password_set_page
    redirect_to set_password_path if current_user.present? and !devise_controller? and current_user.encrypted_password.blank?
  end

  def redirect_to_home_screen
    redirect_to home_path
  end
end
