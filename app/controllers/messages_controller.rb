class MessagesController < ApplicationController
  before_action :authenticate_user!
  before_action :load_messages, only: %i(index destroy mark_as_read)
  load_and_authorize_resource except: %i(create index mark_as_read)

  def index; end

  def create
    @lesson_feedback = LessonFeedback.find_by(id: message_params[:lesson_feedback_id])

    @message = Message.new(message_params)
    @message.content.strip!
    @message.sender = current_user

    ActiveRecord::Base.transaction do
      @message.save!
      @lesson_feedback.unresolved!
    end
  end

  def destroy
    if params[:lesson_feedback_id].nil?
      @message.mark_as_deleted!
    else
      current_user.received_messages.where(lesson_feedback_id: params[:lesson_feedback_id]).mark_group_as_deleted!
    end
  end

  def mark_as_read
    @message = @messages.find(params[:message_id])

    return if @message.read_at.present?

    @message.mark_as_read! if current_user.valid_membership? or current_user.free_account_access?
  end

  private

  def load_messages
    @messages = current_user.received_messages.not_deleted.order(created_at: :desc)
    @new_messages = @messages.where(read_at: nil)
    @lesson_feedbacks = current_user.lesson_feedbacks.order(created_at: :desc)
  end

  def message_params
    params.require(:message).permit(:lesson_feedback_id, :content)
  end
end
