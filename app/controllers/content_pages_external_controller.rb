class ContentPagesExternalController < ApplicationController
  before_action :set_session_for_utm_event
  before_action :redirect_to_home_screen, if: -> { current_user.present? }, only: :index

  caches_action :index, expires_in: 6.hours, cache_path: :cache_path
  caches_action :mobile, expires_in: 6.hours, cache_path: :cache_path
  caches_action :score_guarantee, expires_in: 6.hours, cache_path: :cache_path
  caches_action :ondemand_score_guarantee, expires_in: 6.hours, cache_path: :cache_path
  caches_action :tutoring, expires_in: 6.hours, cache_path: :cache_path
  caches_action :about, expires_in: 6.hours, cache_path: :cache_path
  caches_action :company, expires_in: 6.hours, cache_path: :cache_path
  caches_action :liveteach, expires_in: 6.hours, cache_path: :cache_path
  caches_action :testimonials, expires_in: 6.hours, cache_path: :cache_path
  caches_action :variant, expires_in: 6.hours, cache_path: :cache_path
  caches_action :contest, expires_in: 1.hour, cache_path: :cache_path, if: -> { request.format.symbol == :html }

  layout false

  def index
    render_external_page
  end

  def mobile
    render_external_page
  end

  def score_guarantee
    render_external_page
  end

  def ondemand_score_guarantee
    render_external_page
  end

  def tutoring
    render_external_page
  end

  def about
    render_external_page
  end

  def company
    render_external_page
  end

  def liveteach
    render_external_page
  end

  def testimonials
    render_external_page
  end

  def variant
    render_external_page("v2")
  end

  def gmat_focus
    render_external_page("focus-landing")
  end

  def contest
    respond_to do |format|
      format.html { render_external_page }
      format.json
    end
  end

  private

  def fetch_document(custom_path = nil)
    path = []
    path << EXAM_NAME unless request.subdomain == "www"

    if custom_path.present?
      path << custom_path
    else
      path << action_name.gsub("_", "-") unless action_name == "index"
    end

    path << "index.html"

    doc = Nokogiri::HTML(URI.parse("#{Setting.for('external_pages_url')}/#{path.join('/')}").open)
    body = doc.css("html > body")[0]
    body.inner_html = body.inner_html + render_to_string(partial: "layouts/shared/tracking_scripts_for_public_pages") + render_internal_sale_banner

    doc
  end

  def render_internal_sale_banner
    new_sale_banner = render_to_string(partial: "components/new_sale_banner", locals: { disable_redirect_banner: true })
    render_to_string(partial: "layouts/public/banner_external", locals: { new_sale_banner: new_sale_banner })
  end

  def render_external_page(custom_path = nil)
    render html: fetch_document(custom_path).to_s.html_safe
  end

  def redirect_to_home_screen
    if current_user.free_account_access?
      redirect_to dashboards_prep_toolbox_path
    else
      redirect_to home_path
    end
  end
end
