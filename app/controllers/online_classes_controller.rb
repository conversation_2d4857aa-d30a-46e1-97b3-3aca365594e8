class OnlineClassesController < ApplicationController
  skip_before_action :check_welcome_wizard_finished, only: %i(confirm checkout)

  before_action :authenticate_user!, only: :confirm
  before_action :verify_user_session!, only: :confirm
  before_action :only_gmat_or_gre_or_ea
  before_action :bootcamp_page?

  layout :set_layout

  def index
    return render "content_pages/not_found" unless Setting.for("navbar_onlineclasses_enabled")

    @allow_robot_indexing = true
    @custom_page_title = t("user.dashboard.online_classes.index.meta_data.title", exam: Setting.for("exam_name").upcase).html_safe
    @custom_page_description = t("user.dashboard.online_classes.index.meta_data.description", exam: Setting.for("exam_name").upcase).html_safe
    # Only show LiveTeach classes (class_type = 'liveteach')

    if  @is_bootcamp_page
      @online_classes = OnlineClass.liveteach_classes.available(current_user).group_by { |online_class| online_class.first_class.strftime("%B %Y") }
      @closed_online_classes = OnlineClass.liveteach_classes.closed_classes_excluding_active_instructors(current_user).group_by { |online_class| online_class.first_class.strftime("%B %Y") }
    else 
      @online_classes = OnlineClass.liveteach_classes.available(current_user).group_by { |online_class| online_class.first_class.strftime("%B %Y") }
      @closed_online_classes = OnlineClass.liveteach_classes.closed_classes_excluding_active_instructors(current_user).group_by { |online_class| online_class.first_class.strftime("%B %Y") }
    end
  end

  def checkout
    @class_online = OnlineClass.find(params[:id])
    @class_plan_code = Setting.for("recurly_online_class_plan_code")
    @user = current_user || User.new(guest: true)
    @coupon = current_user ? current_user.available_coupon_for(@class_plan_code) : ""
    @prices = @coupon.present? ? current_user.calculate_discount(@class_plan_code, @coupon, true) : ""

    return render "content_pages/not_found" unless RecurlyProxy.valid_plan_code?(@class_plan_code)

    @billing_info = @user.billing_info
    @billing_info&.update_fields if @billing_info

    @cart = Cart.new(user: @user, plan_code: @class_plan_code)

    session[:shopping_cart] = Cart.dump(@cart)
    @submit_path = subscriptions_path(online_class_id: @class_online.id)
    @submit_path = subscribe_online_class_plan_path(online_class_id: @class_online.id) if current_user
    render "checkout"
  end

  def confirm
    redirect_to root_path and return unless current_user.available_current_onlineclass?

    online_class = OnlineClass.current_class(current_user)
    @instructor = online_class.instructor
    @start_date = online_class.first_class&.strftime("%b %-d")
    @access_date = online_class.access_starts_at(user: current_user)&.strftime("%b %-d")

    return unless session[:track_sale].present?

    @sale_value = current_user.last_transaction&.amount_in_cents.to_i / 100.00
    @transaction_id = current_user.last_transaction&.uuid
    @plan_code = session[:track_sale]

    session[:track_sale] = nil
  end

  private

  def set_layout
    case params[:action]
    when "index", "checkout"
      current_user ? "application" : "public"
    else
      "application"
    end
  end

  def bootcamp_page?
    @is_bootcamp_page = request.path.include?("/bootcamp")
  end

  def only_gmat_or_gre_or_ea
    return render "content_pages/not_found" unless EXAM_NAME == "gmat" || EXAM_NAME == "gre" || EXAM_NAME == "ea"
  end
end
