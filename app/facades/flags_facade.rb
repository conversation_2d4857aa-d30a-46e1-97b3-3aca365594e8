class FlagsFacade
  attr_reader :chapters, :instructors

  def initialize(user, section)
    @user = user
    @section = section
    @chapters = Chapter.in_track(@user, @section).ordered
    @instructors = Instructor.all
  end

  def flags_count_by(chapter, include_notes: false)
    flaggable_types(include_notes: include_notes).each_with_object({}) do |flaggable_type, result|
      result[flaggable_type] = flags_by_chapter_and_type(chapter, flaggable_type).count
    end
  end

  def flags_by(chapter, include_notes: false, limit: nil)
    flaggable_types(include_notes: include_notes).each_with_object({}) do |flaggable_type, result|
      limit_per_type = limit || Flag::SUMMARY_LIMITS[flaggable_type]
      result[flaggable_type] = flags_by_chapter_and_type(chapter, flaggable_type).limit(limit_per_type)
    end
  end

  def flags_count_by_chapter_and_type(chapter, flaggable_type)
    flags_by_chapter_and_type(chapter, flaggable_type).count
  end

  def flags_count_by_instructor(instructor)
    flags_by_instructor(instructor).count
  end

  def flags_by_instructor(instructor, limit: nil)
    bookmarked_sessions = OnlineClassSession.bookmarked_by_user(@user).where(online_class_id: instructor.online_classes.ids)

    bookmarked_sessions.limit(limit)
  end

  def flags_by_chapter_and_type(chapter, flaggable_type, limit: nil)
    case flaggable_type.to_s
    when "problem", "concept_mastery"
      chapter.send(flaggable_type.to_s.pluralize).flagged_by_user(@user).limit(limit)
    when "example"
      chapter.examples.in_track(@user, include_archived: true).flagged_by_user(@user).limit(limit)
    when "topic"
      chapter.topics.in_track(@user, include_archived: true).review_for(@user).ordered.limit(limit)
    when "must_know"
      chapter.must_knows.bookmarked_by_user(@user).limit(limit)
    when "note"
      Note.by_user(@user).by_chapter(chapter, @user).limit(limit)
    when "online_class_session"
      OnlineClassSession.bookmarked_by_user(@user).limit(limit)
    end
  end

  def notes_total_count_by_section(subsection = nil)
    chapters = @chapters.where(subsection: subsection)
    chapters.map { |chapter| flags_count_by_chapter_and_type(chapter, "note") }.sum
  end

  def container_classes
    {
      problem: "list-unstyled mb-0",
      example: "list-unstyled mb-0",
      topic: "list-unstyled mb-0",
      concept_mastery: "list-unstyled mb-0",
      must_know: "list-unstyled mb-0",
      note: "list-unstyled mb-0 row gutter-24 row-cols-xxl-3 row-cols-md-2 row-cols-1 py-4",
      online_class_session: "list-unstyled ps-0"
    }
  end

  def flaggable_types(include_notes: false)
    flaggable_types = *FLAG_TYPES_WITH_NOTE
    flaggable_types.delete(:note) unless include_notes
    flaggable_types.delete(:concept_mastery) unless Chapter.subsections_in(@section).any?
    flaggable_types
  end

  def flaggable_icon
    {
      problem: "fa-tasks",
      example: "fa-question-circle",
      concept_mastery: "fa-question-circle",
      topic: "fa-book-open",
      note: "fa-sticky-note",
      must_know: "fa-bookmark"
    }
  end
end
