module Admin
  module Problems
    class CountsFacade
      attr_reader :chapters, :chapter_id

      def initialize(chapter_id, section)
        @chapters = Chapter.testable.in_section(section).ordered.select(:id, :name)
        @chapter_id = chapter_id || @chapters.first.id
      end

      def topics
        Topic.select(:id, :name, :chapter_id).where(chapter_id: chapter_id).order(:name)
      end

      def question_types
        @question_types ||= problems.keys
          .map { |_lesson_id, _level, question_type| question_type }.uniq.sort
      end

      def problems_for(topic_id, level: nil, question_type: nil)
        problems.reduce(0) do |sum, ((p_topic_id, p_level, p_question_type), count)|
          filter = p_topic_id == topic_id && p_level == (level || p_level) &&
            p_question_type == (question_type || p_question_type)

          sum + (filter ? count : 0)
        end
      end

      def problem_levels
        { "E" => "ttp-green", "M" => "ttp-yellow", "H" => "text-danger" }
      end

      private

      def problems
        @problems ||= Problem
          .joins(:lessons)
          .where(lessons: { chapter_id: chapter_id })
          .exclude_group_parents
          .group('lessons.id', :level, :question_type)
          .count
      end
    end
  end
end
