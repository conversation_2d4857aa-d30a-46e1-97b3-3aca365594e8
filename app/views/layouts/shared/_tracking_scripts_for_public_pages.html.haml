- if Setting.for("track_analytics")
  / Google Analytics
  %script{ async: "", src: "https://www.googletagmanager.com/gtag/js?id=#{google_analytics_track_id}" }
  :javascript
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', '#{google_analytics_track_id}');

  / Google tag manager
  %noscript
    %iframe{ height: "0", src: "https://www.googletagmanager.com/ns.html?id=#{Setting.for('google_tag_manager_id')}", style: "display:none;visibility:hidden", width: "0" }

  :javascript
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','#{Setting.for("google_tag_manager_id")}');

  / Pinterest
  :javascript
    !function(e){if(!window.pintrk){window.pintrk = function () {
    window.pintrk.queue.push(Array.prototype.slice.call(arguments))};var
      n=window.pintrk;n.queue=[],n.version="3.0";var
      t=document.createElement("script");t.async=!0,t.src=e;var
      r=document.getElementsByTagName("script")[0];
      r.parentNode.insertBefore(t,r)}}("https://s.pinimg.com/ct/core.js");
    pintrk('load', '2612579507529');
    pintrk('page');
    pintrk('track', 'pagevisit');
  %noscript
    %img{ alt: "", height: "1", src: "https://ct.pinterest.com/v3/?event=init&tid=2612579507529&noscript=1", style: "display:none;", width: "1" }

- if Setting.for("marketing_scripts_enabled")
  / Quora Pixel & Conversions
  :javascript
    !function(q,e,v,n,t,s){if(q.qp) return; n=q.qp=function(){n.qp?n.qp.apply(n,arguments):n.queue.push(arguments);}; n.queue=[];t=document.createElement(e);t.async=!0;t.src=v; s=document.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s);}(window, 'script', 'https://a.quora.com/qevents.js');
    qp('init', '54ce4148931e4ed185b95281cd77e4d9');
    qp('track', 'ViewContent');

  %noscript
    %img{ height: "1", src: "https://q.quora.com/_/ad/54ce4148931e4ed185b95281cd77e4d9/pixel?tag=ViewContent&noscript=1", style: "display:none", width: "1" }

  / Tapfiliate
  %script{ async: "", src: "//static.tapfiliate.com/tapfiliate.js", type: "text/javascript" }
  :javascript
    window['TapfiliateObject'] = i = 'tap';
    window[i] = window[i] || function () {
        (window[i].q = window[i].q || []).push(arguments);
    };
    tap('create', '1273-16d10c');
    tap('detectClick');

  / Linkedin
  :javascript
    _linkedin_partner_id = "2487508";
    window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
    window._linkedin_data_partner_ids.push(_linkedin_partner_id);
  :javascript
    (function(){var s = document.getElementsByTagName("script")[0];
    var b = document.createElement("script");
    b.type = "text/javascript";b.async = true;
    b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
    s.parentNode.insertBefore(b, s);})();
  %noscript
    %img{ alt: "", height: "1", src: "https://px.ads.linkedin.com/collect/?pid=2487508&fmt=gif", style: "display:none;", width: "1" }

  / Reddit
  :javascript
    !function(w,d){if(!w.rdt){var p=w.rdt=function(){p.sendEvent?p.sendEvent.apply(p,arguments):p.callQueue.push(arguments)};p.callQueue=[];var t=d.createElement("script");t.src="https://www.redditstatic.com/ads/pixel.js",t.async=!0;var s=d.getElementsByTagName("script")[0];s.parentNode.insertBefore(t,s)}}(window,document);rdt('init','t2_ti7hd');rdt('track', 'PageVisit');

  / Bing
  :javascript
    (function(w,d,t,r,u){var f,n,i;w[u]=w[u]||[],f=function(){var o={ti:"17521052"};o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")},n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function(){var s=this.readyState;s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)},i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)})(window,document,"script","//bat.bing.com/bat.js","uetq");

  / Referral Rock
  :javascript
    (function (f, r, n, d, b, y) { b = f.createElement(r), y = f.getElementsByTagName(r)[0]; b.async = 1; b.src = n; b.id = 'RR_DIVID'; y.parentNode.insertBefore(b, y); })(document, 'script', '//targettestprep.referralrock.com/webpixel/beta/universalv03.js');

  / Shareasale
  %script{ defer: "defer", src: "https://www.dwin1.com/19038.js", type: "text/javascript" }

  - if current_user.present?
    / Snapchat
    :javascript
      (function(e,t,n){if(e.snaptr)return;var a=e.snaptr=function(){a.handleRequest?a.handleRequest.apply(a,arguments):a.queue.push(arguments)};a.queue=[];var s='script';r=t.createElement(s);r.async=!0;r.src=n;var u=t.getElementsByTagName(s)[0];u.parentNode.insertBefore(r,u);})(window,document,'https://sc-static.net/scevent.min.js');

      snaptr('init', 'aa465eee-af31-4b16-a4cd-22bc973d0a1d', {'user_email': '#{current_user.email}'});
      snaptr('track', 'PAGE_VIEW');

  / TikTok
  :javascript
    !function (w, d, t) {w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};
      ttq.load('CJBCDC3C77U2B51RR8SG');
      // ttq.page();
      ttq.track('PageView');
    }(window, document, 'ttq');

  / Attentive
  %script{ src: "https://cdn.attn.tv/ttp/dtag.js", async: true }

  / Convert
  %script{ src: "//cdn-4.convertexperiments.com/v1/js/100414095-100415503.js?environment=production" }

  / AddShoppers
  :javascript
    var AddShoppersWidgetOptions = { 'loadCss': false, 'pushResponse': false };
    (!function(){
      var t=document.createElement("script");
      t.type="text/javascript",
      t.async=!0,
      t.id="AddShoppers",
      t.src="https://shop.pe/widget/widget_async.js#6800bf9a373185a6649ca945",
      document.getElementsByTagName("head")[0].appendChild(t)
    }());

/ UserWay Accessibility plugin
%script{ src: "https://cdn.userway.org/widget.js", "data-account": "CKJXDULFVL" }