!!! 5
%html{ lang: "en-US" }
  %head
    %meta{charset: 'UTF-8'}
    %meta{content: "IE=edge", "http-equiv" => "X-UA-Compatible"}
    - unless @skip_page_title
      %title= @custom_page_title || "Target Test Prep - #{EXAM_NAME.upcase}"

    - if @custom_page_description
      %meta{ content: @custom_page_description, name: "description" }

    - unless @allow_robot_indexing
      %meta{content: "noindex", name: "robots"}
    %meta{content: "True", name: "HandheldFriendly"}
    %meta{content: "320", name: "MobileOptimized"}
    %meta{ content: "width=device-width, initial-scale=1.0, maximum-scale=2.0", name: "viewport" }
    %meta{content: "telephone=no", name: "format-detection"}
    %link{href: canonical_url, hreflang: "en-us", rel: "alternate"}

    - unless @skip_canonical_url
      %link{href: canonical_url, rel: "canonical"}

    - if %w(checkout).include?(params[:action])
      = stylesheet_link_tag "static_pages", media: "all"

    = stylesheet_link_tag "static_pages_common", media: "all"
    = render partial: "partials/typekit"
    = render partial: "partials/favicon"
    = csrf_meta_tags

    = render "layouts/shared/crazy_egg"

    = yield :additional_head_tags

    %body{ class: "#{EXAM_NAME} #{content_for :body_classes}", id: content_for(:body_id).strip }
    /[if lt IE 10]
      .browsehappy
        = image_tag("layout/logo_ie_prompt.jpg")
        %p
          You are using an
          %strong outdated browser.
          %br
          Please
          = link_to "upgrade your browser", "http://browsehappy.com/"
          to improve your experience.

    - if %w(testimonials welcome_inactive_user giveaway online_whiteboard).include?(params[:action])
      = render "layouts/public/navbar"

    - else
      = render 'layouts/guest_navbar'
      = yield :header_components

    = yield

    = render partial: "layouts/public/footer"

    = yield :bottom_links_and_scripts

    = javascript_include_tag "static_pages"

    - if Setting.for("track_analytics")
      / Google Analytics
      %script{:async => "", :src => "https://www.googletagmanager.com/gtag/js?id=#{google_analytics_track_id}"}
      :javascript
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '#{google_analytics_track_id}');

      / Google tag manager
      %noscript
        %iframe{:height => "0", :src => "https://www.googletagmanager.com/ns.html?id=#{Setting.for("google_tag_manager_id")}", :style => "display:none;visibility:hidden", :width => "0"}

      :javascript
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','#{Setting.for("google_tag_manager_id")}');

      / Pinterest
      :javascript
        !function(e){if(!window.pintrk){window.pintrk = function () {
        window.pintrk.queue.push(Array.prototype.slice.call(arguments))};var
          n=window.pintrk;n.queue=[],n.version="3.0";var
          t=document.createElement("script");t.async=!0,t.src=e;var
          r=document.getElementsByTagName("script")[0];
          r.parentNode.insertBefore(t,r)}}("https://s.pinimg.com/ct/core.js");
        pintrk('load', '2612579507529');
        pintrk('page');
        pintrk('track', 'pagevisit');
      %noscript
        %img{:alt => "", :height => "1", :src => "https://ct.pinterest.com/v3/?event=init&tid=2612579507529&noscript=1", :style => "display:none;", :width => "1"}/

    - if Setting.for("marketing_scripts_enabled")
      / Quora Pixel & Conversions
      :javascript
        !function(q,e,v,n,t,s){if(q.qp) return; n=q.qp=function(){n.qp?n.qp.apply(n,arguments):n.queue.push(arguments);}; n.queue=[];t=document.createElement(e);t.async=!0;t.src=v; s=document.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s);}(window, 'script', 'https://a.quora.com/qevents.js');
        qp('init', '54ce4148931e4ed185b95281cd77e4d9');
        qp('track', 'ViewContent');

      %noscript
        %img{:height => "1", :src => "https://q.quora.com/_/ad/54ce4148931e4ed185b95281cd77e4d9/pixel?tag=ViewContent&noscript=1", :style => "display:none", :width => "1"}/

      / Tapfiliate
      %script{ :async => "", :src => "//static.tapfiliate.com/tapfiliate.js", :type => "text/javascript" }
      :javascript
        window['TapfiliateObject'] = i = 'tap';
        window[i] = window[i] || function () {
            (window[i].q = window[i].q || []).push(arguments);
        };
        tap('create', '1273-16d10c');
        tap('detectClick');

      / Linkedin
      :javascript
        _linkedin_partner_id = "2487508";
        window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
        window._linkedin_data_partner_ids.push(_linkedin_partner_id);
      :javascript
        (function(){var s = document.getElementsByTagName("script")[0];
        var b = document.createElement("script");
        b.type = "text/javascript";b.async = true;
        b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
        s.parentNode.insertBefore(b, s);})();
      %noscript
        %img{:alt => "", :height => "1", :src => "https://px.ads.linkedin.com/collect/?pid=2487508&fmt=gif", :style => "display:none;", :width => "1"}/

      / Referral Rock
      :javascript
        (function (f, r, n, d, b, y) { b = f.createElement(r), y = f.getElementsByTagName(r)[0]; b.async = 1; b.src = n; b.id = 'RR_DIVID'; y.parentNode.insertBefore(b, y); })(document, 'script', '//targettestprep.referralrock.com/webpixel/beta/universalv03.js');

      / Shareasale
      %script{:defer => "defer", :src => "https://www.dwin1.com/19038.js", :type => "text/javascript"}

      / Reddit
      :javascript
        !function(w,d){if(!w.rdt){var p=w.rdt=function(){p.sendEvent?p.sendEvent.apply(p,arguments):p.callQueue.push(arguments)};p.callQueue=[];var t=d.createElement("script");t.src="https://www.redditstatic.com/ads/pixel.js",t.async=!0;var s=d.getElementsByTagName("script")[0];s.parentNode.insertBefore(t,s)}}(window,document);rdt('init','t2_ti7hd');rdt('track', 'PageVisit');

      / Bing
      :javascript
        (function(w,d,t,r,u){var f,n,i;w[u]=w[u]||[],f=function(){var o={ti:"17521052"};o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")},n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function(){var s=this.readyState;s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)},i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)})(window,document,"script","//bat.bing.com/bat.js","uetq");
