%nav.navbar.navbar-dark.navbar-expand-lg.fixed-top#user-navbar{ data: { fetch_message_url: messages_path } }
  - redirect_root_path = (current_user.free_account_access? and !current_user.admin?) ? dashboards_prep_toolbox_path : home_path

  .container
    = link_to redirect_root_path, class: "navbar-brand logo" do
      = image_tag(asset_path("layout/logo_arrow.svg"), alt: "TTP Logo")

    - unless @hide_navbar_menu
      .d-flex
        = link_to "#", class: "nav-link topics-search-icon d-lg-none text-white open-search-bar", aria_label: "Open Search Bar" do
          %i.far.fa-search
          %span.visually-hidden Open Search Bar

        %button.navbar-toggler.collapsed{ type: "button", data: { bs_target: "#main-navbar", bs_toggle: "collapse" } }
          %span.navbar-toggler-icon
          %span.visually-hidden Toggle Navbar

      .collapse.navbar-collapse#main-navbar
        %ul.navbar-nav.profile-menu.d-lg-none

        %ul.navbar-nav.me-auto.mb-2.mb-lg-0.align-items-lg-center.text-lg-center
          %li.nav-item{ class: ("active" if navbar_home_link_active?) }
            = link_to t("user.top_nav.logged.home"), redirect_root_path, class: "nav-link show-overlay"

          %li.nav-item.d-flex{ class: ("active" if navbar_study_plan_link_active?) }
            = link_to t("user.top_nav.logged.study_plan"), study_plan_path, class: "nav-link wrap-text #{current_user.valid_membership? ? '' : ' pe-0'}"
            - unless current_user.valid_membership?
              %i.fas.fa-lock.lock-icon

          %li.nav-item.d-flex{ class: navbar_chapters_link_active? ? "active" : "" }
            = link_to t("user.top_nav.logged.lessons"), lessons_path(section: current_section), class: "nav-link show-overlay #{current_user.valid_membership? ? '' : ' pe-0'}"
            - unless current_user.valid_membership?
              %i.fas.fa-lock.lock-icon

          %li.nav-item.dropdown
            %a.nav-link.dropdown-toggle{ href: "#", role: "button", data: { bs_toggle: "dropdown" } }
              = t("user.evaluations.list.navbar.title")

            .dropdown-menu
              .d-flex
                = link_to evaluations_path(section: current_section), class: "dropdown-item show-overlay #{'active' if navbar_chapter_tests_link_active?}" do
                  = t("user.evaluations.list.navbar.evaluations")
                  - unless current_user.valid_membership?
                    %i.fas.fa-lock.lock-icon{ class: "position-relative bottom-50" }

              .dropdown-divider

              .d-flex
                = link_to custom_evaluations_path, class: "dropdown-item show-overlay #{'active' if navbar_custom_tests_link_active?}" do
                  = t("user.evaluations.list.navbar.custom_evaluations")
                  - unless current_user.valid_membership?
                    %i.fas.fa-lock.lock-icon{ class: "position-relative bottom-50" }

              - if current_user.diagnostic.present?
                .dropdown-divider

                = link_to t("user.evaluations.list.navbar.diagnostics"), diagnostic_list_path, class: "dropdown-item show-overlay #{'active' if navbar_diagnostics_link_active?}"

              - if current_user.personalized_evaluations.any?
                .dropdown-divider

                = link_to t("user.evaluations.list.navbar.personalized_evaluations"), personalized_evaluations_path(section: "quant"), class: "dropdown-item show-overlay #{'active' if navbar_personalized_evaluations_link_active?}"

          %li.nav-item.d-flex{ class: navbar_analytics_link_active? ? "active" : "" }
            = link_to t("user.top_nav.logged.analytics"), analytics_path, class: "nav-link show-overlay #{current_user.valid_membership? ? '' : ' pe-0'}"
            - unless current_user.valid_membership?
              %i.fas.fa-lock.lock-icon

          %li.nav-item{ class: navbar_bookmarks_link_active? ? "active" : "" }
            = link_to t("user.top_nav.logged.flags"), chapter_list_flags_path(section: current_section), class: "nav-link show-overlay"

          %li.nav-item.dropdown.toolbox
            %a.nav-link.dropdown-toggle{ href: "#", role: "button", data: { bs_toggle: "dropdown" } }
              = t("user.top_nav.logged.toolbox.title")


            .dropdown-menu
              - if current_user.admin? or %w(gmat gre ea).include?(EXAM_NAME)
                = link_to t("user.flashcards.list.navbar.flashcards"), flash_card_decks_path(section: current_section), class: ["dropdown-item show-overlay", navbar_flashcard_decks_link_active? ? "active" : nil]
                .dropdown-divider

              .d-flex
                = link_to failure_reasons_path(section: current_section), class: ["dropdown-item show-overlay", navbar_error_tracker_link_active? ? "active" : nil] do
                  = t("user.top_nav.logged.toolbox.error_tracker")
                  - unless current_user.valid_membership?
                    %i.fas.fa-lock.lock-icon{ class: "position-relative bottom-50" }
              .dropdown-divider

              = link_to t("user.top_nav.logged.toolbox.test_scores_and_analyzer"), exam_scores_path, class: ["dropdown-item show-overlay", navbar_scores_link_active? ? "active" : nil]
              .dropdown-divider

              = link_to t("user.top_nav.logged.toolbox.resources"), resources_path, class: ["dropdown-item show-overlay", navbar_resources_link_active? ? "active" : nil]
              .dropdown-divider

              - if current_user.access_online_class? and current_user.valid_membership?
                = link_to t("user.top_nav.logged.toolbox.liveteach_cohort_library"), online_class_resources_library_path(back_to: request.fullpath), class: ["dropdown-item show-overlay", navbar_cohort_library_link_active? ? "active" : nil]

          - if Setting.for("navbar_onlineclasses_enabled")
            %li.nav-item.d-flex{ class: navbar_analytics_link_active? ? "active" : "" }
              = link_to t("user.top_nav.logged.online_class"), online_classes_path, class: "nav-link wrap-text show-overlay"

          - if gmat_exam?
            %li.nav-item{ class: navbar_resources_link_active? ? "active" : "" }
              = link_to t("user.top_nav.logged.online_tutoring"), online_tutoring_path, class: "nav-link show-overlay"
          - else
            %li.nav-item{ class: navbar_resources_link_active? ? "active" : "" }
              = link_to t("user.top_nav.logged.online_tutoring"), tutoring_path, class: "nav-link", target: "_blank"

        %ul.navbar-nav.profile-menu.d-none.d-lg-flex
          %li.nav-item
            = link_to "#", class: "nav-link topics-search-icon d-none d-lg-block open-search-bar" do
              %i.far.fa-search
              %span.visually-hidden Open Search Bar

  #search-bar
    .container
      = form_tag topics_search_path, remote: true do
        .row
          .col-12.col-lg-8.col-sl-6
            %i.far.fa-search
            %label.visually-hidden{ for: "topics_query" } Search Topics
            = text_field_tag :topics_query, params[:topics_query], placeholder: t("user.top_nav.logged.search_word"), autocomplete: "off", class: "form-control"
            = link_to "#", id: "close-search-bar", aria_label: "Close Search Bar" do
              %i.far.fa-times
              %span.visually-hidden Close Search Bar
    .overlay
    #topics-search-results

  #notification-message-box
    = render "users/home/<USER>"
  #message-container
  #lesson-feedbacks-container
