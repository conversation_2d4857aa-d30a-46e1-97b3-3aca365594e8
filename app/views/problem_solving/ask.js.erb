var abc_checked = $('.mark-text .abc-icon.checked').length > 0;

window.history.pushState({ id: '<%= Time.now.strftime("%Y%m%d%H%M%S") %>' }, '', '<%= @next_url || request.original_fullpath %>');
$('section#problem-solving').replaceWith('<%= j render("ask", evaluation: @evaluation, evaluation_attempt: @evaluation_attempt, problem: @problem, problem_attempt: @problem_attempt, navigator: @navigator, problem_flagged: @problem_flagged, skip_questions_enabled: @skip_questions_enabled, review_question: @review_question) %>');
if ($('.mark-text .abc-icon').length > 0 && abc_checked) {
  $('.mark-text .abc-icon').click();
}

$('#loading-overlay').hide();
replaces_graphics_interpretation_dropdown_spaces();
initialize_radio_buttons();
resume_evaluation();
reprocess_math();
add_sort_by_select_for_table_analysis();
table_responsive($('section#problem-solving table'), false);
determine_gre_stem_alignment();
determine_sat_alignment();
reload_calculator();
initialize_tooltips();
apply_strike_to_problem_sections();

if ($('.activate-calculator').length == 0) {
  $('#calculator').hide();
}
