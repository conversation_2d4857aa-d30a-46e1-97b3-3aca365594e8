:ruby
  question_type = problem.question_type
  remaing_answer_edits = evaluation_attempt.remaining_answer_edits[problem_attempt.remaining_edits_section_key] || 3
  classes = sat_exam? ? ["sat-section"] : []
  attempt = problem_attempt if skip_questions_enabled || review_question || problem.reading_comprehension?

%section#problem-solving{ class: [EXAM_NAME, classes, question_type, evaluation.test_mode, review_question ? "review-question" : nil], data: { evaluation_et: evaluation_attempt.elapsed_time_in_seconds, problem_et: problem_attempt.elapsed_time_in_seconds, estimated_finish_time: navigator.evaluation_estimated_finish_time_in_seconds, seconds_since_last_paused: problem_attempt.seconds_since_last_paused, time_per_question: navigator.time_per_question, problem_attempt_id: problem_attempt.id, evaluation_attempt_id: evaluation_attempt.id, adaptive_evaluation: evaluation_attempt.adaptive_evaluation?.to_s, mark_current_section_as_timed_out_path: evaluation_mark_current_section_as_timed_out_path(attempt_id: evaluation_attempt.id, format: :json) } }
  .test{ class: (sat_exam? ? "container-fluid p-0" : "container") }
    %nav.question-nav{ class: session[:hide_timer] ? "timer-hidden" : "" }
      - if gre_exam? || sat_exam?
        = render "problem_solving/navs/#{EXAM_NAME}", problem: problem, problem_attempt: problem_attempt, problem_flagged: problem_flagged, evaluation: evaluation, user: current_user || evaluation_attempt.user, evaluation_attempt: evaluation_attempt
      - else
        = render "problem_solving/navs/default", problem: problem, problem_attempt: problem_attempt, problem_flagged: problem_flagged, evaluation: evaluation, user: current_user || evaluation_attempt.user

    :ruby
      data_attributes = {
        correct_option: problem_attempt.problem.correct_option_to_compare,
        user_attempted_answer: problem_attempt.answer,
        remaing_answer_edits: remaing_answer_edits
      }

      data_attributes[:reasoning_question_type] = problem_attempt.problem.multi_source_reasoning.question_type if problem_attempt.problem.multi_source_reasoning?

    .question.font-switch-wrapper{ class: [problem.question_type, "#{(current_user || evaluation_attempt.user).font_size_preference}-font", classes], data: data_attributes }
      .question-content{ class: (sat_exam? ? "sat-question-content container" : "") }
        - path = evaluation.practice_mode? ? problem_solving_show_solution_path : problem_solving_answer_path
        = form_tag path, method: "post", id: "answer", data: { remote: true } do
          = hidden_field_tag "evaluation_attempt_id", evaluation_attempt.id
          = hidden_field_tag "problem_attempt_id", problem_attempt.id
          = hidden_field_tag "parsed_answer", problem_attempt.answer, class: "js-exercise-answer"
          = hidden_field_tag "current_et", ""
          = hidden_field_tag "next_question_is_forward", "yes", id: "next_question_is_forward"
          -# RP: In a test like GRE, from a test question is possible to go backwards, so previous input is to identify direction of flow
          = hidden_field_tag "navigate_to_question", "no", id: "navigate_to_question"
          = hidden_field_tag "navigate_to_question_id", "", id: "next_problem_id"
          = hidden_field_tag "is_review_page", "no", id: "is_review_page"
          = hidden_field_tag "exit_and_score", "no", id: "exit-and-score"
          = hidden_field_tag :hide_timer
          = hidden_field_tag :solve_problem, "yes"
          = hidden_field_tag :review_question, review_question ? "yes" : "no"
          = generate_problem_content(problem, (attempt || problem_attempt), navigator, evaluation_attempt).html_safe

      - unless sat_exam?
        .question-actions
          = link_to t("user.evaluations.problem_solving.question_actions.pause"), "#", { id: "pause-btn-on-skip", class: "btn", data: { bs_toggle: "modal", bs_target: "#pause-test-time" } }
          - if problem_attempt.show_exit_and_score_button?
            = link_to t("user.evaluations.problem_solving.question_actions.exit_and_score"), "#", { class: "btn exit", data: { bs_toggle: "modal", bs_target: "#exit-test-and-score" } }
          - else
            = link_to t("user.evaluations.problem_solving.question_actions.exit"), "#", { class: "btn exit", data: { bs_toggle: "modal", bs_target: "#exit-test", redirect_url: problem_solving_pause_path(evaluation_attempt_id: evaluation_attempt.id, problem_attempt_id: problem_attempt.id) } }

          - unless gre_exam?
            - if evaluation.practice_mode?
              = button_tag t("user.evaluations.problem_solving.question_actions.show_solution"), { class: "btn gray confirm-btn pull-right" }
            - elsif review_question
              = link_to problem_solving_review_center_path(evaluation_attempt.id, problem_attempt_id: problem_attempt.id), class: "btn return-to-review-center pull-right show-overlay" do
                %span.d-none.d-md-inline-block.review-span-btn= t("user.evaluations.problem_solving.question_actions.review_question.return_to_review_center")
                %span.d-md-none.review-span-btn= t("user.evaluations.problem_solving.question_actions.review_question.return_to_review_center_small")

              - if remaing_answer_edits.positive?
                = link_to t("user.evaluations.problem_solving.question_actions.review_question.confirm_and_return"), "#", { class: "d-none btn pull-right confirm-and-return-to-review-center", data: { bs_toggle: "modal", bs_target: "#confirm-review-answer-change-modal" } }
            - else
              = button_tag t("user.evaluations.problem_solving.question_actions.next"), { class: "btn gray confirm-btn pull-right show-overlay" }

    #review-test-status

- if problem_attempt.show_exit_and_score_button?
  .modal#exit-test-and-score{ data: { bs_focus: "true", bs_keyboard: "false", bs_backdrop: "static" } }
    .modal-dialog.modal-dialog-centered
      .modal-content
        .modal-header.border-bottom-0
          %button.btn-close.pe-3.pt-3.text-grey-4.fw-bold{ type: "button", data: { bs_dismiss: "modal" } }
        .modal-body.py-0.px-5.text-center
          %h3
            = t("user.evaluations.problem_solving.modal.exit_and_score.title")
          %p.pt-2.text-grey-2= t("user.evaluations.problem_solving.modal.exit_and_score.description")
          = link_to t("user.evaluations.problem_solving.question_actions.exit_and_score"), "#", { class: "btn btn-primary exit modal-no-close modal-js-enabled", id: "exit-and-score-button" }

- if review_question
  .modal#confirm-review-answer-change-modal{ data: { bs_focus: "true", bs_keyboard: "false", bs_backdrop: "static" } }
    .modal-dialog.modal-dialog-centered
      .modal-content
        .modal-header.border-bottom-0
          %button.btn-close.pe-3.pt-3.text-grey-4.fw-bold{ type: "button", data: { bs_dismiss: "modal" } }
        .modal-body.py-0.px-5.text-center
          = image_tag(asset_path("layout/controllers/problem_solving/review_answer_change.svg"), class: "ms-4 mb-1")
          %h3.mt-4= t("user.evaluations.problem_solving.modal.review_answer_change.title")
          %p= t("user.evaluations.problem_solving.modal.review_answer_change.description", remaning_answer_edits: remaing_answer_edits)

        .modal-footer.justify-content-center.border-top-0.pb-4.mb-3
          = link_to t("user.evaluations.problem_solving.modal.review_answer_change.actions.keep_original"), problem_solving_review_center_url(evaluation_attempt.id, problem_attempt_id: problem_attempt.id), { class: "btn btn-outline-secondary me-md-3 show-overlay" }

          = button_tag t("user.evaluations.problem_solving.modal.review_answer_change.actions.change_answer"), { class: "btn btn-primary confirm-btn" }
