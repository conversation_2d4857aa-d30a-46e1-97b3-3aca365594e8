%nav.navbar.navbar-dark.navbar-expand-lg.fixed-top#user-navbar
  .container
    .d-flex.justify-content-between.w-100
      = link_to home_path, class: "navbar-brand logo" do
        = image_tag(asset_path("layout/logo_arrow.svg"), alt: "")

      .toggle-fullscreen.text-white.d-flex.align-items-center.justify-content-center
        %a.d-flex.align-items-center#toggle-fullscreen{ class: ("d-none" if show_fullscreen_button) }
          %i.fas.fa-expand.me-2
          %span.text-nowrap#fullscreen-text Full Screen

%section#end-intermediate-section{ class: EXAM_NAME }
  .container
    %nav.question-nav
      %span.fw-semibold= t("problem_solving.review_center.nav_title")

    .question
      .question-content
        .review-info
          .title= t("problem_solving.review_center.title")
          .description.mt-2
            %span= t("problem_solving.review_center.description", exam: exam_title(for_legacy_course: evaluation.user.for_legacy_course)).html_safe

          .mt-4#review-questions
            .status-tables.d-flex
              :ruby
                if problems_ids_for_review.length > 20
                  middle_index = (problems_ids_for_review.length / 2.0).ceil
                  groups = [problems_ids_for_review[0...middle_index], problems_ids_for_review[middle_index..]]
                else
                  groups = problems_ids_for_review.each_slice(10).to_a
                end

                number_of_questions_first_table = groups[0].size
                number_of_questions_last_table = groups[1].nil? ? 0 : groups[1].size

                data = [
                  [number_of_questions_first_table, first_question_index]
                ]

                if groups.size == 2
                  last_table_question_index = number_of_questions_first_table + first_question_index
                  data << [number_of_questions_last_table, last_table_question_index]
                end

              - data.each_with_index do |(table_number_of_problems, table_first_question_index), table_index|
                %div{ class: table_index.positive? ? "d-none d-md-block ms-4" : nil }
                  %table.table-bordered
                    %thead
                      %tr
                        %th= t("problem_solving.review_center.review_questions.question")
                        %th= t("problem_solving.review_center.review_questions.bookmarked")

                    %tbody
                      - (table_first_question_index...(problems_ids_for_review.length + first_question_index)).each do |problem_index|
                        :ruby
                          is_visible_mobile = table_index.zero? && (table_number_of_problems + first_question_index) <= problem_index
                          row_problem_attempt = evaluation_attempt.problem_attempts.find { |pa| pa.problem_id == navigator.problems.ids[problem_index] }

                        %tr{ class: is_visible_mobile ? "d-md-none" : nil, data: { problem_id: row_problem_attempt.problem_id, problem_attempt_id: row_problem_attempt.id } }
                          %td= link_to (problem_index + 1), problem_solving_ask_path(evaluation_attempt, row_problem_attempt, review_action: "review_question"), class: "show-overlay"
                          %td= link_to row_problem_attempt.flags.present? ? "<i class='fas fa-bookmark'>".html_safe : "", problem_solving_ask_path(evaluation_attempt, row_problem_attempt, review_action: "review_question"), class: "show-overlay"

      .question-actions
        = link_to t("user.evaluations.problem_solving.question_actions.pause"), "#", { id: "pause-btn-on-skip", class: "btn", data: { bs_toggle: "modal", bs_target: "#pause-test-time" } }

        = link_to t("user.evaluations.problem_solving.question_actions.exit"), "#", { class: "btn exit", data: { bs_toggle: "modal", bs_target: "#exit-test", redirect_url: problem_solving_pause_path(evaluation_attempt_id: evaluation_attempt.id, problem_attempt_id: problem_attempt.id) } }

        = link_to problem_solving_answer_path(evaluation_attempt_id: evaluation_attempt.id, problem_attempt_id: evaluation_attempt.problem_attempts.find_by(problem_id: problems_ids_for_review[-1]).id, end_review: "yes", next_question_is_forward: "yes"), method: :post, class: "btn pull-right end-review show-overlay", data: { disable_with: "Wait..." } do
          = t("problem_solving.review_center.end_review")


