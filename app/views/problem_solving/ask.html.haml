%nav.navbar.navbar-dark.navbar-expand-lg.fixed-top#user-navbar
  .container
    .d-flex.algin-items-center.justify-content-between.w-100
      = link_to home_path, class: "navbar-brand logo" do
        = image_tag(asset_path("layout/logo_arrow.svg"), alt: "TTP Home")

      .toggle-fullscreen.text-white.d-flex.align-items-center.justify-content-center
        %a.d-flex.align-items-center#toggle-fullscreen{ class: @show_fullscreen_button ? "" : "d-none" }
          %i.fas.fa-expand.me-2
          %span.text-nowrap#fullscreen-text Full Screen

= render "ask", evaluation: @evaluation, evaluation_attempt: @evaluation_attempt, problem: @problem, problem_attempt: @problem_attempt, navigator: @navigator, problem_flagged: @problem_flagged, skip_questions_enabled: @skip_questions_enabled, review_question: @review_question

- if Setting.for("whiteboard_enabled")
  = render "partials/whiteboard"
