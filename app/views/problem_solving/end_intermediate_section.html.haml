%nav.navbar.navbar-dark.navbar-expand-lg.fixed-top#user-navbar
  .container
    .d-flex.algin-items-center.justify-content-between.w-100
      = link_to home_path, class: "navbar-brand logo" do
        = image_tag(asset_path("layout/logo_arrow.svg"), alt: "")

      .toggle-fullscreen.text-white.d-flex.align-items-center.justify-content-center
        %a.d-flex.align-items-center#toggle-fullscreen{ class: ("d-none" if @show_fullscreen_button) }
          %i.fas.fa-expand.me-2
          %span.text-nowrap#fullscreen-text Full Screen

%section#end-intermediate-section{ class: EXAM_NAME }
  .container
    %nav.question-nav.clearfix
      %span.fw-semibold.text-white= t("problem_solving.end_intermediate_section.nav_title")
      - if gre_exam?
        .pull-right
          .item.last.navigation-buttons
            = link_to evaluation_continue_path(@evaluation_attempt), class: "btn next-btn show-overlay" do
              = t("user.evaluations.problem_solving.question_actions.next")

    .question
      .question-content
        .break-info
          = image_tag("layout/controllers/problem_solving/test_paused.svg")
          .title.text-dark-1= t("problem_solving.end_intermediate_section.title")
          .description
            %span.text-grey-2.fw-light= t("problem_solving.end_intermediate_section.description_part_1", exam: exam_title(for_legacy_course: @evaluation.user.for_legacy_course)).html_safe
            - unless @evaluation_attempt.user.guest?
              %span.text-grey-2.fw-light= t("problem_solving.end_intermediate_section.description_part_2").html_safe
      .question-actions
        = link_to t("user.evaluations.problem_solving.question_actions.pause"), "#", id: "pause-btn-on-skip", class: "btn gray"

        = link_to t("user.evaluations.problem_solving.question_actions.exit"), "#", { class: "btn exit", data: { bs_toggle: "modal", bs_target: "#exit-test", redirect_url: problem_solving_pause_url(evaluation_attempt_id: @evaluation_attempt.id, problem_attempt_id: @problem_attempt.id) } }

        - unless gre_exam?
          = link_to evaluation_continue_path(@evaluation_attempt), class: "btn pull-right next_from_intermediate_section" do
            = t("user.evaluations.problem_solving.question_actions.next")

    #review-test-status
