:ruby
  problem_attempt = local_assigns[:problem_attempt]
  example_attempt = local_assigns[:example_attempt]
  is_example = problem.is_a?(Example)
  is_concept_mastery = problem.is_a?(ConceptMastery)
  for_problem_editors ||= local_assigns[:for_problem_editors] ? for_problem_editors : false
  is_problem_show = local_assigns[:is_problem_show] || false
  is_problem = problem.is_a?(Problem)
  is_targeted_practice = problem.is_a?(TargetedPractice)
  skip_correctness = local_assigns[:skip_correctness] || false
  is_incorrect_correct ||= local_assigns[:is_incorrect_correct] ? is_incorrect_correct : false
  show_users_answers_analytics = problem_attempt || (example_attempt && local_assigns[:show_users_answers_analytics])
  row_index ||= 999
.test-problem{ class: EXAM_NAME }
  .test-scroll-box.d-flex.justify-content-flex-start.flex-column.mx-auto
    - if problem.text_completion?
      .text-completion-instructions
        %p.text-grey-1.fw-normal.mb-0.fs-16= t("user.exercises.question_instructions.text_completion.#{(exercise.correct_option_index.split(',').length > 1) ? 'multiple_group' : 'single_group'}")
    - elsif problem.sentence_equivalence?
      .sentence-equivalence-instructions
        %p.text-grey-1.fw-normal.mb-0.fs-16= t("user.exercises.question_instructions.sentence_equivalence").html_safe

    - is_not_noteable = problem.reading_comprehension? || problem.graphics_interpretation? || problem.multi_source_reasoning? || problem.table_analysis?
    .d-flex.w-100.mark-wrapper
      %span.question-count.fs-18.fw-semibold= problem_attempt.navigator.current_problem_number
      .mark-text.fs-16.d-flex.align-items-center
        .flag-wrapper.item.bookmark-icon{ data: { bs_toggle: "tooltip", bs_placement: "bottom" }, title: "Mark question for review" }
          = link_to Rails.application.routes.url_helpers.toggle_flag_path(id: problem_attempt.id, type: "problem_attempt", label: :marked), class: ["mark-problem", problem_attempt.flags.exists? ? "flagged" : nil, "show-overlay"], data: { problem_id: problem.id } do
            %span.visually-hidden= t("user.evaluations.problem_solving.toggle_bookmark")
            %i.fas.fa-bookmark
            %i.fal.fa-bookmark
        %p.mark-review.m-0.ms-1= t("user.evaluations.problem_solving.mark_for_review")
        .abc-icon.position-relative{ class: problem.fill_in_the_blank? ? "d-none" : nil, data: { evaluation_id: evaluation_attempt.id }}
          = image_tag("layout/sat/abc_icon.svg", class: "default-abc-icon")
          = image_tag("layout/sat/filled_abc_color_icon.svg", class: "filled-abc-icon")
          .abc-tooltip.position-absolute
            .arrow-up
            Cross out answer choices you think are wrong

    .interrogation_part.sat-question{ class: is_not_noteable ? "not-noteable" : nil }

      = replace_img_for_asset(problem.content, content_assets).html_safe

      - if problem.quantitative_comparison?
        .quantity-a
          %h4= "Quantity A"
          = replace_img_for_asset(problem.quantity_a, content_assets).html_safe
        .quantity-b
          %h4= "Quantity B"
          = replace_img_for_asset(problem.quantity_b, content_assets).html_safe

    .answers{ class: [for_editors ? "answers-problem-bookmarked" : "", problem.multi_source_reasoning&.multiple_answer? ? "multiple-answers" : nil, is_not_noteable ? "not-noteable" : nil] }
      - if for_editors or for_problem_editors or is_example or is_concept_mastery or is_problem_show or is_problem or is_ai_example
        %input.js-exercise-answer{ type: "hidden" }

      - if problem.fill_in_the_blank?
        - correctness_class = (problem_attempt&.correct || example_attempt&.correct) ? "correct" : "incorrect"
        - if problem.prepended_symbol.present?
          %span{ class: ["prepended-symbol", exercise.double_input? ? "fraction" : ""] }= exercise.prepended_symbol
        %fieldset.fraction-input#fill-in-the-blanks
          -# RP: In result test page we want input to be disabled. If row_index is not 999 it's because we are in result test page
          .numerator-wrapper
            %input.integer_or_float.numerator{ name: "answer", maxlength: 6, value: problem_attempt&.answer, disabled: row_index != 999 }
            .error-message.d-none
              %i.far.fa-exclamation-circle
              %span.error-text

        %span{ class: ["result", "fill-in-the-blank", is_incorrect_correct ? correctness_class : "", problem_attempt&.question_skipped? ? "d-none" : ""] }
        .answer-preview
          .d-flex.gap-2
            %p= t("user.evaluations.problem_solving.answer_preview")
            %p.result.fill-in-the-blank{ class: [problem.double_input? ? "fraction" : "", is_incorrect_correct ? correctness_class : ""] }
              = problem_attempt&.fill_in_the_blank_numerator || example_attempt&.fill_in_the_blank_numerator
              - if problem.double_input? && (problem_attempt&.fill_in_the_blank_denominator.present? || example_attempt&.fill_in_the_blank_denominator.present?)
                = "/"
                = problem_attempt&.fill_in_the_blank_denominator || example_attempt&.fill_in_the_blank_denominator
      - elsif is_targeted_practice
        :ruby
          blanks_count = problem.correct_option_index.split(",").length
          options_count = problem.options_array.length
          options_per_group = options_count / blanks_count

        - problem.options_array.in_groups_of(options_per_group).each_with_index do |options, group_index|
          %div{ class: ((blanks_count > 1) ? "stack-mobile-#{blanks_count} blank-group" : "") }
            - if blanks_count > 1
              %h4 Blank (#{'i' * (group_index + 1)})

            - options.each_with_index do |option, index|
              - unless option.blank?
                .d-flex.align-items-center.justify-content-between.answer-main-wrap
                  .answer.d-flex.gap-3.align-items-center#problem-section{ data: { value: index } }
                    %span.alpha-num= ("A".ord + index).chr
                    %label.visually-hidden{ for: "answer-#{index}" } Option #{index}
                    %span.option.answer-text
                      = replace_img_for_asset(option, content_assets).html_safe
                  %span.alpha-num-strike.d-none
                    %span.alpha-num-strike-through= ("A".ord + index).chr
                  %span.undo-text.fw-semibold.d-none= t("user.evaluations.problem_solving.undo")
      - elsif problem.text_completion?
        = render "exercises/shared/text_completion", exercise: problem, attempt: example_attempt || problem_attempt, content_assets: content_assets
      - elsif problem.multi_source_reasoning&.multiple_answer?
        = render "exercises/shared/multi_source_reasoning/multiple_answer", exercise: problem, attempt: example_attempt || problem_attempt, content_assets: content_assets, skip_correctness: skip_correctness
      - elsif problem.two_part_analysis?
        = render "exercises/shared/two_part_analysis", exercise: problem, attempt: example_attempt || problem_attempt, content_assets: content_assets
      - elsif problem.graphics_interpretation?
        = render "exercises/shared/graphics_interpretation", exercise: problem, attempt: example_attempt || problem_attempt, content_assets: content_assets
      - elsif problem.table_analysis?
        = render "exercises/shared/table_analysis", exercise: problem, attempt: example_attempt || problem_attempt, content_assets: content_assets, skip_correctness: skip_correctness
      - elsif !problem.empty_question?
        - problem.options_array.each_with_index do |option, index|
          - unless option.blank?
            :ruby
              classes = ["radio-style"]
              classes << "skip-correctness" if skip_correctness

            - if problem_attempt
              :ruby
                classes << class_for_answer(problem_attempt.correct_option?(index))
                classes << "user-choice" if problem_attempt.user_selected?(index)

            - elsif is_incorrect_correct
              :ruby
                classes << class_for_answer(example_attempt.correct_option?(index))
                classes << "user-choice" if example_attempt.user_selected?(index)
            .d-flex.align-items-center.justify-content-between.answer-main-wrap
              .answer.d-flex.gap-3.align-items-center#problem-section{ data: { value: index, question_id: problem_attempt.id }, class: ((problem_attempt&.answer == index.to_s) ? "selected" : "") }
                %span.alpha-num= ("A".ord + index).chr
                %label.visually-hidden{ for: "answer-#{index}" } Option #{index}
                %span.option.answer-text
                  = replace_img_for_asset(option, content_assets).html_safe
              %span.alpha-num-strike.d-none
                %span.alpha-num-strike-through= ("A".ord + index).chr
              %span.undo-text.fs-16.fw-semibold.d-none= t("user.evaluations.problem_solving.undo")

            - if show_users_answers_analytics || for_problem_editors
              .clearfix
              .option-analytics.hidden.tooltip-top{ data: { qtip: "tooltip", title: t("user.evaluations.results.results-table.option_analytics", percentage: problem.first_attempts_analytics[index]) } }
                .accuracy-progress-bar-wrapper
                  .accuracy-progress-bar{ style: "width: #{problem.first_attempts_analytics[index]}%", data: { accuracy: problem.first_attempts_analytics[index] } }
                .accuracy #{problem.first_attempts_analytics[index]}%

  = render "problem_solving/navs/line_reader_modal"

.question-actions.d-flex.justify-content-end.flex-md-row.flex-column.position-relative
  .question-btn.fs-14.fw-semibold.d-flex.align-items-center.gap-3#question-tooltip-button
    .question-text= t("user.evaluations.problem_solving.questions", progress: problem_attempt.question_progress)
    %i.fa.fa-angle-down
    = render "problem_solving/navs/sat/question_number_modal", problem: problem, problem_attempt: problem_attempt, navigator: navigator, evaluation_attempt: evaluation_attempt
  .d-flex.justify-content-md-end.justify-content-center.gap-2
    = button_tag t("user.evaluations.problem_solving.question_actions.back").upcase, { class: "btn back-btn show-overlay fs-14 fw-semibolds #{navigator.disable_back_button? ? 'disabled' : ''}" }
    = button_tag t("user.evaluations.problem_solving.question_actions.next").upcase, { class: "btn next-btn show-overlay fs-14 fw-semibold text-light" }
