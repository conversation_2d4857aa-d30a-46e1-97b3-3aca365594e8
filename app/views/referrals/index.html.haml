:ruby
  toggle_modal_attributes = current_user ? {} : { bs_toggle: "modal", bs_target: "#sign-up-sign-in-modal" }

%section.flex-grow-1.py-5.px-3.bg-light-1
  .container
    .row.referral
      .col-12.col-lg-6.p-0.form-column
        .referral-form
          .header
            .wrapper
              = image_tag(asset_path("layout/controllers/referral/ttp_logo.png"), alt: "")
              %h3.text-white.fw-semibold= t("user.referral.title").html_safe

          .body.h-100
            .share-link{ data: toggle_modal_attributes }
              %span.icon-wrapper.flex-shrink-0.flex-grow-0.my-auto.text-center
                %i.fal.fa-share-alt
              %h5.fw-semibold.mt-2= t("user.referral.body.first_way.title")
              .row.gx-2.gx-lg-3.mt-3.mx-0
                .col-12.col-md-auto.gx-0.mt-2.mt-md-0.order-2.order-md-1
                  %button.btn.btn-outline-primary.w-100#copy-text{ type: "button", data: { value: plans_url(referral_code: @referral_token) } }= t("user.referral.body.first_way.button").html_safe
                %div{ class: current_user ? "referred-link" : "blurred-referred-link" }
                  = label_tag :referred_link, "Referred Link", class: "visually-hidden"
                  = text_field_tag :referred_link, plans_url(referral_code: @referral_token), required: true, class: "form-control read_only border-grey-3", autocomplete: "off"


            .seperator
              %span.fade-rule
              %span.text OR

            .email-friends{ data: toggle_modal_attributes }
              %span.icon-wrapper.flex-shrink-0.flex-grow-0.my-auto.text-center
                %i.fal.fa-envelope
              %h5.fw-semibold.mt-2.mb-1= t("user.referral.body.second_way.title")
              - if current_user
                %span.second-way-description.text-grey-3= t("user.referral.body.second_way.description", email: current_user.email)
              - else
                .second-way-description.text-grey-3= t("user.referral.body.second_way.description_without_email")
                .second-way-blurred-email.text-grey-3= t("user.referral.body.second_way.example_email")

              = form_for :referrals, method: :post, remote: true, html: { class: "mt-3" } do |f|
                #emails
                  .email-input-wrapper.mb-1
                    = label_tag "referral_email", "Email"
                    = text_field_tag "referrals[emails][]", nil, class: "form-control referral-email", placeholder: "Email", autofocus: true, id: "referral_email"
                    .invalid-feedback

                %button.link-primary.text-decoration-underline.bg-transparent.border-0.text-start.p-0.mt-2.mb-3#add-referral-email-field{ type: "button" }= t("user.referral.body.second_way.add_another_email").html_safe

                .form-check.my-3
                  = f.check_box :is_reminder_set, class: "form-check-input input-check-app", value: false
                  = f.label :is_reminder_set, t("user.referral.body.second_way.reminder").html_safe, class: "fw-normal text-grey-2"

                = f.submit t("user.referral.body.second_way.button"), class: "btn btn-primary w-100 text-nowrap"

      .col-12.col-lg-6.p-0.instruction-column
        .referral-instructions
          .wrapper
            .title= t("user.referral.instructions.title").html_safe
            .instructions
              - (1..4).each do |index|
                .instruction.d-flex.align-items-start
                  = image_tag(asset_path("layout/controllers/referral/number_#{index}.png"), alt: "")
                  %p= t("user.referral.instructions.instruction_#{index}", exam: exam_name_display_title(include_exam_type: true)).html_safe
              .note
                %span= t("user.referral.instructions.note").html_safe

= render "components/sign_up_sign_in_modal"
= render "referrals/email_successfully_sent_modal"
