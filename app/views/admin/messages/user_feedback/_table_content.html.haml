- if lesson_feedbacks.present?
  - lesson_feedbacks.each do |lesson_feedback|
    = render "admin/messages/user_feedback/lesson_feedback", lesson_feedback: lesson_feedback

- elsif query.present?
  %tr
    %td{ colspan: 5 }
      .no-content
        .icon= image_tag "admin/lens.png", width: "110px", height: "110px"
        .title= "No comments found"
        .subtitle= "Try a different search to find results here"

- else
  %tr
    %td{ colspan: 5 }
      .no-content
        .icon= image_tag "admin/messages.png", width: "78px", height: "64px"
        .title= "Well done! No comments found"
        .subtitle= "You don't have feedback to review"
