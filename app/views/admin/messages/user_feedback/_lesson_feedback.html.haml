%tr{ id: "lesson-feedback-#{lesson_feedback.id}" }
  %td.width-custom
    .user-full-name
      = lesson_feedback.user.full_name
      - if lesson_feedback.user.more_than_15_negative_feedback?
        %i.fal.fa-exclamation-triangle
    .user-email
      = lesson_feedback.user.email
    .feedback-date
      %span.subtitle= "DATE:"
      %span.date= lesson_feedback.created_at.strftime("%b %d, %Y")
    .feedback-score
      %span.subtitle= "SCORE:"
      %span.score{ class: lesson_feedback.score_to_class }= lesson_feedback.score_to_s.humanize
  %td.width-custom
    .info
      %span.chapter= lesson_feedback.lesson.chapter.name
      %span.point-separator= "•"
      %span.lesson= lesson_feedback.lesson.name
    .vertical-separator
    .message
      = lesson_feedback.comment.present? ? lesson_feedback.comment.truncate(300) : "-"
  %td.text-center
    .status{ class: lesson_feedback.status.to_s }= lesson_feedback.status.upcase
  %td
    .resolved-by= lesson_feedback.resolved_by ? lesson_feedback.resolved_by.full_name : "-"
    .resolved-at= lesson_feedback.resolved_at ? lesson_feedback.resolved_at.strftime("%b %d, %Y") : ""

  - if current_user.edit_access_to_admin_module?("Messages")
    %td.text-center.max-width-custom
      = link_to "VIEW / REPLY", reply_admin_messages_path(id: lesson_feedback.id), class: "btn btn-primary-fully btn-custom"
      = link_to "MARK AS RESOLVED", resolve_admin_lesson_feedback_path(lesson_feedback), method: :put, remote: true, class: "btn btn-primary btn-custom show-overlay #{'disabled' if lesson_feedback.resolved?}"
