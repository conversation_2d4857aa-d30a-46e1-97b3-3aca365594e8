.title-box
  .title.justify-content-between
    - if action.in?(%w(user_feedback announcements))
      %h1= title
    - else
      = link_to url_back, class: "link-container" do
        %i.fas.fa-chevron-left
        %h1= title

    = yield

  - if chapters
    .view-tab-group
      - Section.pluck(:name).each do |section|
        = link_to section.capitalize, url_for(section: section), class: "view-tab #{'active' if section == current_section}"

- if tabs
  .section-tab-box
    .section-tab-group
      = link_to user_feedback_admin_messages_path(section: current_section), class: ("active" if action == "user_feedback") do
        .section-tab
          FEEDBACK MESSAGES
      = link_to admin_announcements_path(section: current_section), class: ("active" if action == "announcements") do
        .section-tab
          HOME MESSAGES
      = link_to admin_marketing_modals_path, class: ("active" if action == "marketing_modal") do
        .section-tab
          MODAL MESSAGES
