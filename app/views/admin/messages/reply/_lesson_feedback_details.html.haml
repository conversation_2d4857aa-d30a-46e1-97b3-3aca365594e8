.row
  .col-md-3
    .user-fullname= lesson_feedback.user.full_name
    .user-email= lesson_feedback.user.email
  .col-md-2
    .field-label= "SCORE"
    .score{ class: lesson_feedback.score_to_class }= lesson_feedback.score_to_s.humanize
  .col-md-2
    .field-label= "STATUS"
    .status{ class: lesson_feedback.status.to_s }= lesson_feedback.status.upcase
  .col-md-2
    .field-label= "DATE"
    .date-value= lesson_feedback.created_at.strftime("%b %d, %Y")
  .col-md-3
    = link_to "MARK AS RESOLVED", resolve_admin_lesson_feedback_path(lesson_feedback), method: :put, remote: true, class: "btn btn-primary btn-custom show-overlay #{'disabled' if lesson_feedback.resolved?}"
