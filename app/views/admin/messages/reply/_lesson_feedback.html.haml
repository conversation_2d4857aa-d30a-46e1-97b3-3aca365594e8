.container-fluid{ id: "lesson-feedback-#{lesson_feedback.id}" }
  .white-box
    .details-container
      #lesson-feedback-details
        = render "admin/messages/reply/lesson_feedback_details", lesson_feedback: lesson_feedback
    .lesson-info-container
      - lesson = lesson_feedback.lesson

      %span.from= "FROM:"
      %span.chapter= lesson.chapter.name
      %span.lesson= link_to lesson.name, lesson_path(lesson), target: "_blank"
  .chat-box
    #conversation
      = render "admin/messages/reply/conversation", messages: lesson_feedback.messages.order(:created_at)

    = form_for [:admin, Message.new], remote: true, html: { id: "send-message", class: ("disabled" if lesson_feedback.resolved?) } do |f|
      = hidden_field_tag :lesson_feedback_id, lesson_feedback.id
      = f.text_area :content, class: "form-control", required: true, rows: 1, placeholder: "Write a response for the message here", disabled: lesson_feedback.resolved?
      = button_tag type: "submit", id: "reply-and-resolve", class: "btn btn-primary-fully show-overlay send-reply-button #{'disabled' if lesson_feedback.resolved?}" do
        %span= "SEND REPLY & RESOLVE"
