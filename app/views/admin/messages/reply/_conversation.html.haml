- messages.each do |message|
  - sender = message.sender
  .message-group{ class: ("admin-sender" if sender.admin?) }
    - if sender.admin?
      .admin-img-container= image_tag "admin/chat_icon.svg", width: "42px", height: "42px"
    - else
      .img-container
        .sender-letters= sender.letters_for_avatar
    .message-container
      .message-content
        = message.content
      - if sender.admin?
        .message-sender
          = sender.full_name
      .message-date{ class: ("custom-margin-top" unless sender.admin?) }
        = message.created_at.strftime("%b. %d, %Y, %H:%M")
