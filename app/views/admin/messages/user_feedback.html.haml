= render "menu", action: "user_feedback", chapters: true, tabs: true, title: "Message Center", url_back: user_feedback_admin_messages_path

.container-fluid
  = form_tag request.path, method: :get, remote: true, class: "mt-4 show-overlay", id: "chapter-filter-form" do
    = hidden_field_tag :section, current_section

    .row
      .col-12.col-md-3
        .mb-3
          = select_tag :chapter_id, options_from_collection_for_select(@chapters, :id, :name, @chapter_id), include_blank: "All Chapters", class: "submit-on-change form-select form-select-lg"

      .col-12.col-md-3
        .mb-3
          = select_tag :score, options_for_select([%w(Negative negative), %w(Positive positive)], @score), include_blank: "All Scores", class: "submit-on-change form-select form-select-lg"

      .col-12.col-md-3
        .mb-3
          = select_tag :status, options_for_select(LessonFeedback.statuses.map { |x| [x[0].humanize, x[1]] }, @status), include_blank: "All Status", class: "submit-on-change form-select form-select-lg"

      .col-12.col-md-3
        .input-group.mb-3
          = text_field_tag :query, @query, class: "form-control form-control-lg", placeholder: "Search by name or email"
          %button.button-search.input-group-text.d-sm-flex
            %i.fas.fa-search

  .scrollable-table-container
    %table.ttp-table.table.table-borderless.table-custom-border.mt-4
      %thead.table-custom-border
        %tr
          %th.sticky-top SENT BY
          %th.sticky-top FEEDBACK
          %th.sticky-top.text-center STATUS
          %th.sticky-top LAST RESOLVED BY
          - if current_user.edit_access_to_admin_module?("Messages")
            %th.sticky-top.text-center ACTIONS

      %tbody#lesson-feedbacks
        = render "admin/messages/user_feedback/table_content", lesson_feedbacks: @lesson_feedbacks, query: @query

    = render "admin/shared/pagination", pagy: @pagy
