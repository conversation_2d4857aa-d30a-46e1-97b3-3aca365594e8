= form_for :tracks, url: save_tracks_admin_topics_path, remote: true, html: { id: "lessons-examples-tracks", class: "show-overlay" } do |f|
  = f.hidden_field :chapter_id, value: chapter.id

  .scrollable-table-container
    %table.ttp-table.table.table-borderless.table-custom-border.mt-4#lessons-tracks
      %thead.table-custom-border
        %tr
          %th.sticky-top.text-center Number
          %th.sticky-top Lesson Name
          - @tracks.each do |track|
            %th
              .d-flex.flex-column.text-center
              - if track.for_legacy_course
                %span.sticky-top.text-center.for-legacy-course.align-items-center.justify-content-center.d-flex.text-white LEGACY
                .sticky-top.track.text-center= track.name.titleize
              - else
                .sticky-top.track.text-center= track.name.titleize
          %th.sticky-top Actions

      %tbody#tracks
        = f.fields_for :lessons, chapter, index: chapter.id do |lf|
          %tr.lesson.odd{ id: "chapter-#{chapter.id}" }
            %td.text-center= chapter.full_number
            %td= chapter.name

            - tracks.each do |track|
              %td.text-center= lf.check_box :track_ids, { multiple: true, data: { id: chapter.id } }, track.id, ''
            %td.text-center

        - topics.each_with_index do |topic, index|
          - examples = topic.examples.only_parents.load.ordered
          - concept_masteries = topic.concept_masteries.load.ordered

          = f.fields_for :lessons, topic, index: topic.id do |lf|
            %tr.lesson{ class: index % 2 == 0 ? "even" : "odd", id: "topic-#{topic.id}" }
              %td.text-center= topic.full_number
              %td
                - if examples.present? || concept_masteries.present?
                  = link_to '', { data: { bs_toggle: "collapse", bs_target: ".exercise.topic-#{topic.id}" }, class: 'lesson-link' } do
                    = topic.name
                    %i.far.fa-chevron-down.ms-2
                    %i.far.fa-chevron-up.ms-2
                - else
                  = topic.name
                - tracks.each do |track|
                  %td.text-center= lf.check_box :track_ids, { multiple: true, data: { id: topic.id, parent_id: topic.parent_id } }, track.id, ""
              %td.text-center.justify-content-center.dropdown.overflow-visible
                = link_to "#", class: "dropdown-toggle", data: { bs_toggle: "dropdown" } do
                  %i.fas.fa-ellipsis-v
                .dropdown-menu.dropdown-menu-right
                  = link_to lesson_path(topic), class: "dropdown-item", target: "_blank" do
                    Preview
                    %i.fas.fa-sign-in.float-end
                  = link_to edit_admin_topic_path(topic), class: "dropdown-item" do
                    Edit
                    %i.fas.fa-pencil.float-end
                  = link_to admin_topic_path(topic), method: :delete, remote: true, class: "dropdown-item red", data: { confirm: "Are you sure?" } do
                    Delete
                    %i.fas.fa-trash.float-end

          - [examples, concept_masteries].each do |exercises|
            - exercises.each_with_index do |exercise, index|
              - exercise_class = exercise.instance_of?(Example) ? :examples : :concept_masteries
              = f.fields_for exercise_class, exercise, index: exercise.id do |qf|
                - statistics = exercise.statistics

                %tr.exercise.collapse{ class: "topic-#{topic.id}" }
                  %td{ class: ("border-bottom" if index == exercises.size - 1) }
                  %td.border-bottom.exercise-content
                    .exercise-type.mb-4= exercise.type.titleize
                    .d-flex.align-items-center
                      .stem= full_exercise_stem(exercise)
                      .level.text-center
                        = "#{statistics[:correct]}%"
                        - if exercise.level.present?
                          %br
                          %span.test-lvl.d-inline-block.mt-1{ class: Exercise::LEVELS[exercise.level.to_sym] }= Exercise::LEVELS[exercise.level.to_sym].capitalize

                  - tracks.each do |track|
                    %td.border-bottom.text-center= qf.check_box :included_track_ids, { multiple: true }, track.id, ""
                  %td.text-center
