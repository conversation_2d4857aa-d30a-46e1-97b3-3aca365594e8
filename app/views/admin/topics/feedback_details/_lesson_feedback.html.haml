.card.bg-light.mb-3.border-0{ id: "lesson-feedback-#{lesson_feedback.id}" }
  .card-body
    .card-title.d-flex.justify-content-between.align-items-start
      %div
        = link_to lesson_feedback.user.full_name, feedbacks_admin_user_path(lesson_feedback.user, chapter_id: params[:chapter_id], date_range: params[:date_range], order: params[:order], topic_id: lesson_feedback.lesson_id), class: "h4 m-0"
        %p.small= lesson_feedback.user.email
        %p.small.text-secondary Date: #{lesson_feedback.created_at.strftime("%-m/%-d/%Y")}
      %div
        %h3.bg-white.ms-3.px-4.py-2.text-primary.font-weight-bold= lesson_feedback.score
    - if current_user.edit_access_to_admin_module?("Lessons and Examples")
      %p
        - if lesson_feedback.resolved?
          = link_to "MARK AS UNRESOLVED", unresolve_admin_lesson_feedback_path(lesson_feedback), method: :put, remote: true, class: "btn btn-outline-secondary float-end show-overlay"
        - else
          = link_to "MARK AS RESOLVED", resolve_admin_lesson_feedback_path(lesson_feedback), method: :put, remote: true, class: "btn btn-primary float-end show-overlay"

    = lesson_feedback.comment
