= render "show_menu", action: @filter, online_class: @online_class, chapters: false, tabs: true, title: "Live Classes", url_back: redirect_to_online_class_action_path(:index)

- if @filter == "classes"
  #online-classes-list
    = render "classes", online_class: @online_class, is_bootcamp_page: @is_bootcamp_page
- elsif @filter == "messages"
  = render "messages", online_class: @online_class
- else
  = render "general", online_class: @online_class

= render "admin/online_classes/messages/modal", online_class: @online_class
