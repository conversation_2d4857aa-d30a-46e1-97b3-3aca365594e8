.accordion.mb-3{ id: "accordion-class-session-#{session_number}" }
  .card
    .card-header
      .accordion-toggle.d-block.w-100.text-start
        .grab-zone
          %span{ id: "class-session-#{session_number}" } Class #{session_number}

        %i.far.fa-trash.remove-class-session
        .separator
        %i{ class: "far fa-chevron-down row-icon", data: { bs_toggle: "collapse", bs_target: "#content-#{unique_id}-session-item-#{session_number}" } }

    .collapse.show{ id: "content-#{unique_id}-session-item-#{session_number}", data: { parent: "#accordion-class-session-#{session_number}" } }
      .card-body
        .row
          .col-lg-5.col-sm-6
            .mb-3
              = session_form.label :date, class: "form-label"
              = session_form.date_field :date,
              class: "form-control session-schedule-date-field",
              placeholder: "yyyy-mm-dd"

          .col-lg-3.col-sm-6
            .setup-custom-time.mt-5
              = check_box_tag :setup_time, class: "session-setup-time-field"
              = label_tag "SETUP CUSTOM TIME", nil, class: "form-label"

          .col-lg-2.col-sm-6
            .mb-3
              = session_form.label :start_time, class: "form-label"
              = session_form.select :start_time,
                fetch_time_options,
                {},
                { class: "form-select  session-start-time-field disabled" }

          .col-lg-2.col-sm-6
            .mb-3
              = session_form.label :end_time, class: "form-label"
              = session_form.select :end_time,
                fetch_time_options,
                {},
                { class: "form-select session-end-time-field disabled" }

        = session_form.label :description, class: "form-label"
        = session_form.cktext_area :description, class: "form-control session-description-field", id: "description_#{unique_id}-session-#{session_number}"
        - if session_number != 0
          = session_form.check_box :_destroy, class: "checkbox-destroy-field checkbox-destroy-session-#{session_number}-field", hidden: true
