.modal.fade.add-class-recording-modal{ id: "add-class-recording-#{session.id}", "aria-hidden": "true", tabindex: "-1" }
  .modal-dialog.form-modal
    .modal-content
      .modal-header
        %h5.modal-title= recording_url?(session) ? t("admin.actions.online_class.add_class_recording.edit_recording") : t("admin.actions.online_class.add_class_recording.add_recording")
      .modal-body
        = form_tag add_class_recording_admin_online_classes_path(session), method: :post, remote: true, data: { recording: session.recording_url } do
          - disabled = session.recording_url.blank?
          .form-group.flex-column.d-flex
            = label_tag :recording_url, t("admin.actions.online_class.add_class_recording.recording_link"), class: "fs-16 fw-semibold text-uppercase mb-2"
            = text_field_tag :recording_url, session.recording_url, class: "form-input", id: "recording-url-input"

          .mt-4.gap-2.align-items-center.d-flex
            = check_box_tag :show_in_library, "1", session.show_in_library, class: "show-recording", disabled: disabled, data: { checkbox: session.show_in_library }
            = label_tag :show_in_library, t("admin.actions.online_class.add_class_recording.show_in_repository"), class: "fs-14 fw-semibold text-uppercase text-dark show-recording-label #{'text-muted' if disabled}"

          .modal-footer.border-0.p-0.mt-5
            %button.close-btn.text-uppercase.fs-14.fw-semibold.my-0.ms-0{ "data-bs-dismiss": "modal", "aria-label": "Close", type: "button" } Cancel
            = submit_tag recording_url?(session) ? t("admin.actions.online_class.add_class_recording.edit") : t("admin.actions.online_class.add_class_recording.add"), class: "btn btn-primary text-uppercase add-recording-btn fs-14 fw-semibold m-0", disable_with: "Please wait..."
