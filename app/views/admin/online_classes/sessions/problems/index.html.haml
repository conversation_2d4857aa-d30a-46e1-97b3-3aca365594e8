.title-box
  .title.justify-content-between
    = link_to admin_online_class_path(@session.online_class, filter: "classes"), class: "link-container" do
      %i.fas.fa-chevron-left
      %span.schedule-time-detail Class-#{@index + 1} - #{@session.date.strftime('%b %d')}, #{session_schedule_time(@session)}
.container-fluid
  .top-header
    %span.q-box
      Q
    %h4= @session.description.html_safe
  .row.instructor-sections
    .col-12
      %h4.class-heading#assigned-instructor= t("admin.actions.online_class.home_work.class_homework")
      .row
        .col-12
          .homework-details.d-flex
            .d-flex.main-homework
              .homework
                %span.name= t("admin.actions.online_class.home_work.title")
                %p= t("admin.actions.online_class.home_work.sub_title_1", end_time: @session.end_time, date: @session.date.strftime("%b %d")).html_safe
                %p.title= t("admin.actions.online_class.home_work.sub_title_2")
              .ready-btn
                %h5= "Ready"
                %span.icon-red{ class: (@session.homework_status == "not_ready") ? "" : "green" }
              - if current_user.edit_access_to_admin_module?("Live Classes and Instructors")
                .show-homework-link.d-flex
                  - if @session.homework_status == "not_ready"
                    = link_to update_homework_status_admin_homework_problems_path(index: @index, session: @session, status: "ready_for_release"), method: :patch, remote: true, data: { confirm: "This will schedule the list of tasks to be sent when the class ends. Are you sure? (Once sent, the custom test can’t be unsent)" }, class: "btn btn-primary btn-lg show-overlay" do
                      = t("admin.actions.online_class.home_work.mark_as_ready")
                  - else
                    = link_to update_homework_status_admin_homework_problems_path(index: @index, session: @session, status: "not_ready"), method: :patch, remote: true, class: "btn btn-primary btn-lg show-overlay #{'disabled' if @session.homework_status == 'released'}" do
                      = t("admin.actions.online_class.home_work.mark_as_not_ready")
  .row.class-user-sections.mt-4
    .col-12.col-lg-10.col-md-8.mt-4
      %p.problems-include#problem-count= t("admin.actions.online_class.home_work.homework_tasks", tasks: @tasks.count)

    .col-12.col-lg-2.col-md-4.mt-4
      - if current_user.edit_access_to_admin_module?("Live Classes and Instructors")
        .d-flex.gap-2b.download-schedule-btn.justify-content-end
          = link_to "#", class: "btn btn-primary btn-lg add-another-problem-btn #{'disabled' if @session.homework_status != 'not_ready'}", data: { bs_toggle: "modal", bs_target: "#add-homework-task-modal" } do
            %i.fas.fa-plus
            = t("admin.actions.online_class.home_work.add_task")
  .scrollable-table-container.homework-table
    = render "homework_problems_table", tasks: @tasks, class_session: @session, prereading_sections: @prereading_sections, custom_sections: @custom_sections, chapter_name: @chapter_name

.container-fluid
  = render "homework_task_modal",
    chapters: @chapters,
    grouped_topics: @grouped_topics,
    session: @session,
    add_problems_path: fetch_add_problems_path(online_session: @session),
    chapters_path: chapters_admin_homework_problems_path,
    homework_task_type_path: homework_task_type_admin_homework_problems_path(online_class_session_id: @session),
    chapter_test_path: chapter_test_admin_homework_problems_path(online_class_session_id: @session)
