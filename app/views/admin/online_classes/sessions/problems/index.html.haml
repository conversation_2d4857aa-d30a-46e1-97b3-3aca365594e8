.title-box
  .title.justify-content-between
    = link_to (params[:bootcamp_page] == "true") ? admin_bootcamp_class_path(@session.online_class, filter: "classes") : admin_online_class_path(@session.online_class, filter: "classes"), class: "link-container" do
      %i.fas.fa-chevron-left
      %span.schedule-time-detail Class-#{@index + 1} - #{@session.date.strftime('%b %d')}, #{session_schedule_time(@session)}
.container-fluid
  .top-header
    %span.q-box
      Q
    %h4= @session.description.html_safe
  .row.instructor-sections
    .col-12
      %h4.class-heading#assigned-instructor= "Class Homework"
      .row
        .col-12
          .homework-details.d-flex
            .d-flex.main-homework
              .homework
                %span.name= t("admin.actions.online_class.home_work.title")
                %p= t("admin.actions.online_class.home_work.sub_title_1", end_time: @session.end_time, date: @session.date.strftime("%b %d")).html_safe
                %p.title= t("admin.actions.online_class.home_work.sub_title_2")
              .ready-btn
                %h5= "Ready"
                %span.icon-red{ class: @session.homework_status == "not_ready" ? "" : "green" }
              - if current_user.edit_access_to_admin_module?("Live Classes and Instructors")
                .show-homework-link.d-flex
                  - if @session.homework_status == "not_ready"
                    = link_to update_homework_status_admin_homework_problems_path(index: @index, session: @session, status: "ready_for_release"), method: :patch, remote: :true, data: { confirm: "This will schedule a custom test to be sent when the class ends. Are you sure? (Once sent, the custom test can’t be unsent)" }, class: "btn btn-primary btn-lg show-overlay" do
                      MARK AS READY
                  - else
                    = link_to update_homework_status_admin_homework_problems_path(index: @index, session: @session, status: "not_ready"), method: :patch, remote: :true, class: "btn btn-primary btn-lg show-overlay" do
                      MARK AS NOT READY
  .row.class-user-sections.mt-4
    .col-12.col-lg-10.col-md-8.mt-4
      %p.problems-include#problem-count Problems Included (#{@problems.count}):

    .col-12.col-lg-2.col-md-4.mt-4
      - if current_user.edit_access_to_admin_module?("Live Classes and Instructors")
        .d-grid.gap-2b.download-schedule-btn
          = link_to "#", class: "btn btn-primary btn-lg add-another-problem-btn #{'disabled' if @session.homework_status != 'not_ready'}", data: { bs_toggle: "modal", bs_target: "#add-problems-modal" } do
            %i.fas.fa-plus
            ADD PROBLEMS
  .scrollable-table-container.homework-table
    = render "homework_problems_table", problems: @problems, class_session: @session

.container-fluid
  = render "admin/components/problem_modal",
    chapters: @chapters,
    grouped_topics: @grouped_topics,
    session: @session,
    chapter_problem_path: chapter_problems_admin_homework_problems_path(online_class_session_id: @session),
    add_problems_path: fetch_add_problems_path(online_session: @session),
    chapters_path: chapters_admin_homework_problems_path
