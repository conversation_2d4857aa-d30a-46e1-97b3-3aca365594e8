.modal.fade.task-modal#add-homework-task-modal{ data: { homework_task_type_path: homework_task_type_path, chapters_path: chapters_path, chapter_test_path: chapter_test_path } }
  .modal-dialog.modal-lg.modal-dialog-scrollable.modal-dialog-centered
    .modal-content
      .modal-header
        %h5.modal-title= t("admin.actions.online_class.home_work.homework_task.title")
        %button.fas.fa-times.btn-close.close-modal-button.opacity-100{ "data-bs-dismiss": "modal", "aria-label": "Close", type: "button" }
      .modal-task-type
        %h6.fs-13.fw-semibold.task= t("admin.actions.online_class.home_work.homework_task.task_title")
        = select_tag :task_type,
          options_for_select(HomeworkTask::TASK_TYPES.values.map { |v| [v == "pre_reading" ? "Pre-reading" : v.titleize, v] }, "custom_test"), class: "form-select task-type-select-id fs-16"

      .modal-navigation
        %ul.nav.nav-pills.mx-2
          - grouped_topics.each_key do |section|
            %li.nav-item
              = link_to section&.capitalize, "##{section}-topics",
                class: "nav-link fs-13 fw-semibold text-align-center text-uppercase #{'active' if section == current_section}",
                data: { bs_toggle: "tab", section: section }
              = hidden_field_tag :section, current_section, name: "section"

      .modal-body
        .row
          .col-sm-12.search-input
            .chapters-select-field
              = render "chapters_field", chapters: chapters

        = hidden_field_tag :problem_ids, nil, class: "problems-id-input-field", multiple: true

        .table-content
          .empty-results
            .empty-result-image
              = image_tag(url_for("admin/problems/empty-example-bookmarks.svg"))
            %span.empty-result-message= t("admin.actions.online_class.home_work.homework_task.select_chapter")
          .table-results.d-none

      .modal-footer
        .selected-count.m-0.text-end.fs-18.fw-semibold.d-flex.justify-content-end.me-3
          .selected-count-label= t("admin.actions.online_class.home_work.homework_task.problem_selected")
          %span.selected-count-number.ms-1

        %button.btn.btn-outline-secondary.close-modal-button.fs-14{ data: { bs_dismiss: "modal" } } CANCEL
        %button.btn.btn-primary.ml-10.btn-save-problem.fs-14{ type: "button", data: { "bs-dismiss": "modal", add_problems_path: add_problems_path } }
          = t("admin.actions.online_class.home_work.homework_task.create_task")
