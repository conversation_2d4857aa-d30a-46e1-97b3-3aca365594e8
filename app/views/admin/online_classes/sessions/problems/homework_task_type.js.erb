var problem_ids = $('.problems-id-input-field').val() || 0;
$('.selected-count').removeClass('d-none');

<% if params[:type] == 'custom_test' and @problems.present? %>
  $('.empty-results').addClass('d-none');
  $('.table-results').removeClass('d-none');
  $('.selected-count').removeClass('d-none');

  $('.table-results').html("<%= j render 'admin/diagnostic_problems/content_table', problems: @problems %>");

  if (problem_ids != 0) {
    problem_ids.split(',').forEach(function(problem_id) {
      $('.problem-checkbox-field:input[data-problem-id="' + parseInt(problem_id) + '"]').prop('checked', true);
    });
  }

  $('.modal-navigation a.nav-link').removeClass('active');
  $("a[data-section='<%= current_section %>']").addClass('active');
  $('.problems-found').html('<%= @problems.count %>');

  reprocess_math();

<% elsif params[:type] == 'pre_reading' and @topics.present? %>
  $('.empty-results').addClass('d-none');
  $('.table-results').removeClass('d-none');
  $('.selected-count').removeClass('d-none');

  $('.table-results').html("<%= j render 'admin/online_classes/sessions/problems/prereading_content_page', topics: @topics %>");

  if (problem_ids != 0) {
    problem_ids.split(',').forEach(function(problem_id) {
      $('.problem-checkbox-field:input[data-problem-id="' + parseInt(problem_id) + '"]').prop('checked', true);
    });
  }

  $('.modal-navigation a.nav-link').removeClass('active');
  $("a[data-section='<%= current_section %>']").addClass('active');
  $('.problems-found').html('<%= @topics.count %>');

  reprocess_math();

<% else %>
  $('.empty-results').removeClass('d-none');
  $('.table-results').addClass('d-none');
  $('.empty-result-message').html('No results were found.');
  $('.empty-result-image').html("<%= j image_tag('admin/problems/not-found.svg') %>");
  $('.problems-found').html('0');
<% end %>

var total_checked_problems = 0;
if (problem_ids != 0) {
  total_checked_problems = problem_ids.split(',').length;
}
total_checked_problems = total_checked_problems == 0 ? '' : '(' + total_checked_problems + ')';
$('#add-homework-task-modal .btn-save-problem span').text(total_checked_problems);
$('#loading-overlay').hide();
