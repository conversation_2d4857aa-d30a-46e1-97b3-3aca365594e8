- empty_class = tasks.present? ? nil : "empty-task-list"
- date_class = (tasks.present? ? nil : "hide-date")

%table.ttp-table.table.table-borderless.table-custom-border.mt-4{ class: empty_class, id: "homework-problems-table" }
  %thead.table-custom-border
    %tr
      %th.sticky-top= t("admin.actions.online_class.home_work.homework_task.type")
      %th.sticky-top.text-start.w-500= t("admin.actions.online_class.home_work.homework_task.content")
      %th.sticky-top.text-center{ class: date_class, id: "task-date-created" }= t("admin.actions.online_class.home_work.homework_task.created_at")
      %th.sticky-top.text-center{ class: date_class, id: "task-date-updated" }= t("admin.actions.online_class.home_work.homework_task.updated_at")
      - if current_user.edit_access_to_admin_module?("Live Classes and Instructors")
        %th.sticky-top.text-center= t("admin.actions.online_class.home_work.homework_task.action")
  %tbody
    - if tasks.present?
      - tasks.each do |task|
        %tr.homework-problems{ id: "homework-task-#{task.id}" }
          %td.task-type.fw-bold= task.task_type.titleize
          %td.text-start
            - if task.task_type == "custom_test"
              %span
                = t("admin.actions.online_class.home_work.homework_task.task_type.custom_test")
              %span.text-count.fw-semibold= t("admin.actions.online_class.home_work.homework_task.task_type.custom_test_1", count: task.data.count, type: custom_sections)
            - elsif task.task_type == "chapter_test"
              %span
                = t("admin.actions.online_class.home_work.homework_task.task_type.chapter_test", count: task.data.count)
              %span.text-count.fw-semibold= t("admin.actions.online_class.home_work.homework_task.task_type.chapter_test_1", chapter_name: chapter_name.to_sentence)
            - else
              %span
                = t("admin.actions.online_class.home_work.homework_task.task_type.prereading")
              %span.text-count.fw-semibold= t("admin.actions.online_class.home_work.homework_task.task_type.prereading_1", count: task.data.count, type: prereading_sections)
          %td.created_at.text-center= task.created_at.strftime("%d/%m/%y")
          %td.last-edited.text-center= task.updated_at.strftime("%d/%m/%y")
          - if current_user.edit_access_to_admin_module?("Live Classes and Instructors")
            %td.actions.text-center.overflow-visible
              - disabled_link = class_session.homework_status != "not_ready"
              = link_to_unless disabled_link, "<i class=\"fas fa-pencil #{'disabled ms-2' if disabled_link}\" data-bs_toggle='tooltip' data-bs_placement='bottom' title='Edit'></i>".html_safe, "#", remote: true, class: "ms-2 update-task-link", data: { bs_toggle: "modal", bs_target: "#add-homework-task-modal", task_type: task.task_type, task_data: task.data, edit_path: edit_homework_task_admin_homework_problems_path }
              = link_to_unless disabled_link, "<i class=\"fas fa-trash #{'disabled ms-2' if disabled_link}\" data-bs_toggle='tooltip' data-bs_placement='bottom' title='Delete'></i>".html_safe, admin_homework_problem_path(task, session: class_session), method: :delete, remote: true, class: "ms-2", data: { confirm: "Are you sure?" }
    - else
      = render "empty_problem_list"
