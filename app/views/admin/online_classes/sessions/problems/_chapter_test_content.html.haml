%table.ttp-table.table.table-borderless.diagnostic-problems-table
  %tbody
    - chapter_test_level.each_with_index do |level, index|
      - next if level.blank?

      - level_name = Exercise::LEVELS[level.to_sym]
      %tr.problem-description
        %td
          .problem-content
            #{chapter.name} - #{level_name.titleize}
        %td
          = check_box_tag :problems_ids, chapter.id, false,
            class: "problem-checkbox-field",
            id: "problem-index-#{index}",
            data: { problem_id: "#{chapter.id}-#{level}" },
            multiple: true
          %label.fas{ for: "problems_ids" }
        %td
