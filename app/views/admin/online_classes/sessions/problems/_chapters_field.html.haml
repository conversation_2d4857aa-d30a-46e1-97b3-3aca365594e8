- options = options_for_select(chapters.where(section: current_section).map { |chapter| [chapter.name, chapter.id] })
= select_tag :chapter_id, options, { include_blank: "Select a Chapter", class: "form-select problems-modal-chapter-id" }
.input-group.mb-3.d-none
  = text_field_tag :query, params[:query], class: "form-control form-control-lg search-problems", placeholder: ""
  %button.button-search.input-group-text.d-sm-flex
    %i.fas.fa-search
