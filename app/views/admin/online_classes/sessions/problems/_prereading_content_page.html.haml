%table.ttp-table.table.table-borderless.diagnostic-problems-table
  %tbody
    - topics.each_with_index do |topic, index|
      %tr.problem-description
        %td
          .problem-content= topic.name
        %td
          = link_to lesson_path(topic), target: "_blank" do
            %i.fal.fa-eye.float-end.detail-icon
        %td.prereading
          = check_box_tag :problems_ids, topic.id, false,
            class: "problem-checkbox-field",
            id: "problem-index-#{index}",
            data: { problem_id: topic.id },
            multiple: true
          %label.fas{ for: "problem-index-#{index}" }
