:ruby
  is_bootcamp_page = local_assigns[:is_bootcamp_page] || false
.container-fluid
  .row
    .col-12
      .row.class-user-sections
        .col-12.col-lg-6.pt-2
          %h4#class-dates= "Class Dates"

      .mt-4.sessions
        %table.ttp-table.table.table-borderless.table-custom-border#online-class-schedules
          %thead.table-custom-border
            %tr
              %th.session-date Dates
              %th.session-description Description
              %th.session-description.text-center HomeWork Status
              %th.session-description.text-center Class Recording
              %th

          %tbody
            - sorted_online_class_sessions(online_class).each_with_index do |session, index|
              %tr.online-class-session{ data: { path: admin_homework_problems_path(index: index, session: session.id) } }
                %td.session-detail
                  %span.session-date= session.date.strftime("%B %d, %Y")
                  %p.session-time.mb-0= session_schedule_time(session)
                %td.instructor-description
                  %p= session.description.html_safe
                %td
                  .status{ class: session.homework_status.gsub("_", "-") }
                    %p= session.homework_status.gsub("_", " ").upcase
                %td
                  .status{ class: class_recording_status(session).gsub("_", "-") }
                    %p= class_recording_status(session).gsub("_", " ").upcase
                %td
                  .mark-arrow.justify-content-end.recording-actions.mb-2
                    = link_to admin_homework_problems_path(index: index, session: session.id, bootcamp_page: is_bootcamp_page), class: "show-overlay edit-recording me-3" do
                      %i.fal.fa-edit
                    = link_to "#", class: "add-recording", data: { bs_toggle: "modal", bs_target: "#add-class-recording-#{session.id}" } do
                      %img{ src: asset_url("admin/controllers/online_classes/video_icon.svg") }

                    = render "add_class_recording_modal", session: session
