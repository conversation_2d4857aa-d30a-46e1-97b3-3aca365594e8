.card.bg-light.mb-3.border-0{ id: "lesson-feedback-#{lesson_feedback.id}" }
  .card-body
    .card-title.d-flex.justify-content-between.align-items-start
      %div
        = link_to lesson_feedback.lesson.name, lesson_path(lesson_feedback.lesson), class: "h4 m-0"
        %p.small= lesson_feedback.lesson.chapter.name
        %p.small.text-secondary.mb-0 Date: #{lesson_feedback.created_at.strftime("%-m/%-d/%Y")}
        - if lesson_feedback.created_at.to_date != lesson_feedback.updated_at.to_date
          %p.small.text-secondary.mb-0 Updated: #{lesson_feedback.updated_at.strftime("%-m/%-d/%Y")}
      %div
        %h3.bg-white.ms-3.px-4.py-2.text-primary.font-weight-bold= lesson_feedback.score
    - if current_user.edit_access_to_admin_module?("Lessons and Examples")
      %p
        - if lesson_feedback.resolved?
          = link_to "MARK AS UNRESOLVED", unresolve_admin_lesson_feedback_path(lesson_feedback), method: :put, remote: true, class: "btn btn-outline-secondary float-end show-overlay"
        - else
          = link_to "MARK AS RESOLVED", resolve_admin_lesson_feedback_path(lesson_feedback), method: :put, remote: true, class: "btn btn-primary float-end show-overlay"

    = lesson_feedback.comment
