%section.modal.auto-trigger#subscription-thank-you
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-body
        .container
          .row
            .col.d-flex.flex-column.align-items-center
              %button.align-self-center.btn-close.position-absolute.end-0.top-0.mt-4.me-4{ type: "button", data: { bs_dismiss: "modal" } }
                %span.visually-hidden Close
              = image_tag(asset_path("layout/controllers/subscriptions/invoice.svg"), width: 90, height: "auto", alt: "")
              %h3.text-dark-1.my-3= t("user.subscriptions.manage.thank_you_modal.title")
              - plan_code = (future_plan || current_plan).try(:plan_code)
              %p.text-grey-1.mb-0.text-center
                - plan_name = t("user.subscriptions.manage.your_plan.plan_type.#{current_plan.plan_code.split('-').first}.title", exam_name: EXAM_NAME.upcase)

                = t("user.subscriptions.manage.thank_you_modal.body", plan_name: plan_name).html_safe

          .row.justify-content-center
            %button.btn.btn-outline-primary.mt-4.col-3{ type: "button", data: { bs_dismiss: "modal" } } okay
