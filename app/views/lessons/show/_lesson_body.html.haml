:ruby
  locked_repository = ondemand_locked_repository?(current_user, lesson.chapter)

#lesson-content-wrapper.m-0
  - if coordinate_geometry_lesson_bar?(lesson)
    .coordinate-geometry-lesson-bar.d-flex.justify-content-between.container{ class: coordinate_geometry_tooltip? ? nil : "d-none" }
      .d-flex.align-items-center.coordinate-geometry-wrapper
        .info-image
        %h6.m-0.d-md-flex.flex-md-col
          = t("user.lessons.show.coordinate_geometry.title", exam: exam_name_display_title(include_exam_type: true)).html_safe
      %button.btn-close{ "aria-label" => "Close", :type => "button", data: { flag_url: flag_path(id: current_user.id, type: "user", label: :coordinate_geometry_acknowledged) } }

  .main-content.container.lesson-content{ class: ("bottom-corner" if coordinate_geometry_lesson_bar?(lesson)) }
    .title-wrapper
      %h1.title.chapter-name{ data: { chapter_id: lesson.chapter.id } }= LessonContentPresenter.lesson_title(lesson.chapter, @track)

    .subtitle-wrapper
      %h2.subtitle.topic-name.text-grey-1= LessonContentPresenter.lesson_subtitle(lesson, @track)

    .content.noteable.lesson-description
      - if (current_user.ondemand_active? and !locked_repository) and lesson.videos.any?
        = render "lessons/shared/ondemand/lessons_video_container", lesson: lesson

      - if current_user.ondemand_active? and !locked_repository
        = render "lessons/shared/ondemand/read_lesson_container"

      = noteable_content(generate_content(lesson)).html_safe

      - if @topic.present?
        - [nil, @topics_to_show, nil].flatten.each_cons(3) do |_previous, current, following|
          - if current.id == lesson.id and !following
            .end-of-topic.text-center
              - if @topic.chapter.name.downcase.include?("strategy")
                %span.strategy
                  = image_tag("layout/controllers/lessons/show/strategy.svg")
                  %h3.text-grey-1
                    = t("user.lessons.show.completed_strategy_reading")

      - unless lesson.archived?
        .lesson-feedback-wrapper
          = render "lesson_feedbacks/new", lesson: lesson, lesson_feedback: current_user.lesson_feedbacks.find_or_initialize_by(lesson_id: lesson.id)

      = render "notes/index", noteable: @lesson
  - if ai_summaries_enabled(lesson)
    = render "ai_assist/summaries/summary_loader"

  #summarize-result-loader
  - if ai_summaries_enabled(lesson) and !lesson.archived? and !(chapter.present? or topic.present?)

    .summaries-navigation.bg-white.py-1.position-absolute.bottom-0.w-100
      .container
        .d-flex.align-items-center.justify-content-between.my-4.px-0.flex-md-row.flex-column.summaries-navigation-row
          = render "ai_assist/summaries/summary_lesson_navigation", chapter: @chapter, topic: @topic, lesson: @lesson, topics_to_show: @topics_to_show, lessons_navigator: @lessons_navigator
  - else
    - unless lesson.archived?
      .navigation.d-flex.align-items-center.justify-content-between.my-4.px-0.container.mx-auto
        = render "lessons/show/lesson_navigation_with_buttons", chapter: @chapter, topic: @topic, lesson: @lesson, topics_to_show: @topics_to_show, lessons_navigator: @lessons_navigator

- unless lesson.archived?
  = render "lessons/show/lesson_navigation_with_arrows"

- if ai_assist_enabled(lesson)
  #ai-assist
    = render "components/open_chat_ai/chat_sidebar", lesson: lesson
    = render "components/open_chat_ai/chat_resize_modal"
    - if lesson.section == "quant" or lesson.section == "verbal"
      = render "components/open_chat_ai/chat_ai_assist_modal", lesson: lesson

    .chat-component.d-none{ data: { section: lesson.section } }
      = render "components/open_chat_ai/chat_modal", lesson: lesson

.lesson-sidebar
  = render "lessons/show/lesson_sidebar", lesson: lesson

.modal.fade#reference-guide-modal{ "aria-hidden": "true", tabindex: "-1" }
  .modal-dialog.modal-lg.modal-dialog-centered
    .modal-content
      .modal-header
        %h5.modal-title
          Reference Guide
        %button.fas.btn-close{ "data-bs-dismiss": "modal", "aria-label": "Close", type: "button" }
      .modal-body
        .equations.hidden-mobile
          = image_tag("layout/sat/equations.svg")
        .mobile-equations.visible-mobile
          = image_tag("layout/sat/mobile_equations.svg")

- if gmat_exam?
  = render "components/coordinate_geometry_modal"

.chat-feedback-modals
= form_tag load_ai_assist_summaries_ai_assist_summaries_path(lesson_id: lesson.id), method: :get, remote: true, class: "submit-on-ready"

- if ai_assist_enabled(lesson)
  = render "ai_assist/custom_intercom_button"
