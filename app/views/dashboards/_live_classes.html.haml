%section.live_classes.bg-light-1.flex-grow-1.pb-3
  .container
    .row
      .col-lg-4.col-md-5.col-12
        - if next_session_by_class.present?
          .card.live-class-card
            .card-header
              .card-title.fs-18.fw-semibold.d-flex.live-class-title-card.m-0
                %i.fas.fa-users-class
                .title= t("user.home.live_classes.next_class")
            .card-body
              .card-date.fs-14.fw-semibold
                %strong NEXT :
                %span #{next_session_by_class.date.strftime('%b %-d')}  |  #{next_session_by_class.start_time.gsub(/ AM$| PM$/, '')} - #{next_session_by_class.end_time}
              %h3.session-title.fs-18.fw-bold Session #{next_session_by_class.number_of_session}
              %p.session-detail.fs-16.fw-normal
                %span= next_session_by_class.description.html_safe
        .card.live-class-card.cohort-card
          .card-header
            .card-title.fs-18.fw-semibold.d-flex.live-class-title-card.m-0
              %i.fas.fa-user
              .title= t("user.home.live_classes.cohort_info")
          .card-body
            - if gmat_exam?
              .d-flex.gap-2
                .exam-logo.align-items-center
                  %span= exam_name_display_title(include_exam_type: true)
            .d-flex.profile.gap-3
              %img= image_tag(asset_url(online_class.instructor.image_url), class: "instructor-img")
              %div
                %strong.fs-16.fw-bold.profile-title= online_class.instructor.name
                %p.fs-16.fw-normal.profile-dis= online_class.instructor.title

      .col-12.col-lg-8.col-md-7
        .card.live-class-card.cohort-card
          .card-header
            .card-title.fs-18.fw-semibold.d-flex.live-class-title-card.m-0.align-items-center
              %i.fal.fa-calendar
              .title.flex-1.w-100= t("user.home.live_classes.schedule")
              = link_to online_class.syllabus_url, class: "fs-14 fw-semibold schedule-download-btn", download: "syllabus_#{online_class.id}.pdf", target: "_blank" do
                = t("user.home.live_classes.download_pdf")
                %i.far.fa-arrow-to-bottom.ps-2
          .card-body.live-classes-details
            %ul.online-class-sessions-list.p-0.m-0
              - online_class.sessions.sort_by { |session| session[:date] }.each do |online_class_session|
                %li.online-class-session.list-unstyled
                  - is_completed = ActiveSupport::TimeZone["Eastern Time (US & Canada)"].parse("#{online_class_session.date} #{online_class_session.end_time}") < Time.current
                  - is_next_class = online_class_session.id == next_session_by_class&.id
                  .bar{ class: is_completed ? "completed" : "bg-grey-6" }
                  - if is_next_class
                    .next-item-indicator
                      = t("user.home.live_classes.indicator.next")
                  - else
                    %i.status{ class: is_completed ? "completed fas fa-check" : "" }
                  .section-bg-color-5-hover.ms-lg-2.ms-md-0.ms-2.classes-sessions-detail-sections
                    .schedule-card.current{ class: is_next_class ? "active" : "" }
                      .row
                        .col-lg-4.col-md-12.schedule-date-card
                          %strong.schedule-card-title.fs-18.fw-semibold= online_class_session.date.strftime("%b %-d")
                          %p.schedule-time.fs-16.fw-normal.w-lg-75.w-md-100 #{online_class_session.start_time} - #{online_class_session.end_time} (ET)
                        .col-lg-8.col-md-12.d-md-block.d-flex.flex-column.schedule-card-session
                          .schedule-card-title.fs-18.fw-semibold Session #{online_class_session.number_of_session}
                          %p.fs-16.fw-normal.description
                            %p.description= online_class_session.description.html_safe
