:ruby
  instructor_comment = session_homework.comment
  instructor = instructor_comment&.instructor if instructor_comment.present?

.session-homework.others{ class: "#{classes.join(' ')} #{'active' if show_next_indicator}" }
  .next-item-indicator{ class: show_next_indicator ? "d-block" : "d-none" }= t("user.home.live_classes.indicator.next")
  %a{ class: "header #{show_next_indicator ? '' : 'collapsed'}", role: "button", data: { bs_toggle: "collapse", bs_target: "#session-homework-#{session_homework.id}-content" } }
    .header-info
      .status-icon
        .icon-container
          %i.fal.fa-trophy.trophy-icon{ class: (completion[:percentage] == 100) ? "completed" : "" }
          %i.fa.fa-check-circle.check-circle-icon{ class: (completion[:percentage] == 100) ? "completed" : "" }

        .knob-container
          %input.knob{ disabled: true, type: "text", value: completion[:percentage], data: { fgColor: "#57C292", bgColor: "#F0F2F5", width: "100%", height: "100%", thickness: ".1", displayInput: "false" } }

      .header-title{ class: completed ? "text-decoration-line-through text-grey-4" : "" }= session_homework.title&.html_safe
    .completion-status.d-flex.align-items-center.gap-2{ class: (completed ? "completed" : "") }
      %span.text-grey-2.completed-text{ class: (completion[:percentage] == 100) ? "text-grey-5 completed" : "d-none" }= "Completed"
      %span.start-text{ class: (completion[:percentage] == 100) ? "d-none" : "" }= "To Start"
    .arrows
      %i.far.fa-chevron-down
      %i.far.fa-chevron-up

  %div{ id: "session-homework-#{session_homework.id}-content", class: "collapse main-content #{show_next_indicator ? 'show' : ''}" }
    .content
      %p.fw-normal= session_homework.description&.html_safe
      - if instructor_comment.present?
        .homework-task-comments.d-flex.d-none
          .instructor-image= image_tag(instructor.image.url, class: "rounded-circle", alt: instructor.name)
          .comment-info
            .comment-box-top-arrow.d-block.d-md-none
              = image_tag(asset_url("layout/controllers/online_classes/comment_box_top_arrow.webp"))
            .comment-box-left-arrow.d-none.d-md-block
              = image_tag(asset_url("layout/controllers/online_classes/comment_box_left_arrow.webp"))
            .instructor-name= instructor.name
            .comment= instructor_comment.comment
      .d-flex.align-items-end.justify-content-between.gap-3.comments-btn-section
        .mark-complete-btn
          = check_box_tag "homework_task", "1", completed, id: "homework-task-#{session_homework.id}-checkbox", class: "awesome-checkbox homework-task-completion", data: { label_content_unchecked: "Mark as Complete", label_content_checked: "Mark as Incomplete", label_class_checked: "btn btn-outline-secondary", label_class_unchecked: "btn btn-outline-primary", url: toggle_flag_path(id: session_homework.id, type: "homework_task", label: :completed) }
        .my-task-takeaways
          = link_to "#", class: "takeways-link", data: { bs_toggle: "modal", bs_target: "#homework-takeaways-modal-#{session_homework.id}" } do
            %i.far.fa-sticky-note.open-file-icon
            %span= t("user.home.next_liveteach_class.homework_task_takeaways.takeaways_title").html_safe

= render "dashboards/homework_task_takeaways_modal", session_homework: session_homework
