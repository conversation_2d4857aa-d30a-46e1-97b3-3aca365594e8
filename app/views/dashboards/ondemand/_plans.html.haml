.row.mb-5.d-flex.flex-column.plan-details
  %h2.fw-semibold.mb-3.plan-header.fs-35.fw-semibold
    %span.title= t("user.home.ondemand.plans.title", exam: exam_name_display_title(include_exam_type: true)).html_safe
    .line-decorator-title.mt-2
.row
  .col-12.mx-md-4.mx-0
    .d-flex.justify-content-end
      .d-flex.plan-table.header.align-items-center
        .d-inline.header-text
          %span.fs-20.fw-semibold= t("user.home.ondemand.plans.plans_table.head.other_ttp")
          %span.fs-20.fw-semibold.self-study-plans-head= t("user.home.ondemand.plans.plans_table.head.self_study")
        %span
          %img.logo-img{ src: asset_url("layout/controllers/ondemand/shared/ondemand_logo.svg") }
    %hr.w-100.plan-divider.m-0
    %table.table.align-middle.plan-table
      %tbody
        %tr
          %td{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.score_improvement")
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.guarantee")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.unmatched")
          %td.text-grey-1.fw-semibold= t("user.home.ondemand.plans.plans_table.body.point")
          %td
            %p.count.fs-16.fw-bold.m-0= t("user.home.ondemand.plans.plans_table.body.score")
            %p.fs-16.fw-semibold.m-0= t("user.home.ondemand.plans.plans_table.body.percentile")
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.streaming_video")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.led")
          %td.text-grey-2.fw-semibold= t("user.home.ondemand.plans.plans_table.body.video_solution_count")
          %td
            %p.streaming-video.fs-16.fw-semibold.m-0.count= t("user.home.ondemand.plans.plans_table.body.hours_count").html_safe
            %p.fw-semibold.m-0= t("user.home.ondemand.plans.plans_table.body.video_plus_count")
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.weekly_online")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.with_ttp", exam: exam_name_display_title(include_exam_type: true)).html_safe
          %td.text-center.app-color-2
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.6_month")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.self_study", exam: exam_name_display_title(include_exam_type: true)).html_safe
          %td.text-center.app-color-2
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.personal_study")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.to_guide")
          %td.text-center.app-color-2
            %i.fas.fa-check
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.lesson_count")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.covering", exam: exam_name_display_title(include_exam_type: true)).html_safe
          %td.text-center.app-color-2
            %i.fas.fa-check
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.quants_count")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.question_count")
          %td.text-center.app-color-2
            %i.fas.fa-check
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.quant_count")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.custom_flashcard")
          %td.text-center.app-color-2
            %i.fas.fa-check
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.custom_exam", exam: exam_name_display_title(include_exam_type: true)).html_safe
          %td.text-center.app-color-2
            %i.fas.fa-check
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.intelligent_analytics")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.target_weakness")
          %td.text-center.app-color-2
            %i.fas.fa-check
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr.ai-assist-display
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.ai_assist")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.your_personalized")
          %td.text-center.app-color-2
            %i.fas.fa-check
          %td.text-center.app-color-2.golden-check
            %i.fas.fa-check
        %tr
          %td.ps-0{ colspan: 2 }
            %span.dot-color.me-3 ●
            %span
              %span.fs-16.fw-semibold= t("user.home.ondemand.plans.plans_table.body.online_support")
              %span.fs-16= t("user.home.ondemand.plans.plans_table.body.team")
          %td.text-center.app-color-2
            <span class="text-grey-2 fw-semibold">24/7 live support</span>
          %td.text-center.app-color-2.golden-check
            <span class="text-gold fw-semibold">24/7 live support</span>

.row
  .try-ttp-ondemand-for-free.d-flex.align-items-center.mx-md-4.mx-0
    %hr.left
    .try-ondemand-for-free-btn
      - if current_user.present?
        - if current_user.free_account_access? or current_user.ondemand_enabled?
          = link_to cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "profile-btn fs-16 fw-semibold text-center" do
            = t("user.home.ondemand.unlock_ondemand.purchase_ondemand")
        - else
          %button.profile-btn.fs-16.text-center.fw-semibold{ data: { bs_toggle: "modal", bs_target: "#try-ondemand" } }
            = t("user.home.ondemand.unlock_ondemand.try_ttp_ondemand_for_free")
      - else
        = link_to t("user.home.ondemand.unlock_ondemand.try_ttp_ondemand_for_free"), cart_path(cart: { plan_code: Setting.for("recurly_5days_ondemand_plan_code") }), method: :post, class: "plan-f profile-btn fs-16 text-center fw-semibold"
    %hr.right
