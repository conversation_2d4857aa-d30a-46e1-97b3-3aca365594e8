:ruby
  if current_user.present?
    upgrade_btn = current_user.free_account_access? ? "UNLOCK" : "UPGRADE"
    upgrade_price, _discount = RecurlyProxy.calculate_ondemand_price(current_user)
    upgrade_price = number_with_delimiter(format("%.2f", upgrade_price))
    path = (params[:action] == "ondemand" and params[:controller] == "dashboards") ? home_path(ondemand: true) : study_plan_path(ondemand: true)
  end
= video_tag(asset_path("layout/controllers/ondemand/shared/course_banner_bg.mp4"), alt: "", class: "course-banner-video", autoplay: true, muted: true, loop: true, playsinline: true)
.row.d-flex.flex-column.course-banner-video-text
  %h3.fs-45.fw-semibold.course-banner-title.d-flex.mb-0
    %span.text-uppercase= t("user.home.ondemand.course_banner.ttp", exam: exam_name_display_title(include_exam_type: true))
    %img.ms-2.logo-img{ src: asset_url("layout/controllers/ondemand/shared/ondemand_logo.svg") }
  %h3.fs-40.fw-semibold.course-banner-title= t("user.home.ondemand.course_banner.most_comprehensive", exam: exam_name_display_title(include_exam_type: true)).html_safe
  %p.fs-18.fw-normal.course-banner-detail
    = t("user.home.ondemand.course_banner.description").html_safe
  .d-flex.mt-3.flex-md-row.flex-column.align-items-md-start.align-items-center
    - if current_user.present?
      %button.show-overlay-ondemand.try-free-btn.fs-18.fw-semibold.mb-md-0.mb-4.text-center{ data: { bs_toggle: "modal", bs_target: "#try-ondemand" }, class: ("d-none" if current_user&.free_account_access? or current_user&.ondemand_enabled?) }
        = t("user.home.ondemand.unlock_ondemand.try_now_for_free")
      = link_to cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "upgrade-btn fs-18 fw-semibold ms-md-3 text-uppercase ms-0 text-center #{current_user&.free_account_access? ? 'free-account-user' : ''}" do
        = t("user.home.ondemand.course_banner.upgrade_btn", upgrade_btn: upgrade_btn, upgrade_price: upgrade_price)
    - else
      .prices
        .price-wrapper.pb-3.d-flex.mt-3.flex-md-row.flex-column.align-items-md-start.align-items-center
          = link_to t("user.home.ondemand.unlock_ondemand.try_now_for_free"), cart_path(cart: { plan_code: Setting.for("recurly_5days_ondemand_plan_code") }), method: :post, class: "plan-f try-free-btn text-uppercase fs-16 fw-semibold mb-md-0 mb-4 text-center d-flex justify-content-center align-items-center"

          = link_to t("user.plans.purchase_now_with_price").html_safe, cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "plan-f upgrade-btn fs-18 fw-semibold ms-md-3 text-uppercase ms-0 text-center ms-md-3 ms-0"

        - if affirm_enabled?
          .monthly-wrapper.d-flex.align-items-baseline.pt-3.flex-sm-row.flex-column
            .d-flex.align-items-baseline.pb-2.pb-md-0.justify-content-sm-start.justify-content-center.monthly-price
              - price = (t("user.plans.plan_f.sale_price").to_s.delete(",").to_i * 1.1698 / 12).ceil
              %p.one-month-price.fs-16.m-0= t("user.home.ondemand.course_banner.monthly_price", price: price)
              %img.ms-2.me-4{ src: asset_url("layout/controllers/dashboards/ondemand/affirm_logo.svg") }
            .d-flex.align-items-baseline.justify-content-sm-start.justify-content-center.monthly-price
              %p.yearly-price.fs-14.fw-semibold.m-0.pe-1= t("user.home.ondemand.course_banner.self_study_month")
              %span.access-price.fs-14.fw-semibold= t("user.home.ondemand.course_banner.access_included")
