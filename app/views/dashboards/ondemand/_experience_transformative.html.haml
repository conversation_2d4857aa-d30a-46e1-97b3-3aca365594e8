.row.transformative-bg-img
  .col-md-6.p-0.transformative-row
    %h3.fs-32.fw-semibold.text-light
      = t("user.home.ondemand.experience_transformative.title")
    .line-decorator-title.mx-lg-0.mt-3
    .purchase-btn-group.d-flex.flex-column
      - if current_user.present?
        - if current_user.free_account_access? or current_user.ondemand_enabled?
          = link_to cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "d-flex justify-content-center profile-btn fs-16 fw-semibold text-center" do
            = t("user.home.ondemand.unlock_ondemand.purchase_ondemand")
        - else
          %button.profile-btn.fs-16.text-center.fw-semibold{ data: { bs_toggle: "modal", bs_target: "#try-ondemand" } }
            = t("user.home.ondemand.unlock_ondemand.try_now_for_free")

          = link_to t("user.plans.or_purchase").html_safe, cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "plan-f fs-16 purchase-now-btn text-center fw-semibold text-white mt-2"
      - else
        = link_to t("user.home.ondemand.unlock_ondemand.try_now_for_free"), cart_path(cart: { plan_code: Setting.for("recurly_5days_ondemand_plan_code") }), method: :post, class: "plan-f profile-btn fs-16 text-center fw-semibold"

        = link_to t("user.plans.or_purchase").html_safe, cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "plan-f fs-16 purchase-now-btn text-center fw-semibold text-white mt-2"
