.container-box
  %section.profile.d-flex.justify-content-between.flex-lg-row.flex-column
    .description.fs-26
      %p.profile-title.text-white.mb-0
        %span.fw-semibold= t("user.home.ondemand.unlock_ondemand.unlock_full_potential")
        %span.ps-0= t("user.home.ondemand.unlock_ondemand.with_supercharged")
      %p.profile-detail.text-white.m-0= t("user.home.ondemand.unlock_ondemand.ondemand_experienced")
    .purchase-btn-group.d-flex.flex-column
      - if current_user.present?
        - if current_user.free_account_access? or current_user.ondemand_enabled?
          = link_to cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "profile-btn fs-16 fw-semibold text-center" do
            = t("user.home.ondemand.unlock_ondemand.purchase_ondemand")
        - else
          %button.profile-btn.fs-16.text-center.fw-semibold{ data: { bs_toggle: "modal", bs_target: "#try-ondemand" } }
            = t("user.home.ondemand.unlock_ondemand.try_now_for_free")

          = link_to t("user.plans.or_purchase").html_safe, cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "plan-f purchase-now-btn fs-16 text-center fw-semibold text-white mt-2"
      - else
        = link_to t("user.home.ondemand.unlock_ondemand.try_now_for_free"), cart_path(cart: { plan_code: Setting.for("recurly_5days_ondemand_plan_code") }), method: :post, class: "plan-f profile-btn fs-16 text-center fw-semibold"

        = link_to t("user.plans.or_purchase").html_safe, cart_path(cart: { plan_code: Setting.for("recurly_default_ondemand_plan_code") }), method: :post, class: "plan-f purchase-now-btn fs-16 text-center fw-semibold text-white mt-2"
