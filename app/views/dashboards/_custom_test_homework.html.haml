:ruby
  instructor_comment = session_homework.comment
  instructor = instructor_comment&.instructor if instructor_comment.present?

.session-homework.custom-test{ class: "#{classes.join(' ')} #{'active' if show_next_indicator} #{completed ? 'completed' : 'incomplete'}" }
  .next-item-indicator{ class: show_next_indicator ? "d-block" : "d-none" }= t("user.home.live_classes.indicator.next")
  %a{ class: "header #{show_next_indicator ? '' : 'collapsed'}", role: "button", data: { bs_toggle: "collapse", bs_target: "#session-homework-#{session_homework.id}-content" } }
    .header-info
      .status-icon
        .icon-container
          %i.fal.fa-trophy{ class: (completion[:percentage] >= 100) ? "completed" : "" }
          %i.fa.fa-check-circle.check-circle-icon{ class: (completion[:percentage] >= 100) ? "completed" : "" }

        .knob-container
          %input.knob{ disabled: true, type: "text", value: completion[:percentage], data: { fgColor: "#57C292", bgColor: "#F0F2F5", width: "100%", height: "100%", thickness: ".1", displayInput: "false" } }

      .header-title{ class: completed ? "text-decoration-line-through text-grey-4" : "" }= session_homework.title&.html_safe
    .completion-status.d-flex.align-items-center.gap-2{ class: (completed ? "completed" : "") }
      %span.text-grey-2{ class: completed ? "completed text-grey-5" : "d-none" }= "Completed"
      %span.text-green-2{ class: (completion[:percentage].positive? and completion[:percentage] < 100) ? "" : "d-none" }= "In Progress"
      %span{ class: completion[:to_start] ? "" : "d-none" }= "To Start"
    .arrows
      %i.far.fa-chevron-down
      %i.far.fa-chevron-up

  %div{ id: "session-homework-#{session_homework.id}-content", class: "collapse main-content #{show_next_indicator ? 'show' : ''}" }
    .content
      %p.fw-normal= session_homework.description&.html_safe
      - if instructor_comment.present?
        .homework-task-comments.d-flex
          .instructor-image= image_tag(instructor.image.url, class: "rounded-circle", alt: instructor.name)
          .comment-info
            .comment-box-top-arrow.d-block.d-md-none
              = image_tag(asset_url("layout/controllers/online_classes/comment_box_top_arrow.webp"))
            .comment-box-left-arrow.d-none.d-md-block
              = image_tag(asset_url("layout/controllers/online_classes/comment_box_left_arrow.webp"))
            .instructor-name= instructor.name
            .comment= instructor_comment.comment
      .my-task-takeaways
        = link_to "#", class: "takeways-link", data: { bs_toggle: "modal", bs_target: "#homework-takeaways-modal-#{session_homework.id}" } do
          %i.far.fa-sticky-note.fs-16.open-file-icon
          %span= t("user.home.next_liveteach_class.homework_task_takeaways.takeaways_title").html_safe
    .tests-list
      .subtitle= t("user.study_plan.study_module_items.test.test_progress")
      - accuracy_met = completed && completion[:accuracy]
      .test-item
        .title.text-grey-2
          .test-incomplete{ class: accuracy_met ? "test-completed" : "" }
            %i.fas.fa-check
          %span{ class: completed ? "text-decoration-line-through text-grey-4" : "" }= t("user.home.next_liveteach_class.home_work_test", index: 1)
        %span.accuracy
          .knob-container
            - color = accuracy_met ? "#57C292" : "#B65454"
            - value = completed ? completion[:accuracy] : 0

            - if completion[:accuracy] == 100
              %i.fal.fa-check-circle.fs-22.tasks-completed
            - else
              %input.knob{ data: { fgColor: color, bgColor: "#E8EAED", width: "100%", height: "100%", thickness: ".2", displayInput: "false" }, disabled: true, type: "text", value: value }
          %span.your-accuracy-title
            - if completed
              %span= t("user.home.next_liveteach_class.accuracy", accuracy_score: value).html_safe
            - else
              %span= t("user.home.next_liveteach_class.no_accuracy").html_safe
        %span.link-container
          - if completed
            = link_to completion[:evaluation_link], class: "section-color-3", target: "_blank" do
              = t("user.study_plan.study_module_items.review_quiz.results_button")
              %i.far.fa-chevron-right
          - elsif completion[:to_start]
            = link_to completion[:evaluation_link], class: "section-color-3" do
              = t("user.home.next_liveteach_class.take_test")
              %i.far.fa-chevron-right
          - elsif completion[:completed].positive?
            = link_to completion[:evaluation_link], class: "section-color-3" do
              = t("user.study_plan.study_module_items.review_quiz.continue_button")
              %i.far.fa-chevron-right
          - else
            = render "evaluations/shared/new_homework_custom_test_button", session_homework: session_homework

= render "dashboards/homework_task_takeaways_modal", session_homework: session_homework
