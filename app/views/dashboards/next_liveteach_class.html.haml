:ruby
  online_class_sessions = @online_class_sessions
  next_session_by_class = @next_session_by_class
  weekly_hour = @weekly_hours
  start_time = weekly_hour&.start_time
  end_time = weekly_hour&.end_time

%section.next-liveteach-class
  .next-liveteach-class-header
    .container
      .cohort-card.d-flex.align-items-center
        .instructor-info.d-flex
          .back-arrow
            = link_to home_path, class: "back-link" do
              %i.far.fa-chevron-left
          .d-flex.align-items-center
            = image_tag(asset_url(@online_classes_session.online_class.instructor.image_url), alt: "", class: "instructor-image rounded-full")
            .instructor-name
              %h2.fs-18.fw-bold= @online_classes_session.online_class.instructor.name
              = link_to online_class_resources_library_path(back_to: request.fullpath), class: "see-other-cohorts" do
                %i.fal.fa-eye
                %span.underline= t("user.home.next_liveteach_class.see_other_cohorts")
              .instructor-title
                %p.fs-16= @online_classes_session.online_class.instructor.title
        .schedule-info{ class: weekly_office_hours_active?(weekly_hour) ? "weekly-office-hour-active" : "weekly-office-hour-inactive" }
          .date-range
            = image_tag asset_url("layout/controllers/online_classes/calender_icon.svg"), class: "calendar-icon", alt: "Calendar Icon"
            %span.start-date.fs-18= @online_classes_session.online_class.first_class.strftime("%b %-d")
            %span.arrow
              %i.far.fa-arrow-right.fs-14
            %span.end-date.fs-18= @online_classes_session.online_class.last_class.strftime("%b %-d")
          .class-timing.text-gray-600
            %span.fw-semibold= @online_classes_session.date.strftime("%a")
            %span= "- #{@online_classes_session.start_time} - #{@online_classes_session.end_time} (ET)"
        - if weekly_office_hours_active?(weekly_hour)
          .office-hours{ type: "button", data: { "bs-toggle": "modal", "bs-target": "#ondemand-office-hour-modal" } }
            %h3.office-title.text-xs.font-bold.uppercase.text-gray-400= t("user.home.next_liveteach_class.liveteach_office_hours")
            .office-schedule.d-flex.justify-content-between.align-items-center
              .day-time.d-flex.flex-column
                .weekly-office-day.d-flex
                  = image_tag asset_url("layout/controllers/online_classes/calender_icon.svg")
                  %span.fs-16.fw-semibold= weekly_hour&.day
                .weekly-office-time.d-flex
                  = image_tag asset_url("layout/controllers/online_classes/clock_icon.svg"), alt: "Weekly Office Hours"
                  %span=  "#{start_time} - #{end_time} (ET)"
              .instructors
                %span= t("user.home.ondemand_office_hours.instructors")
                .instructor-avatars
                  - @first_five_weekly_hour_instructors.each do |instructor|
                    = image_tag instructor.instructor.image.url, class: "rounded-circle", alt: instructor.instructor.name
  .next-liveteach-class-body
    .container
      .online-class-details
        .accordion#accordion-next-liveteach-class
          %ul.online-class-sessions-list.p-0.m-0
            - online_class_sessions.each do |online_class_session|
              - is_completed = ActiveSupport::TimeZone["Eastern Time (US & Canada)"].parse("#{online_class_session.date} #{online_class_session.end_time}") < Time.current
              - is_next_class = online_class_session.id == next_session_by_class&.id
              .online-class-sessions-list-group
                .status{ class: is_completed ? "completed" : "" }
                  %i.fas.fa-check
                .accordion-item
                  %li.online-class-session.list-unstyled
                    .section-bg-color-5-hover.classes-sessions-detail-sections
                      = render "dashboards/session_schedule_card", online_class_session: online_class_session, is_next_class: is_next_class, is_completed: is_completed
                      .accordion-collapse.collapse.session-accordion{ id: "cohort-session-#{online_class_session.id}", aria: { labelledby: "heading-#{online_class_session.id}" }, class: online_class_session.homework_groups.any? ? "" : "no-session-found", data: { session_id: online_class_session.id } }
                        .accordion-body{ class: homework_groups_completed?(online_class_session, current_user) ? "completed" : "" }
                          .session-homework-head
                            %span= t("user.home.next_liveteach_class.homework_tasks")
                          .accordion#accordian-session-homework
                            .accordion-item
                              - if online_class_session.homework_groups.any?
                                = render "dashboards/homework_group", online_class_session: online_class_session
                              - else
                                .no-session-homework-found.text-grey-3.text-center.fs-16.pb-5= t("user.home.next_liveteach_class.no_homework_assigned")

- if weekly_office_hours_active?(weekly_hour)
  = render "users/weekly_office_hours", weekly_hour: weekly_hour, start_time: start_time, end_time: end_time, on_demand_weekly_hour_instructors: @on_demand_weekly_hour_instructors
