:ruby
  next_group_reached = false
  first_incomplete_group = first_incomplete_group_for_session(online_class_session, current_user)
  next_not_group_reached = true

- online_class_session.homework_groups.each do |homework_group|
  - next_group_reached = first_incomplete_group&.id == homework_group.id
  - group_completed = homework_group_completed?(homework_group, current_user)

  .homework-group{ class: "#{'next-group' if next_group_reached} #{'completed' if group_completed}", id: "homework-group-#{homework_group.id}-content", data: { homework_group_id: homework_group.id } }
    .bar{ class: next_not_group_reached ? "highlighted" : "" }
    .indicator{ class: next_not_group_reached ? "highlighted" : "" }= homework_group.formatted_name[0]
    .group-header
      .group-name= homework_group.formatted_name
    .homework-tasks
      :ruby
        next_not_group_reached = false if next_group_reached
        next_indicator_shown = false
        first_incomplete_homework = homework_group.homework_tasks.find { |task| task.generate_completion_for(current_user)[:percentage] < 100 }
      - next_homework_reached = false
      - homework_group.homework_tasks.each_with_index do |homework, index|
        :ruby
          result = homework_task_completion(homework, current_user)
          next_indicator_shown = next_group_reached && (first_incomplete_homework&.id == homework.id)
          next_homework_reached = true if next_indicator_shown
          classes = []
          classes << "next-task" if next_indicator_shown
          classes << "next-task-shown" if next_homework_reached

        .homework-task-wrapper{ data: { task_index: index, completed: result[:completed], task_id: homework.id } }
          = render sessions_homework_partial_name(homework), session_homework: homework, online_class_session: online_class_session, completion: result[:completion], completed: result[:completed], show_next_indicator: next_indicator_shown, total_tasks: homework_group.homework_tasks.count, classes: classes
