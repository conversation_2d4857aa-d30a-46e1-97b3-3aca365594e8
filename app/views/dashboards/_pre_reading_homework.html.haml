:ruby
  instructor_comment = session_homework.comment
  instructor = instructor_comment&.instructor if instructor_comment.present?

.session-homework.pre-reading{ class: "#{classes.join(' ')} #{'active' if show_next_indicator} #{completed ? 'completed' : 'incomplete'}" }
  .next-item-indicator{ class: show_next_indicator ? "d-block" : "d-none" }= t("user.home.live_classes.indicator.next")
  %a{ class: "header #{show_next_indicator ? '' : 'collapsed'}", role: "button", data: { bs_toggle: "collapse", bs_target: "#session-homework-#{session_homework.id}-content-reading" } }
    .header-info
      .status-icon
        .icon-container
          %i.fal.fa-trophy{ class: (completion[:percentage] >= 100) ? "completed" : "" }
          %i.fa.fa-check-circle.check-circle-icon{ class: (completion[:percentage] >= 100) ? "completed" : "" }

        .knob-container
          %input.knob{ disabled: true, type: "text", value: completion[:percentage], data: { fgColor: "#57C292", bgColor: "#F0F2F5", width: "100%", height: "100%", thickness: ".1", displayInput: "false" } }

      .header-title{ class: completed ? "text-decoration-line-through text-grey-5" : "" }= session_homework.title&.html_safe

    .completion-status.d-flex.align-items-center.gap-2{ class: (completed ? "completed" : "") }
      %span{ class: completion[:percentage].zero? ? "" : "d-none" }= "To Start"
      %span.text-grey-2{ class: completed ? "completed text-grey-5" : "d-none" }= "Completed"
      %span.text-green-2{ class: (completion[:percentage] != 0 and completion[:total_tasks] != completion[:completed]) ? "" : "d-none" }= "In Progress"
    .arrows
      %i.far.fa-chevron-down
      %i.far.fa-chevron-up

  %div{ id: "session-homework-#{session_homework.id}-content-reading", class: "collapse main-content #{show_next_indicator ? 'show' : ''}" }
    .content
      %p.fw-normal= session_homework.description&.html_safe
      - if instructor_comment.present?
        .homework-task-comments.d-flex
          .instructor-image= image_tag(instructor.image.url, class: "rounded-circle", alt: instructor.name)
          .comment-info
            .comment-box-top-arrow.d-block.d-md-none
              = image_tag(asset_url("layout/controllers/online_classes/comment_box_top_arrow.webp"))
            .comment-box-left-arrow.d-none.d-md-block
              = image_tag(asset_url("layout/controllers/online_classes/comment_box_left_arrow.webp"))
            .instructor-name= instructor.name
            .comment= instructor_comment.comment
      .my-task-takeaways
        = link_to "#", class: "takeways-link", data: { bs_toggle: "modal", bs_target: "#homework-takeaways-modal-#{session_homework.id}" } do
          %i.far.fa-sticky-note.fs-16.open-file-icon
          %span= t("user.home.next_liveteach_class.homework_task_takeaways.takeaways_title").html_safe
    .tests-list
      .subtitle.fw-semibold= t("user.home.next_liveteach_class.reading_progress")
      - session_homework.data.each do |topic_id|
        :ruby
          topic = fetch_topic_from_homework_data(topic_id)
          completed = task_completed?(topic, current_user)

        - next if topic.nil?

        .test-item
          .title.text-grey-2.reading-title
            .test-incomplete{ class: completed ? "test-completed" : "" }
              %i.fas.fa-check
            %span{ class: completed ? "text-decoration-line-through text-grey-4" : "" }= topic.name
          %span.link-container.reading-link-container
            = link_to lesson_path(topic.id), class: "text-decoration-none", target: "_blank" do
              %span= t("user.home.next_liveteach_class.read_lesson")
              %i.far.fa-chevron-right

= render "dashboards/homework_task_takeaways_modal", session_homework: session_homework
