:ruby
  takeaway = current_user.homework_task_takeaways.find_by(homework_task_id: session_homework.id) || HomeworkTaskTakeaway.new

.modal.fade.homework-takeaways-modal{ id: "homework-takeaways-modal-#{session_homework.id}", tabindex: "-1", "aria-hidden": "true" }
  .modal-dialog.modal-lg.modal-dialog-centered
    .modal-content
      .modal-header
        %h5.modal-title.text-primary= t("user.home.next_liveteach_class.homework_task_takeaways.edit_takeaways")
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" }, "aria-label": "Close" }
      .modal-body.pb-0
        = form_for takeaway, url: create_homework_task_takeaways_path, method: :post, html: { id: "takeaways-form-#{session_homework.id}", class: "homework-takeaways-form" }, remote: true do |f|
          = f.hidden_field :homework_task_id, value: session_homework.id
          .mb-3
            = f.cktext_area :content,
              class: "form-control ckeditored takeaways-editor",
              id: "takeaways-editor-#{session_homework.id}",
              rows: 10,
              data: { value: takeaway.content }
      .modal-footer
        %button.btn.cancel-btn{ type: "button", data: { bs_dismiss: "modal" } }= t("user.home.next_liveteach_class.homework_task_takeaways.cancel")
        %button.btn.btn-primary.submit-btn.show-overlay{ type: "submit", form: "takeaways-form-#{session_homework.id}" }= t("user.home.next_liveteach_class.homework_task_takeaways.save")
