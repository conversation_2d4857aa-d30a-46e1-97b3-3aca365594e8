.modal.online-class-session-files-modal{ id: "online-class-session-#{online_class_session.id}-files" }
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header
        .header-info
          %h2.title.fs-26.m-0= t("user.home.next_liveteach_class.session_files_modal.session_files", number_of_session: online_class_session.number_of_session)
          %p.last-updated.fs-16.m-0= t("user.home.next_liveteach_class.session_files_modal.last_updated", date: online_class_session.updated_at.strftime("%b %-d, %Y"))
        %button.btn-close.pe-3.pt-3.text-light.fw-bold{ type: "button", data: { bs_dismiss: "modal" } }
      .modal-body
        .uploaded-files
          %h3.section-title.fs-14.fw-semibold.text-uppercase= t("user.home.next_liveteach_class.session_files_modal.uploaded_files", number_of_files: files.count)
          .file-list-item
            - files.each do |file|
              .file-item
                .row
                  .col-sm-1.col-3.file-icon-col
                    .file-icon
                      %i.far.fa-regular.fa-file
                  .col-sm-10.col-6.file-details-col
                    .file-details
                      %p.file-name.fs-16.fw-semibold.m-0= file.name
                      %p.upload-date.fs-14.m-0= t("user.home.next_liveteach_class.session_files_modal.uploaded_on", date: file.created_at.strftime("%b %-d, %Y"))
                  .col-sm-1.col-3.d-flex.justify-content-end.file-actions-col
                    .file-actions{ data: { bs_toggle: "tooltip", bs_placement: "top", bs_trigger: "hover" }, title: "Download file" }
                      = link_to file.file_url, class: "fs-18 fw-semibold schedule-download-btn", download: "session_#{online_class_session.id}_#{file.name}#{File.extname(file.file.identifier)}", target: "_blank" do
                        = image_tag(asset_url("layout/controllers/online_classes/download_file_icon.svg"), alt: "Download", class: "download-icon")

