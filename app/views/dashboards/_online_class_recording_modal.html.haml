:ruby
  intructor_name = session.online_class.instructor.name
  flagged = "flagged" if Flag.flagged?(current_user, session, :review)

.modal.online-class-session-recording{ id: "online-class-session-#{session.id}-recording", role: "dialog", aria: { modal: "true", labelledby: "modal-title-#{session.id}" }, data: { bs_backdrop: "static", video_src: session.recording_url, model_id: session.id, flag_url: Rails.application.routes.url_helpers.flag_path(id: session.id, type: "online_class_session", label: :cohort_video_completed) } }
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header
        %h4.modal-title.text-blue-2 #{intructor_name} - Session #{session.number_of_session}
        %span.visually-hidden Title
        - unless from_bookmarks
          .flags.ms-auto
            = link_to toggle_flag_path(id: session.id, type: :online_class_session, label: :review), class: "bookmark-icon-link #{flagged} recording-modal-bookmark-#{session.id} recording-bookmark-session-btn", data: { session_id: session.id } do
              .text-blue-3.fs-18.bookmark-icon-group
                %i.fas.fa-bookmark{ data: { bs_toggle: "tooltip", bs_placement: "top", bs_trigger: "hover" }, title: "Remove Bookmark" }
                %i.far.fa-bookmark{ data: { bs_toggle: "tooltip", bs_placement: "top", bs_trigger: "hover" }, title: "Bookmark video" }
              %span.visually-hidden Toggle bookmark
        %button.btn-close.pe-3.pt-3.text-grey-4.fw-bold.ms-0{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body
        .video-wrapper

