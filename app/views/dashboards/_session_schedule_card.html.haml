:ruby
  has_files = online_class_session.online_class_session_documents.any?
  action_btn_attr = liveteach_action_btn_attributes(online_class_session, is_completed)
  see_files_btn_attributes = see_files_btn_attributes(online_class_session)

.schedule-card.current.collapsed{ class: is_next_class ? "active" : "", type: "button", data: { target: "#cohort-session-#{online_class_session.id}", aria_expanded: "true", aria_controls: "cohort-session-#{online_class_session.id}" } }
  .row
    .col-lg-2.col-md-12.schedule-date-card
      .schedule-card-title.fs-18.fw-semibold= online_class_session.date.strftime("%B %-d")
      .schedule-card-title-mobile.fs-18.fw-semibold= online_class_session.date.strftime("%b %-d")
      %p.schedule-time
        #{online_class_session.start_time} -
        %br
        #{online_class_session.end_time} (ET)
    .col-lg-8.col-md-12.d-md-block.d-flex.flex-column.schedule-card-session
      .d-flex.justify-content-between.schedule-card-session-title-block
        .session-details
          .schedule-card-title.fs-18.fw-semibold Session #{online_class_session.number_of_session}
          %p.session-topic.fs-16.fw-normal.description= online_class_session.description.html_safe
        .actions-links-mobile.dropdown.overflow-visible.d-xl-none.d-lg-none.d-md-none.d-flex.justify-content-center.align-items-center
          = link_to "#", class: "dropdown-toggle without-caret", data: { bs_toggle: "dropdown", stop_propagation: true } do
            %i.fal.fa-ellipsis-v.section-color-3.p-0.m-0.fs-30
            %span.visually-hidden Toggle dropdown test actions
          .dropdown-menu.custom-dropdown{ data: { stop_propagation: true } }
            - if has_files
              %button.session-resources-icon-group.dropdown-item.session-files{ **see_files_btn_attributes }
                .icon-container
                  = image_tag("layout/controllers/online_classes/folder_icon.svg", class: "see-session-files")
                .icon-text.fs-16= t("user.home.next_liveteach_class.view_session_files")

            - if action_btn_attr[:url].present?
              = link_to action_btn_attr[:url], { class: "session-resources-icon-group dropdown-item #{action_btn_attr[:class_name]}", target: "_blank", data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: action_btn_attr[:title], stop_propagation: true } } do
                .icon-container
                  = image_tag(action_btn_attr[:icon])
                .icon-text.fs-16= t("user.home.next_liveteach_class.open_zoom_link")
            - else
              %button.session-resources-icon-group.dropdown-item{ class: [action_btn_attr[:class_name], action_btn_attr[:disabled] ? "disabled" : ""],
                type: "button", data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: action_btn_attr[:title],
                bs_target: action_btn_attr[:target], stop_propagation: true } }
                .icon-container
                  = image_tag(action_btn_attr[:icon])
                .icon-text.fs-16{ class: (action_btn_attr[:class_name] == "online-class-recording") ? "" : action_btn_attr[:class_name] }= action_btn_attr[:icon_text]

    .col-auto.px-0.d-flex.align-items-center.schedule-data-btn-group{ class: has_files ? "" : "file-icon-hidden" }
      - if has_files
        .session-files{ **see_files_btn_attributes }
          = image_tag("layout/controllers/online_classes/folder_icon.svg", class: "see-session-files")
      - if action_btn_attr[:url].present?
        = link_to action_btn_attr[:url], { class: "session-resources-icon-group #{action_btn_attr[:class_name]}", target: "_blank", data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: action_btn_attr[:title], stop_propagation: true } } do
          = image_tag(action_btn_attr[:icon])
      - else
        %button.session-resources-icon-group{ class: [action_btn_attr[:class_name], action_btn_attr[:disabled] ? "disabled" : ""],
           type: "button", data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: action_btn_attr[:title],
           bs_target: action_btn_attr[:target], stop_propagation: true } }
          = image_tag(action_btn_attr[:icon])

      .border-btn-group
      %button.accordion-button.accordion-arrow-btn.btn.p-0.ms-3.collapsed{
        type: "button",
        data: { bs_toggle: "collapse", bs_target: "#cohort-session-#{online_class_session.id}" },
        aria: { expanded: "false", controls: "cohort-session-#{online_class_session.id}" }
      }

- if online_class_session.online_class_session_documents.any?
  = render "dashboards/online_class_session_files_modal", files: online_class_session.online_class_session_documents, online_class_session: online_class_session
- if online_class_session.recording_url.present?
  = render "dashboards/online_class_recording_modal", session: online_class_session, recording_url: online_class_session.recording_url, from_bookmarks: false
