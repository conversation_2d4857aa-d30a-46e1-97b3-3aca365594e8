<% @word_definitions.each do |word_definition| %>
  <% exercise_selector = @exercise.grouped? ? ".exercise[data-group-parent-id=#{@exercise.group_parent_id}]" :  ".exercise[data-id=#{@exercise.id}]" %>

  $('<%= exercise_selector %> .solution-wrapper .info-box-content').find('.notetaking-preselection, .notetaking-loaded, p').each(function() {
    var regexp = new RegExp(/\b<%= word_definition.word %>\b/i, "gi")
    var matched_word = $(this).html().match(regexp);

    $(this).html($(this).html().replace(regexp, function (matched_word) {
      return '<%= j render("word_definitions/word_definition", word_definition: word_definition, exercise: @exercise) %>'.replace(regexp, matched_word);
    }));
  });
<% end %>
