%script{ src: "https://player.vimeo.com/api/player.js" }

.liveteach-cohort-library
  .header
    .header-container.container
      .title-box.d-flex.align-items-center.justify-content-start.gap-3
        = link_to @back_to_params.presence || home_path, class: "back-link" do
          %i.far.fa-chevron-left
        %h1.text-blue-3.fs-28.mb-0.title= t("user.online_class_resources.title")

  .bg-light-1.py-4.flex-grow-1.body#cohort-library
    .body-container.container
      .row
        .col-12
          .input-group
            = image_tag(asset_path("layout/controllers/users/ondemand/search_icon.svg"), class: "search-icon")
            %input.search-input.form-control.form-control-lg#search-video-input{ placeholder: t("user.online_class_resources.search_placeholder"), class: "form-control form-control-lg search-topic-input" }
      %hr.separator
      .search-result.fs-16.fw-semibold.text-grey-4#result-count= t("user.online_class_resources.ttp_instructors")
      .instructor-and-topic-filter-group
        .row.no-gutters
          %ul.list-instructors.col#instructor-list
            = render partial: "online_class_resources/instructors_list", locals: { instructors: @instructors }
        .row.cohort-library-video-card-container.invisible-container#cohort-library-video-list
          = render partial: "online_class_resources/cohort_videos", locals: { videos_by_instructor: @videos_by_instructor }
