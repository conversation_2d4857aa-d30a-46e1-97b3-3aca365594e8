:ruby
  flagged ||= ""
.card#cohort-video-card{ data: { bs_target: "#online-class-session-#{online_class_session.id}-recording" } }
  .row.m-0
    .col-lg-2.col-md-12.session-video.p-0
      .video-link
        %iframe.video-thumbnail{ src: "https://player.vimeo.com/video/#{online_class_session.recording_url}?autoplay=0&background=1", frameborder: "0", allowfullscreen: true, class: "" }
        .play-button
          = image_tag(asset_path("layout/controllers/liveteach_cohort_library/play_icon.svg"), class: "play-icon")
    .col-lg-8.col-md-12.d-md-block.d-flex.flex-column.session-info-block
      .d-flex.justify-content-between.align-items-center
        .session-details
          .session-title.fs-18.fw-semibold Session #{online_class_session.number_of_session}
          .session-topic.fs-16.fw-normal.description= online_class_session.description.html_safe
    .col-auto.px-0.d-flex.align-items-center.action-btn-group
      .clapperboard-play-icon{ data: { bs_toggle: "tooltip", bs_placement: "top", bs_trigger: "hover" }, title: t("user.home.next_liveteach_class.view_class_recording") }
        .action-btn-icon-group
          = image_tag(asset_url("layout/controllers/online_classes/clapperboard_play_icon.svg"), class: "clapperboard-play-icon", alt: "Play Video")
      - if from_bookmarks
        = link_to delete_flag_path(type: "OnlineClassSession", id: online_class_session.id, label: "review"), class: "stretched-link position-relative section-color-3 delete-bookmark show-overlay", method: :delete, remote: true, title: "Delete", data: { confirm: t("user.actions.confirm_element_deletion", element: "bookmark"), stop_propagation: true, bs_toggle: "tooltip", bs_placement: "top", bs_trigger: "hover" } do
          %i.fal.fa-trash.fs-18.m-auto
          %span.visually-hidden Delete must know bookmark
      - else
        .flags
          = link_to toggle_flag_path(id: online_class_session.id, type: :online_class_session, label: :review), class: "action-btn-icon-group bookmark-icon-link text-blue-3 fs-17 #{flagged} bookmark-session-btn online-class-session-bookmark-#{online_class_session.id}", data: { session_id: online_class_session.id } do
            %i.fas.fa-bookmark{ data: { bs_toggle: "tooltip", bs_placement: "top", bs_trigger: "hover" }, title: "Remove Bookmark" }
            %i.far.fa-bookmark{ data: { bs_toggle: "tooltip", bs_placement: "top", bs_trigger: "hover" }, title: "Bookmark video" }
            %span.visually-hidden Toggle bookmark
