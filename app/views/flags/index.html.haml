%script{ src: "https://player.vimeo.com/api/player.js" }

%section.page-header-light.header-type-3
  .container
    .row
      .col-12
        .header-inner
          .title-box
            %h1.section-color-2= t("user.flags.index.title")
          .select-section-tab-group
            - current_user.active_sections.each do |section|
              = link_to section_label_name(section), flags_path(section: section, flaggable_type: @flag_type), class: "select-section-tab show-overlay px-3 py-2 section-content section-color-1-active section-color-2-hover #{section} #{'active' if current_section == section}"
  .header-sub-navigation
    .container
      .row
        .col-12
          .header-sub-navigation-inner
            .left-side
              %span.change-view-label= t("user.top_nav.logged.header.change_view")
              = link_to t("user.flags.chapter_list.tab_name"), chapter_list_flags_path(section: current_section), class: "tab-btn section-border-color-3 show-overlay"
              = link_to t("user.flags.index.tab_name"), nil, class: "tab-btn active section-border-color-3 show-overlay"
              = link_to t("user.flags.notes.tab_name"), notes_flags_path(section: current_section), class: "tab-btn section-border-color-3 show-overlay"
            - if subsections_per_section(current_section).any? and !@chapter.present?
              .col-auto.d-flex.align-self-center.ms-auto
                %span.my-auto.d-none.d-md-inline.me-md-2= "Showing:"
                - active_subsection = params[:subsection] || subsections_per_section(current_section).first
                = render "components/subsection_dropdown_filter", subsections: subsections_per_section(current_section), active_subsection: active_subsection, object: controller.controller_path, accronim_on_mobile: true, button_with_section_color: true

%section.bg-light-1.pt-4.pb-5.flex-grow-1
  - partial_name = @flaggable_type.to_s.pluralize
  .container
    .row.m-0.bg-white.border.border-color-grey-6.bookmark-nav-container.flex-nowrap.pe-0.align-items-center.mw-100
      .col-auto.px-0.d-none.d-xxl-block.me-auto
        %ul.list-unstyled.list-group.list-group-horizontal.mb-0
          - @facade.flaggable_types.each do |f_type|
            - name = f_type.to_s.pluralize
            %li
              = link_to flags_path(section: current_section, flaggable_type: f_type, subsection: current_subsection), class: "fs-13 list-group-item list-group-item-action #{'active' if @flaggable_type == f_type.to_s}", role: "tab" do
                %span.text-uppercase= t("user.flags.flaggable.#{current_section}.#{name}")
            .separator.border-1.border-end.border-color-grey-6.my-auto

      .col-auto.px-0.d-flex.d-xxl-none.filter-flaggable-type-responsive
        %span.text-dark-1.my-auto.me-md-2.ps-3 Showing:
        = render "flaggable_type_dropdown", name: partial_name, flaggable_types: @facade.flaggable_types, current_flaggable_type: @flaggable_type
      .col.col-xxl-auto.mw-0.d-none.d-xxl-block
        .row.flex-nowrap.g-0
          - if online_class_session_flag(@flaggable_type)
            %span.col-auto.my-auto.d-none.d-md-inline.mb-8.ps-0.text-nowrap= t("user.flags.flaggable.filter_by")
            .section-content.d-flex.mw-0#bookmark-instructor-dropdown
              = render "flag_collection_dropdown", collection: @facade.instructors, element_name: "instructor"
          - else
            %span.col-auto.my-auto.d-none.d-md-inline.mb-8.ps-0.text-nowrap= t("user.flags.flaggable.filter_by")
            .section-content.d-flex.mw-0#bookmark-chapter-dropdown
              = render "flag_collection_dropdown", collection: @facade.chapters.where(subsection: current_subsection).group_by(&:chapter).keys, element_name: "chapter"
      %button.col-auto.section-color-2.d-xxl-none.flex-basis-0.ms-auto.me-3.my-auto.filter-chapter-responsive.d-flex.align-items-center.justify-content-center{ type: "button", data: { "bs-toggle": "modal", "bs-target": "#filter-chapter" } }
        %i.fas.fa-sliders-v
        %span.visually-hidden Open Filters

  .container.flags-container.tab-content
    .row
      .col-12{ class: "content-bookmarks-#{online_class_session_flag(@flaggable_type) ? 'instructor' : 'chapter'}" }
        - if online_class_session_flag(@flaggable_type)
          - @facade.instructors.each do |instructor|
            - flags = @facade.flags_by_instructor(instructor, limit: 5)
            - total_flags_count = @facade.flags_count_by_instructor(instructor)
            - if total_flags_count.positive?
              .instructor.instructor-content.filterable{ data: { filter: "instructor-#{instructor.id}" }, id: "instructor-#{instructor.id}" }
                .row.position-relative.chapter-title.w-100
                  .col.show-overlay.d-flex.align-items-center.px-0
                    %h5.d-inline.my-0.section-color-2.fs-22= instructor.name
                  = link_to liveteach_bookmark_details_flags_path(section: current_section, instructor_id: instructor, flaggable_type: @flaggable_type, from: "bookmarks"), class: "col-auto section-color-3 fs-13 view-all-link d-flex align-items-end my-auto" do
                    %p.mb-0.text-uppercase.fw-semibold
                      %span.d-none.d-md-inline.text-underline-hover.pe-0.text-text-uppercase= "view all (<span id=\"instructor-#{instructor.id}-#{@flaggable_type}-count\">#{total_flags_count}</span>)".html_safe
                    %i.far.fa-chevron-right.d-flex.align-items-center.justify-content-center
                  = render "flaggables", instructor: instructor, flags: flags, count: total_flags_count, flaggable_type: @flaggable_type, facade: @facade, with_id: true

        - else
          - @facade.chapters.where(subsection: current_subsection).each do |chapter|
            - flags = @facade.flags_by_chapter_and_type(chapter, @flaggable_type, limit: 5)
            - total_flags_count = @facade.flags_count_by_chapter_and_type(chapter, @flaggable_type)

            - if total_flags_count.positive?
              .chapter.chapter-content.filterable{ data: { filter: "chapter-#{chapter.id}" }, id: "chapter-#{chapter.id}" }
                .row.position-relative.chapter-title.w-100
                  .col.show-overlay.d-flex.align-items-center.px-0
                    .numeration.flex-shrink-0.fw-semibold.text-white.d-flex.justify-content-center.align-items-center.section-bg-color-2.rounded-circle
                      = chapter.dynamic_number(current_user.track)
                    %h5.d-inline.my-0.section-color-2.fs-22= sanitize formatted_chapter_name(chapter.name)
                  = link_to details_flags_path(section: current_section, chapter_id: chapter, flaggable_type: @flaggable_type, from: "bookmarks"), class: "col-auto section-color-3 fs-13 view-all-link d-flex align-items-end my-auto" do
                    %p.mb-0.text-uppercase.fw-semibold
                      %span.d-none.d-md-inline.text-underline-hover.pe-0.text-text-uppercase= "view all (<span id=\"chapter-#{chapter.id}-#{@flaggable_type}-count\">#{total_flags_count}</span>)".html_safe
                    %i.far.fa-chevron-right.d-flex.align-items-center.justify-content-center
                .row.gx-0
                  .card
                    .card-header.text-uppercase.bg-color-light-3.text-grey-4.fs-13.fw-semibold
                      #{t("user.flags.flaggable.#{current_section}.#{partial_name}")} bookmarked
                    .card-body.p-0
                      = render "flaggables", chapter: chapter, flags: flags, count: total_flags_count, flaggable_type: @flaggable_type, facade: @facade, with_id: true

  .container.no-flags-container.hidden
    = render "flags/flaggables/no_flaggables", name: @flaggable_type.to_s
%section.modal.fade#filter-chapter
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.position-relative
        %h4.modal-title Filters
        %button.align-self-center.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body
        - if online_class_session_flag(@flaggable_type)
          = label_tag "select", "by instructor:", class: "text-uppercase mb-10"
          .section-content#bookmark-instructor-select-responsive
            = select_tag "select", options_for_select(flags_instructor_options_for_select(@facade.instructors), nil), class: "form-select mt-2 attribute-filters", data: { target_selector: ".instructor", filter_success: "verify_if_bookmarks" }
        - else
          = label_tag "select", "by chapter:", class: "text-uppercase mb-10"
          .section-content#bookmark-chapter-select-responsive
            = select_tag "select", options_for_select(flags_chapter_options_for_select(@facade.chapters), nil),
              class: "form-select mt-2 attribute-filters", data: { target_selector: ".chapter", filter_success: "verify_if_bookmarks" }
        %button.btn.btn-outline-primary.text-uppercase.w-100.mt-68{ type: "button", data: { bs_dismiss: "modal" } } back to list
