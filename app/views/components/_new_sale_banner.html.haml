- current_sale = SaleBanner.active_sale
- if current_sale.present?
  %section.navbar.user.pt-1
    .top-banner-padding
    = content_tag(disable_redirect_banner ? :div : :a, href: disable_redirect_banner ? nil : "/plans#section-trial-sign-up", class: "top-banner trial-banner", style: "background: #{current_sale&.color};") do
      .container-fluid.banner-message
        .top-banner-inner.center-box-banner
          .left-side
            = link_to current_sale&.name.present? ? current_sale&.name : "#{season_of_sale&.upcase} SALE", plans_path(anchor: "section-plan-options"), class: "sale-name js-scroll-to-element #{show_mobile_plan_design_for_gmat?(@plans_mobile_view || false) ? 'd-none d-md-block' : ''}", data: { element_id: "plan-options" }
            %span.text-current
              - if current_sale&.sale_type == "default"
                .default-sale-banner= "#{current_sale&.discount} <span>Ends #{Date.current.at_end_of_week.strftime('%B %d, %Y')}</span>".html_safe
              - else
                %span= current_sale&.discount&.html_safe
