:ruby
  trial_plan = is_trial_free? ? "FREE" : "$1"

%section.my-5.ondemand-plans-mobile-view.mb-0#trial-sign-up
  .container.container-xl.gx-custom-1
    .row.justify-content-center.intro-section
      .col-12
        %h1.fw-semibold.text-dark-1.text-center.mb-3.title= t("user.static_pages.plans.plan_options.title_2", exam: exam_name_display_title(include_exam_type: true)).html_safe
        .line-decorator-title.bg-dark-1.mx-auto
      .col-12.d-flex.justify-content-center.align-items-center.pt-4.flex-column.free-trial-btn-group.position-relative
        %p.mb-4.text-center.fs-18.mb-0.text-grey-2= t("user.static_pages.plans.plan_options.description_2", exam: exam_name_display_title(include_exam_type: true)).html_safe
        - if Setting.for("free_trial_enabled") and !cardless_trial_test?
          = link_to t("user.plans.plan_d.ondemand.btn_text", plan_mode: trial_plan), "#",
            class: "btn-primary trial-btn fs-14 fw-semibold text-uppercase border-0 app-bg-1",
            data: { bs_toggle: "modal", bs_target: "#cardless-free-trial", plan_title: t("user.static_pages.plans.plan_options.plan_d.title", exam_name: EXAM_NAME.upcase),
            plan_subtitle: t("user.plans.plan_d.subtitle"), plan_code: Setting.for("recurly_5days_plan_code") }
        - else
          = link_to t("user.static_pages.plans.ondemand_plans_mobile_view.trial_btn_text", plan_mode: trial_plan), "#",
            class: "btn-primary trial-btn fs-14 fw-semibold text-uppercase border-0 app-bg-1",
            data: { bs_toggle: "modal", bs_target: "#purchase-plan", plan_title: t("user.static_pages.plans.plan_options.plan_d.title", exam_name: EXAM_NAME.upcase),
            plan_subtitle: t("user.plans.plan_d.subtitle"), plan_price: t("user.plans.plan_d.sale_price"),
            plan_before: "1.00", plan_code: Setting.for("recurly_5days_plan_code") }

    .recognition-slider.mb-5
      .header-title.fs-16.fw-semibold.text-grey-3= t("user.static_pages.plans.ondemand_plans_mobile_view.rated_excellent", exam: exam_name_display_title(include_exam_type: true)).html_safe
      .logo-slider-wrapper
        .logo-slider
          - 2.times do
            - %w(trustpilot_logo gmat_club_logo mba_insight_logo poets_and_quants_logo).each do |logo|
              .logo
                = image_tag(asset_url("layout/controllers/content_pages/plans/ondemand_plans_mobile/#{logo}.webp"), alt: "", class: "slider-image #{logo.gsub('_', '-')}")
    %hr.section-separator.text-grey-6

    .plan-options
      = render "content_pages/plans/ondemand/mobile/plan_options"

    .plan-details
      = render "content_pages/plans/ondemand/mobile/plan_details"

  .score-guaranteed-section
    = render "content_pages/plans/ondemand/mobile/score_guarante"

  .container.container-xl.gx-custom-1
    .comprehensive-prep-section
      = render "content_pages/plans/ondemand/mobile/comprehensive_prep"

    .try-ttp-self-study-free-btn.d-flex.justify-content-center
      - if Setting.for("free_trial_enabled") and !cardless_trial_test?
        = link_to t("user.plans.plan_d.ondemand.btn_text", plan_mode: trial_plan), "#",
          class: "trial-btn btn m-auto w-100 fs-16 fw-semibold app-bg-1",
          data: { bs_toggle: "modal", bs_target: "#cardless-free-trial", plan_title: t("user.static_pages.plans.plan_options.plan_d.title", exam_name: EXAM_NAME.upcase),
          plan_subtitle: t("user.plans.plan_d.subtitle"), plan_code: Setting.for("recurly_5days_plan_code") }
      - else
        = link_to t("user.static_pages.plans.ondemand_plans_mobile_view.try_ttp_self_study_for_free"), "#",
          class: "trial-btn btn m-auto w-100 fs-16 fw-semibold app-bg-1",
          data: { bs_toggle: "modal", bs_target: "#purchase-plan", plan_title: t("user.static_pages.plans.plan_options.plan_d.title", exam_name: EXAM_NAME.upcase),
          plan_subtitle: t("user.plans.plan_d.subtitle"), plan_price: t("user.plans.plan_d.sale_price"),
          plan_before: "1.00", plan_code: Setting.for("recurly_5days_plan_code") }

    .video-based-ondemand
      .title.fs-26.fw-semibold.text-dark-1.text-center= t("user.static_pages.plans.ondemand_plans_mobile_view.sneak_peek_ondemand_experience")
      .line-decorator-title.bg-dark-1.mx-auto
      .video-slider-img
        = link_to ondemand_preview_video_path(video_section: "di"), class: "video-show-link show-overlay", id: "video-di", remote: true do
          .text-center.position-relative.slide
            - image_path = ea_exam? ? "layout/controllers/dashboards/ondemand/ea_sneak_peek_3.webp" : "layout/controllers/dashboards/ondemand/sneak_peek_3.webp"
            %img.video-based-experience-img.mx-auto{ src: asset_url(image_path) }
    .try-ttp-ondemand-free-btn.d-flex.justify-content-center
      = link_to t("user.static_pages.plans.ondemand_plans_mobile_view.try_ondemand_for_free"), cart_path(cart: { plan_code: Setting.for("recurly_5days_ondemand_plan_code") }), class: "trial-btn btn m-auto w-100 fs-16 fw-semibold ondemand-trial-plan-btn"

  %section.testimonials-mobile.position-relative
    .container
      .title-head
        %h2.fw-semibold.text-dark-1.text-center= t("user.static_pages.plans.ondemand_plans_mobile_view.testimonials.title")
        .line-decorator-title.bg-dark-1.mx-auto
      - if gmat_exam?
        .row.position-relative.mb-5.mb-md-0.testimonials-slider
          .col-12.col-md-10.col-xl-8.mx-auto.testimonials-slider-wrapper
            #slider-plan-testimonials
              = render "content_pages/plans/testimonials_slider_mobile"
      - else
        .row.row-cols-1.row-cols-lg-3.justify-content-center.justify-content-lg-start.testimonials-container
          = render "content_pages/plans/testimonials_card"

  .container.container-xl.gx-custom-1
    .justify-content-center.free-consultation-section.w-100
      .row.m-0
        .col-md-12.row.m-0.p-0
          .head.text-center
            .row.cart
              .free-consult-card.d-flex.flex-column.justify-content-between.align-items-center
                %p.fs-25.fw-semibold.text-white.m-lg-0.mb-3.mb-md-0.title
                  = t("user.static_pages.plans.ondemand_plans_mobile_view.free_consultation.title").html_safe
                %a.consultation-btn.text-light.fw-semibold.app-bg-2{ href: t("user.plans.free_consultation.call_number") }= t("user.plans.free_consultation.get_free_consultation_btn").html_safe

  - if !gre_exam? and !ea_exam?
    %section.recognitions-mobile
      = render "content_pages/plans/recognitions_mobile"

  .container.container-xl.gx-custom-1
    %section.free-trial-plans#trial-plan-sign-up
      = render "content_pages/plans/ondemand/mobile/free_trial_plans"

= render "videos/show_modal"
