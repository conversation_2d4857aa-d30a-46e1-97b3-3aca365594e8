:ruby


.col-12.plan-card{ class: "plan-#{option}", id: "plan-#{option}-section" }
  .head.text-center{ class: EXAM_NAME }
    .title.fs-26.fw-semibold.text-dark-1= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.plan_#{option}_header.title")
    .description.set-width.fs-16.text-grey-1= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.description", exam: exam_name_display_title(include_exam_type: true)).html_safe

  .plan-container
    .plan-container-group
      .upper-tag{ class: "plan-#{option}" }
        .col.flex-grow-1.px-0.d-flex.justify-content-center.align-items-center
          %p.mb-0.text-uppercase.text-center.fs-14.fw-semibold.d-flex.flex-lg-row.flex-column
            - if option == "g"
              .span.circle
            %span= t("user.static_pages.plans.plan_options.plan_#{option}_upper_tag").html_safe

      .card.border-0.m-auto{ class: "plan-#{option}" }
        .card-body.p-0{ data: { plan: option } }
          .row.plan-period.mx-0
            %p.plan-title.text-grey-1.fw-semibold.p-0.text-center= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.plan_#{option}.title", exam_name: EXAM_NAME.upcase).html_safe
            .plan-description.text-grey-1.mb-0.text-center{ class: "plan-#{option}" }= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.plan_#{option}.description").html_safe
            .price-container.responsive.px-0{ class: "plan-#{option}" }
              .text-grey-5.price.me-1.fw-normal.text-decoration-line-through= number_with_delimiter(t("user.plans.plan_#{option}.price"))
              .text-grey-1.price.plan-price.fw-semibold.mb-0{ class: "plan-#{option}" }= t("user.plans.plan_#{option}.sale_price")
            .justify-content-center.p-0
              .get-started-btn
                - if plan_code == RecurlyProxy::ONLINE_CLASS_PLAN_CODE and option == "g"
                  = link_to t("user.static_pages.plans.plan_details.reserve_your_seat"), online_classes_path, class: "btn show-overlay plan-#{option}"
                - else
                  = link_to t("user.plans.purchase_now").html_safe, "#",
                    class: "btn plan-#{option}",
                    data: { bs_toggle: "modal", bs_target: "#purchase-plan", plan_title: t("user.static_pages.plans.plan_options.plan_#{option}.title", exam_name: EXAM_NAME.upcase),
                    plan_subtitle: t("user.plans.plan_#{option}.subtitle"), plan_intro: t("user.plans.plan_#{option}.intro"),
                    plan_price: t("user.plans.plan_#{option}.sale_price"), plan_before: t("user.plans.plan_#{option}.price"), plan_code: plan_code, ondemand_plan: plan_code == RecurlyProxy::DEFAULT_ONDEMAND_PLAN_CODE }

            - if option == "f" and plan_code == RecurlyProxy::DEFAULT_ONDEMAND_PLAN_CODE
              .try-for-free-btn
                = link_to t("user.plans.try_for_free"), "#trial-plan-sign-up"

      .card-footer{ class: "plan-#{option}" }
        .accordion{ id: "plan-#{option}-benefits-accordion" }
          .accordion-item
            %h2.accordion-header{ id: "plan_#{option}_features_heading" }
              %button.accordion-button.collapsed{ class: "plan-#{option}", type: "button", "data-bs-toggle" => "collapse", "data-bs-target" => "#plan-#{option}-collapse", "aria-expanded" => "false", "aria-controls" => "plan-#{option}-collapse" }
                See all features
            .accordion-collapse.collapse{ id: "plan-#{option}-collapse", "aria-labelledby" => "plan_#{option}_features_heading", "data-bs-parent" => "#plan-#{option}-benefits-accordion" }
              .accordion-body
                %ul.list-unstyled
                  - option_list = (option == "f") ? (5..15) : (1..15)
                  - option_list.each do |item|
                    %li{ class: "plan-#{option}" }
                      %i.fas.fa-check
                      %span= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.list_items.item_#{item}").html_safe
