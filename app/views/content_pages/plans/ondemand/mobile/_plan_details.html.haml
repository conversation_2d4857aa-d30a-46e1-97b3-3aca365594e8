.row.row-cols-1.plans
  .col-12.plan-card.plan-self-study#plan-self-study-section
    .head.text-center{ class: EXAM_NAME }
      .title.fs-26.fw-semibold.text-dark-1= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.self_study.title")
      .description.set-width.fs-16.text-grey-1= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.description", exam: exam_name_display_title(include_exam_type: true)).html_safe

    .plan-container
      .plan-container-group
        .upper-tag{ class: "plan-self-study plan-a" }
          .col.flex-grow-1.px-0.d-flex.justify-content-center
            %p.mb-0.text-center.fs-14.fw-semibold.d-flex.flex-lg-row.flex-column
              = t("user.static_pages.plans.plan_options.self_study_upper_tag", exam: EXAM_NAME.upcase).html_safe
        .card.border-0.blue-shadow-bg
          .card-body.p-0
            .row.plan-period.mx-0
              %p.plan-title.text-grey-1.fw-semibold.p-0.fs-22.text-center= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.plan_self_study.title", exam_name: EXAM_NAME.upcase).html_safe
              %h.plan-description.text-grey-2.mb-0.text-center= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.plan_self_study.description").html_safe
              .price-container.responsive.px-0
                .row.m-0
                  - subscription_plan_codes(hide_ondemand: true).each do |option, plan_code|
                    - option = option.to_s
                    .col-4.d-flex.flex-column.align-items-center.justify-content-center.monthly-plan-options{ class: "#{'selected' if option == 'c'}", data: { option: option, plan_code: plan_code, plan_intro: t("user.plans.plan_#{option}.intro"), plan_subtitle: t("user.plans.plan_#{option}.subtitle"), plan_price: t("user.plans.plan_#{option}.sale_price"), plan_before: t("user.plans.plan_#{option}.price"), ondemand_plan: plan_code == RecurlyProxy::DEFAULT_ONDEMAND_PLAN_CODE } }
                      .select-text
                        SELECTED
                      .text-grey-5.price.fw-normal.text-decoration-line-through.fs-19= number_with_delimiter(t("user.plans.plan_#{option}.price"))
                      .text-grey-1.price.fw-semibold.fs-28.plan-price{ class: "plan-#{option}" }= t("user.plans.plan_#{option}.sale_price")
                      - if option == "c"
                        .text-dark-1.fs-16.fw-normal.d-lg-inline.text-center.d-flex.justify-content-center
                          = t("user.plans.plan_#{option}.only_months").html_safe
                      - else
                        .text-grey-1.fs-16.fw-normal.d-lg-inline.text-center.d-flex.justify-content-center{ class: "plan-#{option}" }= t("user.plans.plan_#{option}.months")
              .justify-content-center.p-0
                .get-started-btn
                  - subscription_plan_codes(hide_ondemand: true).each do |option, plan_code|
                    - option = option.to_s
                    = link_to t("user.plans.purchase_now").html_safe, "#", class: "btn purchase-now-btn plan-#{option} app-bg-1 #{'d-none' if option != 'c'}", id: "purchase-monthly-plan-#{option}-btn", data: { bs_toggle: "modal", bs_target: "#purchase-plan", plan_title: t("user.static_pages.plans.plan_options.plan_#{option}.title", exam_name: EXAM_NAME.upcase), plan_subtitle: t("user.plans.plan_#{option}.subtitle"), plan_intro: t("user.plans.plan_#{option}.intro"), plan_price: t("user.plans.plan_#{option}.sale_price"), plan_before: t("user.plans.plan_#{option}.price"), plan_code: plan_code, ondemand_plan: false }
              .try-for-free-btn.p-0
                = link_to t("user.plans.try_for_free"), "#trial-plan-sign-up"

        .card-footer
          .accordion#plan-self-study-benefits-accordion
            .accordion-item
              %h2.accordion-header#self-study-features-heading
                %button.accordion-button.plan-self-study{ type: "button", "data-bs-toggle" => "collapse", "data-bs-target" => "#self-study-accordion", "aria-expanded" => "true", "aria-controls" => "self-study-accordion" }
                  See all features
              .accordion-collapse.collapse.show#self-study-accordion{ "aria-labelledby" => "self-study-features-heading", "data-bs-parent" => "#plan-self-study-benefits-accordion" }
                .accordion-body
                  %ul.list-unstyled
                    - (8..15).each do |item|
                      %li.plan-self-study
                        %i.fas.fa-check
                        %span= t("user.static_pages.plans.ondemand_plans_mobile_view.plan_details.list_items.item_#{item}").html_safe

  - subscription_plan_codes(hide_ondemand: false).each do |option, plan_code|
    - option = option.to_s
    - next if %w(c a b).include?(option)

    = render "content_pages/plans/ondemand/plan_details_card", option: option, plan_code: plan_code, is_ondemand_page: true
