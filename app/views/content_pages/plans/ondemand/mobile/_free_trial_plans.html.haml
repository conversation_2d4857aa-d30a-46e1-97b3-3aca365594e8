.title.fs-26.text-dark-1.fw-semibold.text-center= "Start Learning Today for FREE."
.line-decorator-title.bg-dark-1.mx-auto
.row.section-tab-box.m-0
  .col-6.section-tab-group.d-flex.justify-content-center.active
    = link_to "#", class: "self-study-trial-plan trial-plan-tab", data: { tab: "self-study-trial" } do
      .section-tab
        %h3.fs-16.m-0.tab-title= t("user.static_pages.plans.ondemand_plans_mobile_view.self_study_tab", exam: EXAM_NAME.upcase)
  .col-6.section-tab-group.d-flex.justify-content-center
    = link_to "#", class: "ondemand-trial-plan trial-plan-tab", data: { tab: "ondemand-trial" } do
      .section-tab
        %h3.fs-16.m-0.tab-title.ondemand-title= t("user.static_pages.plans.ondemand_plans_mobile_view.ondemand_tab", exam: EXAM_NAME.upcase)
.section-content-wrapper
  .free-self-study-trial-group
    .plan-self-study.upper-tag
    .card.free-self-study-trial-card
      .card-body
        .title-and-price-wrapper.gap-3
          .title-wrapper.fs-22.d-flex.flex-column
            .trial-plan-title.text-grey-1.fw-semibold= t("user.static_pages.plans.ondemand_plans_mobile_view.ttp_self_study_trial.title")
            .subtitle.fs-16.text-grey-1= t("user.static_pages.plans.ondemand_plans_mobile_view.ttp_self_study_trial.subtitle")
          .price-wrapper
            .actual-price.text-grey-5.text-decoration-line-through= t("user.static_pages.plans.ondemand_plans_mobile_view.ttp_self_study_trial.actual_price")
            .trial-price.fs-27.text-end= t("user.static_pages.plans.ondemand_plans_mobile_view.ttp_self_study_trial.trial_price")
        .try-now-btn
          - if Setting.for("free_trial_enabled") and !cardless_trial_test?
            = link_to t("user.static_pages.plans.ondemand_plans_mobile_view.start_now_for_free"), "#",
              class: "btn m-auto w-100 fs-16 fw-semibold app-bg-1",
              data: { bs_toggle: "modal", bs_target: "#cardless-free-trial", plan_title: t("user.static_pages.plans.plan_options.plan_d.title", exam_name: EXAM_NAME.upcase),
              plan_subtitle: t("user.plans.plan_d.subtitle"), plan_code: Setting.for("recurly_5days_plan_code") }
          - else
            = link_to t("user.static_pages.plans.ondemand_plans_mobile_view.start_now_for_free"), "#",
              class: "btn m-auto w-100 fs-16 fw-semibold app-bg-1",
              data: { bs_toggle: "modal", bs_target: "#purchase-plan", plan_title: t("user.static_pages.plans.plan_options.plan_d.title", exam_name: EXAM_NAME.upcase),
              plan_subtitle: t("user.plans.plan_d.subtitle"), plan_price: t("user.plans.plan_d.sale_price"),
              plan_before: "1.00", plan_code: Setting.for("recurly_5days_plan_code") }

          .no-billing= t("user.static_pages.plans.ondemand_plans_mobile_view.no_automatic_billing")
  .free-ondemand-trial-group.d-none
    .ondemand-trial-plan.upper-tag
    .card.free-ondemand-trial-card
      .card-body
        .title-and-price-wrapper.gap-3
          .title-wrapper.fs-22
            .trial-plan-title.text-grey-1.fw-semibold= t("user.static_pages.plans.ondemand_plans_mobile_view.ttp_ondemand_trial.title")
            .subtitle.fs-16.text-grey-1= t("user.static_pages.plans.ondemand_plans_mobile_view.ttp_ondemand_trial.subtitle")
          .price-wrapper
            .actual-price.text-grey-5.text-decoration-line-through= t("user.static_pages.plans.ondemand_plans_mobile_view.ttp_ondemand_trial.actual_price")
            .trial-price.fs-27.text-end= t("user.static_pages.plans.ondemand_plans_mobile_view.ttp_ondemand_trial.trial_price")
        .try-now-btn
          = link_to t("user.static_pages.plans.ondemand_plans_mobile_view.start_now_for_free"), cart_path(cart: { plan_code: Setting.for("recurly_5days_ondemand_plan_code") }), class: "btn m-auto w-100 fs-16 fw-semibold purchase-link ondemand-trial-plan-btn"

          .no-billing= t("user.static_pages.plans.ondemand_plans_mobile_view.no_automatic_billing")
