- if only_gmat_or_ea and Setting.for("ondemand_enabled")
  - if show_mobile_plan_design_for_gmat?(@plans_mobile_view)
    .plans-mobile-view.d-block.d-md-none
      = render "content_pages/plans/ondemand/plans_mobile_screen"
  .plans-desktop-view{ class: "#{show_mobile_plan_design_for_gmat?(@plans_mobile_view) ? 'd-none d-md-block' : ''}" }
    = render "content_pages/plans/ondemand/plans"
- else
  = render "content_pages/plans/plans"

- if !gre_exam? and !ea_exam?
  %section.recognitions{ class: show_mobile_plan_design_for_gmat?(@plans_mobile_view) ? "d-none d-md-block" : "" }
    = render "content_pages/plans/recognitions"

%section.testimonials.position-relative{ class: show_mobile_plan_design_for_gmat?(@plans_mobile_view) ? "d-none d-md-block" : "" }
  .container-fluid.container-xxl.d-none.d-md-block
    .row.px-md-2
      .col.pt-4.ms-md-2.dark-dots
        = image_tag(asset_url("layout/controllers/content_pages/plans/dots_4x7.svg"), alt: "")
  .container.mt-5.mt-md-0.pb-5
    .row.mb-5.mb-mt-0
      .col
        %h2.fw-semibold.text-white.text-center.mb-3= t("user.static_pages.plans.testimonials.title", exam: exam_name_display_title(include_exam_type: true)).html_safe
        .line-decorator-title.bg-white.mx-auto
    - if gmat_exam?
      .row.position-relative.mb-5.mb-md-0.testimonials-slider
        .col-12.col-md-10.col-xl-8.mx-auto.testimonials-slider-wrapper
          #slider-plan-testimonials
            = render "content_pages/plans/testimonials_slider"
    - else
      .row.row-cols-1.row-cols-lg-3.justify-content-center.justify-content-lg-start.testimonials-container
        = render "content_pages/plans/testimonials_card"

    .position-absolute.dark-dots.bottom-0.end-0.mb-5.d-none.d-lg-block.me-2
      = image_tag(asset_url("layout/controllers/content_pages/plans/dots_4x7.svg"), alt: "")

%section.faq{ class: show_mobile_plan_design_for_gmat?(@plans_mobile_view) ? "faq-section-mobile" : "" }
  = render "content_pages/plans/faq_section"

%section.advertising{ class: show_mobile_plan_design_for_gmat?(@plans_mobile_view) ? "d-none d-md-block" : "" }
  = render "components/advertisement", title_route: "user.static_pages.advertisement.title", description_route: "user.static_pages.advertisement.description"

%section.modal.fade#purchase-plan
  = render "content_pages/plans/purchase_plan"

= render "components/recurly_lib"

- if Setting.for("free_trial_enabled")
  = render "content_pages/plans/cardless_free_trial"

%section.modal.fade#email-confirmation
