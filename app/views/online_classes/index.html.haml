- details = ondemand_live_teach_details

%section.bg-light-1.pt-5.py-sm-5.d-flex.flex-grow-1#online-classes
  .container
    .row.fixed-height.class-schedules
      .col-12.col-md-5.col-xl-4.class-schedules-lnfo
        .hero.position-relative
          .ellipse-decorator-1.position-absolute.top-0.start-0
            .top-dots.position-absolute.top-0.end-0.me-3
              = image_tag(asset_path("layout/controllers/users/online_tutoring/dots_2x4.svg"), alt: "", class: "align-top")
            .row
              .col-12
                %h1.px-2.fw-normal.mb-0
                  - if %w(gmat gre).include?(EXAM_NAME)
                    = image_tag(asset_path("layout/controllers/online_classes/onlineclasses_logo_#{EXAM_NAME}.svg"), alt: "TTP LiveTeach #{EXAM_NAME.upcase}", width: 191, height: 29)
              .col-12
                %h2.subtitle.my-4.fw-normal.text-center= t("user.dashboard.online_classes.index.hero.subtitle", exam: EXAM_NAME.upcase).html_safe

            .row.emphasyzed-subtitle.border.border-1.mx-4.mx-md-5.mx-lg-0.mx-xxl-2.flex-nowrap.mb-3
              .col.words.px-4.px-lg-3.px-xl-3.py-2.py-xxl-3.m-auto.text-sm-center.text-lg-start= t("user.dashboard.online_classes.index.hero.description").html_safe
              .col-auto.price.px-4.px-md-3.ps-lg-2.pe-lg-3.px-xl-3.d-flex.align-items-center
                .fw-semibold.text-white.words= t("user.dashboard.online_classes.index.hero.price")

            - if %w(gmat gre).include?(EXAM_NAME)
              .row.text-center.text-white.fs-28
                %i.far.fa-plus
          .row
            .col-12.text-center
              %h1.live-teach-btn.fs-24= t("user.dashboard.online_classes.index.hero.liveteach")
              %h2.subtitle.mt-4.fw-normal.text-center= t("user.dashboard.online_classes.index.hero.subtitle", exam: exam_name_display_title(include_exam_type: true)).html_safe
          .row.emphasyzed-subtitle.text-dark-2.border.border-1.mx-3.mx-md-0.mx-lg-0.liveteach-price-container
            .inner-container.position-relative
              .col-auto.col-md-12.col-xl-12.price.d-flex.justify-content-center.p-0
                .discounted-price-badge.fs-18.text-white.position-absolute
                  .discounted-price.fs-18.discounted-price.fw-semibold.text-white
                    %span= t("user.dashboard.online_classes.index.hero.discount_price")
                    %span OFF!
                %strike.original-price.me-2= t("user.dashboard.online_classes.index.hero.original_price")
                .fw-bold.fs-35.text-dark-2.words= t("user.dashboard.online_classes.index.hero.price")
              .col.col-md-12.col-xl-12.m-auto.p-0.text-center.fs-16.mt-2= t("user.dashboard.online_classes.index.hero.description").html_safe

          - if %w(gmat gre ea).include?(EXAM_NAME)
            .row
              .plus-icon.col-2.pe-0 +
              .award-winning.col-10.ps-0.text-center.fs-16= t("user.dashboard.online_classes.index.hero.#{details[:sub_description]}", exam: exam_name_display_title(include_exam_type: true), exam_name: EXAM_NAME.upcase).html_safe
            .view-full-details
              = link_to liveteach_path, target: :_blank do
                .text-center.d-flex.align-items-center.justify-content-center
                  .full-details.w-100= t("user.dashboard.online_classes.index.hero.full_details").html_safe
          .row
            .col-12.mb-5.pb-5.px-3
              %ul.benefits.list-unstyled
                - (1..7).each do |position|
                  %li.my-4.d-flex
                    %i.fal.fa-check.me-12
                    %p.mb-0.text-grey-4.fs-6.item-color= t("user.dashboard.online_classes.index.hero.benefits.item_#{position}", exam: exam_name_display_title(include_exam_type: true), exam_name: details[:exam_name], live_teach_months: details[:live_teach_months], platform: details[:platform]).html_safe
          .ellipse-decorator-3.position-absolute.bottom-0.end-0
          .bottom-dots.position-absolute.bottom-0.start-0.ms-3.mb-2
            = image_tag(asset_url("layout/controllers/users/online_tutoring/dots_3x3.svg"), alt: "", class: "align-top")
      .col-12.col-md-7.col-xl-8.online-class-info
        .schedule-section.position-relative#select-online-class
          .flex-column.h-100.bg-light-blue
            .title.col-12.border-bottom.border-1.d-flex.flex-column.justify-content-center.position-relative
              %h2.mb-0= t("user.dashboard.online_classes.index.schedule.title", exam: exam_name_display_title(include_exam_type: true)).html_safe
            .content.col-12.px-0.flex-grow-1.d-flex
              - if @online_classes.any? || @closed_online_classes.any?
                .classes-options.w-100
                  .accordion#accordion-online-classes
                    - previous_online_classes = 0
                    - if @online_classes.any?
                      - is_accordion_open = @online_classes.keys.one? && @online_classes.values.flatten.one?
                      - @online_classes.each do |month, online_classes|
                        = render "online_classes/index/class_available_option", online_classes: online_classes, user_session: current_user.present?, is_accordion_open: is_accordion_open, month: month, previous_online_classes: previous_online_classes
                        - previous_online_classes += online_classes.count
                    - else
                      .no-classes.text-center.mx-auto.my-5.m-xl-auto
                        = image_tag(asset_path("layout/controllers/online_classes/index/no_classes.svg"), width: 93, alt: "No classes to schedule", class: "pe-none")
                        %h2.text-dark-1.mt-3= t("user.dashboard.online_classes.index.schedule.no_classes.title")
                        %p.text-grey-3.fw-normal= t("user.dashboard.online_classes.index.schedule.no_classes.description")
                    - if @closed_online_classes.any?
                      .mt-5.text-center.closed-classes
                        .border-line
                          %span.border-span
                          %p.text-grey-4.fw-semibold.fs-16= t("user.online_class.closed_class.title")
                      -  @closed_online_classes.each do |month, online_classes|
                        = render "online_classes/index/closed_classes_option", online_classes: online_classes, user_session: current_user.present?, is_accordion_open: is_accordion_open, month: month, previous_online_classes: previous_online_classes
                        - previous_online_classes += online_classes.count
              - else
                .no-classes.text-center.mx-auto.my-5.m-xl-auto
                  = image_tag(asset_path("layout/controllers/online_classes/index/no_classes.svg"), width: 93, alt: "No classes to schedule", class: "pe-none")
                  %h2.text-dark-1.mt-3= t("user.dashboard.online_classes.index.schedule.no_classes.title")
                  %p.text-grey-3.fw-normal= t("user.dashboard.online_classes.index.schedule.no_classes.description")

- unless current_user
  = render "online_classes/index/modal_verify_user"

= render_marketing_modal("liveteach")
