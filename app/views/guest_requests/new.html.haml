%section.page-header-light.header-type-2.header-primary
  .container
    .row
      .col-12
        .header-inner
          .title-box
            = link_to (legacy_gmat? ? practice_test_path : take_diagnostic_guest_request_path), class: "back-btn-chevron section-color-2 section-bg-color-5-hover" do
              %i.far.fa-chevron-left
            %h2.section-color-2= legacy_gmat? ? t("guests.new.legacy_heading") : t("guests.new.heading")

%section.bg-light-1.flex-grow-1#start-diagnostic-test
  = image_tag(asset_url("layout/controllers/guest_requests/new/dots_3x3.svg"), alt: "", class: "dots-grid")
  .container
    = form_for @guest_request do |f|
      .row.no-gutters
        .contact-info.card-ttp.col-12.col-xl-5.me-xl-4.flex-column
          .d-flex.align-items-start.align-items-sm-center.title-wrapper
            = image_tag(asset_url("layout/controllers/guest_requests/new/element_number_one_circle.svg"), alt: "")
            %p.m-0.ms-3.mt-sm-1.text-dark-1.fs-20= t("guests.new.form.contact_info.title")
          .content-wrapper
            %p.m-0.text-grey-2= t("guests.new.form.contact_info.description")

            .first-name-wrapper.mt-4
              = f.label :first_name_label, class: "text-grey-2 fs-13 text-uppercase fw-semibold text-uppercase mb-2" do
                = t("guests.new.form.contact_info.first_name_label")
              = f.text_field :first_name, class: "form-control"

            .last-name-wrapper
              = f.label :last_name_label, class: "text-grey-2 fs-13 text-uppercase fw-semibold mb-2" do
                = t("guests.new.form.contact_info.last_name_label")
              = f.text_field :last_name, class: "form-control"

            .email-wrapper
              = f.label :email, class: "text-grey-2 fs-13 text-uppercase fw-semibold text-uppercase mb-2" do
                = t("guests.new.form.contact_info.email_label")
              = f.email_field :email, class: "form-control"

            = f.hidden_field :diagnostic, value: "true"

        .test-setup.card-ttp.col-12.col-xl.mt-3.mt-xl-0.flex-column
          .d-flex.align-items-start.align-items-sm-center.title-wrapper
            = image_tag(asset_url("layout/controllers/guest_requests/new/element_number_two_circle.svg"), alt: "")
            %p.m-0.ms-3.mt-sm-1.text-dark-1.fs-20= t("guests.new.form.test_setup.title")

          .content-wrapper
            .track-wrapper
              = f.hidden_field :track_id
              .text-grey-2.fs-13.text-uppercase.fw-semibold.mb-2= t("guests.new.form.test_setup.score_range", exam: EXAM_NAME.upcase)
              .score-bar-container
                .score-bar-box
                  %div{ id: "score-bar", class: "tracks-#{ordered_tracks.count}" }
                    - ordered_tracks.each do |track|
                      %button.text-decoration-none.border-0.bg-transparent{ type: "button", class: ["score #{track.name}", "for-default"], data: { track_id: track.id, for_legacy_course: track.for_legacy_course.to_s } }
                        %p.mb-0.score-type= track.name.titleize
                        .icon
                        .score-range= track.range

            - if EXAM_NAME != "ea"
              - course_types.each do |course_type, sections|
                - next unless sections.count > 1

                .question-order-wrapper{ class: course_type == :legacy ? "d-none" : nil, data: { course_type: course_type } }
                  .text-grey-2.fs-13.text-uppercase.fw-semibold.mb-3= t("guests.new.form.test_setup.question_order")

                  = select_tag :test_order, options_for_select(possible_section_order_options(sections, sections.count)), { class: "form-select" }

            = hidden_field_tag :test_order, course_types[:default].join(",")

            %p.m-0.mt-3.fs-14.text-grey-3.terms-notice= t("guests.new.accept_terms", link: link_to(t("user.content_pages.terms_and_conditions.page_title"), terms_path, target: "_blank")).html_safe

            = link_to t("guests.new.form.start_test#{legacy_gmat? ? '_legacy' : nil}"), "#", data: { url: validate_guest_request_path }, class: "btn btn-primary mt-4 w-100"

#guest-request-modal
