$('#main-navbar .profile-menu').append("<%= j render 'layouts/shared/profile_menu_dropdown', messages: @messages %>");
$('#notification-message-box .modal-body.bg-white').html("<%= j render 'users/home/<USER>', messages: @messages %>");

<% if @new_messages.any? %>
  $('.unread-messages-count').html('<%= t("user.top_nav.logged.message", count: @new_messages.count) %>')
  $('.unread-messages-count').addClass('active');
<% else %>
  $('.unread-messages-count').html('<%= t("user.top_nav.logged.message", count: 0) %>')
  $('.unread-messages-count').removeClass('active');
<% end %>

<% @messages.each do |message| %>
  $('#message-container').append("<%= j render 'users/home/<USER>', message: message, user: current_user %>");
<% end %>

<% @lesson_feedbacks.each do |feedback| %>
  $('#lesson-feedbacks-container').append("<%= j render 'users/home/<USER>', lesson_feedback: feedback %>");
<% end %>
