- if session[:ga_subscription_uuid].present? and current_user and Setting.for("track_analytics")
  - content_for :javascript do
    - ga_transaction_id, ga_sale_value, ga_plan_code = ga_tracking_variables
    - session[:ga_subscription_uuid] = nil

    / Google Analytics Ecommerce purchase event
    :javascript
      gtag('event', 'purchase', {
        'transaction_id': '#{ga_transaction_id}',
        'affiliation': 'TTP #{EXAM_NAME} App',
        'value': #{ga_sale_value},
        'currency': 'USD',
        'event_category': 'ecommerce',
        'event_label': '#{ga_plan_code}',
        'items': [{ 'name': '#{ga_plan_code}', 'category': '#{category}' }]
      });

    / TikTok
    :javascript
      ttq.track('CompletePayment', {
        content_id: '#{ga_transaction_id}',
        content_name: '#{ga_plan_code}',
        content_category: '#{category}',
        value: #{ga_sale_value},
        currency: 'USD',
      });

    / Attentive
    %script{ src: "https://cdn.attn.tv/ttp/dtag.js" }

    :javascript
      window.attentive.analytics.purchase({
        items: [
          {
            productId: '#{ga_plan_code}',
            productVariantId: '#{ga_plan_code}',
            category: '#{category}',
            price: { value: #{ga_sale_value}, currency: 'USD' },
            quantity: 1,
          }
        ],
        order: {
          orderId: '#{ga_transaction_id}'
        }
      });

    / AddShoppers
    :javascript
      AddShoppersConversion = {
        order_id: '#{ga_transaction_id}',
        value: '#{ga_sale_value}',
        currency: 'USD',
        email: '#{current_user.try(:email)}',
        offer_code: '#{ga_plan_code}'
      };

      var AddShoppersWidgetOptions = { 'loadCss': false, 'pushResponse': false };

      (!function(){
        var t=document.createElement("script");
        t.type="text/javascript",
        t.async=!0,
        t.id="AddShoppers",
        t.src="https://shop.pe/widget/widget_async.js#6800bf9a373185a6649ca945",
        document.getElementsByTagName("head")[0].appendChild(t)
      }());

    / Convert
    :javascript
      window._conv_q = window._conv_q || [];
      _conv_q.push(['pushRevenue', '#{ga_sale_value}', '1', '100029271']);

    / Convert 2
    :ruby
      convert_goal_id = nil
      convert_goal_id = "100030905" if ga_plan_code.include?("monthly")
      convert_goal_id = "100030906" if ga_plan_code.include?("4months")
      convert_goal_id = "100030907" if ga_plan_code.include?("6months")
      convert_goal_id = "100030908" if ga_plan_code.include?("trial")

    - if convert_goal_id.present?
      :javascript
        window._conv_q = window._conv_q || [];
        _conv_q.push(['triggerConversion', '#{convert_goal_id}']);

- if transaction_id.present? and current_user and Setting.for("marketing_scripts_enabled")
  - content_for :javascript do
    / Google Adwords Conversion
    :javascript
      /* <![CDATA[ */
      var google_conversion_id = 950763581;
      var google_conversion_language = 'en';
      var google_conversion_format = '3';
      var google_conversion_color = 'ffffff';
      var google_conversion_label = 'OhsRCOWo0V4QvYCuxQM';
      var google_remarketing_only = false;
      /* ]]> */

    %script{ src: "//www.googleadservices.com/pagead/conversion.js", type: "text/javascript" }
    %noscript
      %div{ style: "display:inline;" }
        %img{ alt: "", height: "1", src: "//www.googleadservices.com/pagead/conversion/950763581/?label=OhsRCOWo0V4QvYCuxQM&amp;guid=ON&amp;script=0", style: "border-style:none;", width: "1" }/

    / Conversion tracking
    :javascript
      var _vis_opt_revenue = '#{sale_value}';
      window._vis_opt_queue = window._vis_opt_queue || [];
      window._vis_opt_queue.push(function() {_vis_opt_revenue_conversion(_vis_opt_revenue);});

    / Quora Pixel & Conversions
    :javascript
      !function(q,e,v,n,t,s){if(q.qp) return; n=q.qp=function(){n.qp?n.qp.apply(n,arguments):n.queue.push(arguments);}; n.queue=[];t=document.createElement(e);t.async=!0;t.src=v; s=document.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s);}(window, 'script', 'https://a.quora.com/qevents.js');
      qp('init', '54ce4148931e4ed185b95281cd77e4d9');
      qp('track', 'ViewContent');

    %noscript
      %img{ height: "1", src: "https://q.quora.com/_/ad/54ce4148931e4ed185b95281cd77e4d9/pixel?tag=ViewContent&noscript=1", style: "display:none", width: "1" }/

    / Quora Conversion tracking
    :javascript
      qp('track', 'Purchase');

    / Tapfiliate
    - if current_user.previous_plan.nil?
      %script{ async: "", src: "//static.tapfiliate.com/tapfiliate.js", type: "text/javascript" }
      :javascript
        window['TapfiliateObject'] = i = 'tap';
        window[i] = window[i] || function () {
            (window[i].q = window[i].q || []).push(arguments);
        };
        tap('create', '1273-16d10c');
        var metaData = {
          'User id': '#{current_user.app_id}',
          'Email': '#{current_user.email}',
          'First name': '#{current_user.first_name}',
          'Last Name': '#{current_user.last_name}'
        };
        tap('conversion','#{transaction_id}', '#{sale_value}', { meta_data: metaData }, 'Standard', function(conversion) {
          $.ajax({
            type: 'put',
            data: { 'customer_id': '#{current_user.id}', 'conversion_id': conversion.id},
            url: '#{add_tapfiliate_conversion_id_to_user_path(current_user.id)}'
          });
        });

    / Referral Rock
    - if !current_user.referral_rock_commission_generated and !RecurlyProxy::TRIAL_PLAN_CODES.include?(plan_code) and sale_value > 1
      - current_user.update_columns(referral_rock_commission_generated: true)
      :javascript
        window.rrSpace = (
          rrSettingsConversion = {
            debug: 'false',
            parameters: {
              firstname: '#{current_user.first_name}',
              email: '#{current_user.email}',
              externalidentifier: '#{current_user.app_id}',
              amount: '#{sale_value}'
            }
          }
        );

    :javascript
      (function (f, r, n, d, b, y) { b = f.createElement(r), y = f.getElementsByTagName(r)[0]; b.async = 1; b.src = n; b.id = 'RR_DIVID'; y.parentNode.insertBefore(b, y); })(document, 'script', '//targettestprep.referralrock.com/webpixel/beta/universalv03.js');

    - if transaction_id.present? and current_user.generate_first_commission_in_shareasale?
      - value = RecurlyProxy::TRIAL_PLAN_CODES.include?(plan_code) ? transaction_id : "commissions-generated"
      - current_user.update_columns(shareasale_trial_order_number: value)

      / Share a Sale
      %img{ height: "1", src: "https://www.shareasale.com/sale.cfm?tracking=#{transaction_id}&amount=#{sale_value}&merchantID=#{Setting.for('shareasale_merchant_id')}&transtype=sale&xtype=#{plan_code}", width: "1" }/

    / Pinterest
    :javascript
      pintrk('track', 'checkout', {
        value: #{sale_value},
        order_quantity: 1,
        currency: 'USD'
      });

    / Facebook Conversion Tracking
    :javascript
      fbq('track', 'Purchase',
        {
          value: #{sale_value},
          currency: 'USD',
          content_name: '#{current_user.current_plan}',
          content_category: '#{category}'
        }
      );

    / Bing
    :javascript
      (function(w,d,t,r,u){var f,n,i;w[u]=w[u]||[],f=function(){var o={ti:"17521052"};o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")},n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function(){var s=this.readyState;s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)},i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)})(window,document,"script","//bat.bing.com/bat.js","uetq");

      window.uetq = window.uetq || [];

      window.uetq.push('event', 'Purchase', {'revenue_value': '#{sale_value}', 'currency': 'USD'});

    / Reddit
    :javascript
      rdt('track', 'Purchase');

    / Snapchat
    :javascript
      snaptr('track', 'PURCHASE', { 'currency': 'USD', 'price': '#{sale_value}', 'transaction_id': "#{transaction_id}" });
