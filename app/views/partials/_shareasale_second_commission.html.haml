- if current_user&.generate_second_commission_in_shareasale?
  - current_user.update_columns(shareasale_trial_order_number: "commissions-generated")
  - last_transaction = current_user.last_transaction
  - sale_value = last_transaction&.amount_in_cents.to_i / 100.00
  - transaction_id = last_transaction&.uuid
  - plan_code = current_user.current_plan

  / Share a Sale
  %img{ height: "1", src: "https://www.shareasale.com/sale.cfm?tracking=#{transaction_id}&amount=#{sale_value}&merchantID=#{Setting.for('shareasale_merchant_id')}&transtype=sale&xtype=#{plan_code}", width: "1" }/
