$('#loading-overlay').hide();
<% if @success %>
  var modal = $('#homework-takeaways-modal-<%= @takeaway.homework_task_id %>');
  var textarea = modal.find('.takeaways-editor');
  textarea.data('value', <%= raw @takeaway.content.to_json %>);
  if (textarea.length > 0) {
    var editorId = textarea.attr('id');
    if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances[editorId]) {
      CKEDITOR.instances[editorId].setData(<%= raw @takeaway.content.to_json %>);
    }
  }
  $('.homework-takeaways-modal').modal('hide');
  custom_message('success', 'Task Takeaways saved successfully!');
<% else %>
  custom_message('error', '<%= j(@takeaway.errors.full_messages.join(', ')) %>');
<% end %>
