:ruby
  study_start_date = user.study_plan_setting.study_start_date
  calendar_date = if params[:date]
    Date.parse(params[:date])
  elsif study_start_date < Date.current
    Date.current
  else
    study_start_date
  end

  excluded_dates = user.study_plan_setting.study_dates_to_exclude.map(&:to_date)
  exam_dates = user.test_dates.pluck(:next_exam_date)
  is_calenda_view_type_week = user.calendar_week_view_type?

= calendar calendar_date, user.id do |date, tasks_for_date, exam_score_type_calendar_task_dates|
  .d-flex.align-items-center{ class: is_calenda_view_type_week ? "justify-content-end" : "justify-content-between" }
    - unless is_calenda_view_type_week
      %span.date-text= "#{date.day == 1 ? "<span class='show-desktop'>#{date.strftime('%b').upcase}</span>" : ''} #{date.day}".html_safe
    - unless excluded_dates.include?(date) || exam_dates.include?(date) || tasks_for_date.blank?
      %span.study-hours.show-desktop= pretty_hours_and_minutes(tasks_for_date.reject(&:is_completed).map(&:task_study_time).sum)

  - if exam_dates.include?(date)
    = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/2x3_dots.svg"), class: "dots-grid-top")
    .test-image-wrapper
      = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/test_image.svg"), class: "test-image")
      %span.exam-name #{EXAM_NAME.upcase} Test #{user.exam_number(date)}
    = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/2x3_dots.svg"), class: "dots-grid-bottom")

  - if tasks_for_date.present? and !user.user_calendar.invalid?
    .tasks
      - tasks_for_date.each do |task|
        :ruby
          task_title ||= sanitize(calendar_task_title(task, current_user), tags: [])&.gsub("OPTIONAL TASK", "")
          data_attributes = { user_calendar_task_id: task.id, type: task.study_module_item.item_type, section: task.study_module_item.section }
          question_numbers = chapter_question_data(task.study_module_item)

          unless is_calenda_view_type_week
            data_attributes[:bs_toggle] = "tooltip"
            data_attributes[:bs_placement] = "top"
            data_attributes[:bs_title] = task_title
          end

        .task{ class: "#{task.state} #{'guide-without-data' if !question_numbers.present? && task.study_module_item.guide?}", data: data_attributes }
          %i.fa-exclamation-circle.fal.text-red-1.status.skipped{ data: { bs_toggle: "tooltip", bs_placement: "bottom", bs_title: "Skipped task" } }
          %i.fa-check-circle.fal.text-green-2.status.completed{ data: { bs_toggle: "tooltip", bs_placement: "bottom", bs_title: "Complete task" } }
          %i.fa-circle.fal.text-green-2.status.rescheduled{ data: { bs_toggle: "tooltip", bs_placement: "bottom", bs_title: "Rescheduled task" } }
          %i.far.fa-tag.status.optional-task{ data: { bs_toggle: "tooltip", bs_placement: "bottom", bs_title: "Optional task" } }
          %span.title= task_title

          - unless is_calenda_view_type_week
            = calendar_task_badge(task, current_user)

    - if user.calendar_month_view_type?
      .more-tasks-label
        %span

    - if user.ondemand_active? and user.user_calendar.ondemand_trial_study_modules?(date) and !current_user.access_ondemand?
      .locked-tasks.d-flex.flex-column.justify-content-center.align-items-center{ data: { bs_target: "#locked-chapter", bs_toggle: "modal" } }
        .center-img.d-flex.flex-column.justify-content-center.align-items-center
          = image_tag(asset_path("layout/controllers/users/study_plan/ondemand/calendar_view/locked_tasks_image.webp"))
        %span.locked-task-text.text-grey-1.fs-16.fw-semibold
          = t("user.study_plan.ondemand.locked_chapter.upgrade_to_unlock")

      = render "users/study_plan/calendar_view/locked_chapter_modal"

  - elsif date >= Date.current && current_user.user_calendar.study_time_left_for_date?(date)
    - last_exam_score_task_date = exam_score_type_calendar_task_dates.select { |task_date| task_date < date }.last

    - if can_next_task_be_pushed_to_date?(date, last_exam_score_task_date)
      .update-wrapper.mt-5
        .no-tasks-img.mb-4
          = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/need_more_tasks.svg"))
        .fw-semibold.mt-4 Need More Tasks?
        %button.btn.btn-primary.mt-4{ data: { bs_toggle: "modal", bs_target: "#update-study-plan-confirmation-modal" } } Update

.modals


