- if show_setup_study_plan_setting_modal?
  .modal.auto-trigger.p-0#calendar-setup-modal{ data: { bs_focus: "true", bs_keyboard: "false", bs_backdrop: "static" } }
    .modal-dialog.modal-dialog-centered.modal-xl
      .modal-content
        = form_for current_user.build_study_plan_setting do |f|
          = f.hidden_field :is_calendar_view_enabled
          = f.hidden_field :is_study_hours_per_week

          %section#view-preferences{ data: { step: 1 } }
            .modal-header.border-bottom-0
            .modal-body.text-center
              .mx-auto.title-wrapper
                %h2.mb-0.text-blue-2
                  = t("user.study_plan.modals.study_plan_settings.view_preferences.title", first_name: current_user.first_name)
                %p.pt-3.text-grey-2.fw-normal
                  = t("user.study_plan.modals.study_plan_settings.view_preferences.description")

              = render "users/study_plan/study_plan_settings/view_preference", f: f
            .modal-footer.justify-content-between.justify-content-sm-center.border-top-0.mb-3
              %button.btn.btn-outline-primary#setup-later{ type: "submit" }
                = t("user.study_plan.modals.study_plan_settings.view_preferences.actions.setup_later")
              %button.btn.btn-primary.confirm.next{ type: "button", data: { step: 1 } }
                = t("user.study_plan.modals.study_plan_settings.view_preferences.actions.confirm")

          %section.d-none#date-time-preferences{ data: { step: 2 } }
            .modal-header.border-color-grey-6
              .d-flex.align-items-center
                .step-number.d-inline-block.fw-semibold.rounded-circle.text-center 1
                %h3.mb-0.ms-3= t("user.study_plan.modals.study_plan_settings.date_time_preferences.title").html_safe
            .modal-body
              = render "users/study_plan/study_plan_settings/calendar/date_time_preferences", f: f
            .modal-footer.justify-content-between.justify-content-sm-center.border-top-0.mb-3
              %button.btn.btn-outline-primary.previous{ data: { step: 2 } }
                = t("user.study_plan.modals.study_plan_settings.date_time_preferences.actions.previous")
              %button.btn.btn-primary.confirm.next{ type: "button", data: { step: 2 } }
                = t("user.study_plan.modals.study_plan_settings.date_time_preferences.actions.next")

          %section.d-none#availability{ data: { exam_dates: current_user.test_dates.pluck(:next_exam_date).map(&:to_s).join(","), excluded_dates: current_user.study_plan_setting.study_dates_to_exclude.map(&:to_s).join(","), exam_name: EXAM_NAME, step: 3 } }
            .modal-header.border-color-grey-6
              .d-flex.align-items-center
                .step-number.d-inline-block.fw-semibold.rounded-circle.text-center 2
                %h3.mb-0.ms-3= t("user.study_plan.modals.study_plan_settings.availability.title").html_safe
            .modal-body.text-center
              %h4.text-primary-1.mb-4= t("user.study_plan.modals.study_plan_settings.availability.description")
              = render "users/study_plan/study_plan_settings/calendar/availability", f: f
            .modal-footer.justify-content-between.justify-content-sm-center.border-top-0.mb-3
              %button.btn.btn-outline-primary.previous{ data: { step: 3 } }
                = t("user.study_plan.modals.study_plan_settings.availability.actions.previous")
              %button.btn.btn-primary.finish.show-overlay{ type: "submit", data: { step: 3 } }
                = t("user.study_plan.modals.study_plan_settings.availability.actions.finish")

- elsif current_user.mission_study_plan_enabled? and show_new_chapter_evaluation_available_modal?
  :ruby
    section, subsection = ChapterEvaluation.new_evaluation_section_and_subsection(current_user)
    section_or_subsection_key = (subsection || section).downcase.gsub(" ", "_")
    Flag.create(user: current_user, flaggable: current_user, label: "new_#{section_or_subsection_key}_evaluations_acknowledged")

  .modal.auto-trigger#new-chapter-evaluation-modal
    .modal-dialog.modal-dialog-centered
      .modal-content{ class: "section-content #{section}" }
        .modal-header.border-bottom-0
          %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
            %span.visually-hidden Close
        .modal-body.text-center.section-content{ class: section }
          %p= image_tag(asset_path("layout/controllers/users/study_plan/book_#{section}.svg"))
          %h3.section-content.verbal
            = t("user.study_plan.modals.new_chapter_evaluation.title", name: current_user.first_name)
          %p.message.mt-3.mb-0.fs-18
            = t("user.study_plan.modals.new_chapter_evaluation.content_html", section_or_subsection: (subsection&.titleize || titleize_section_label_name(section)))
        .modal-footer.justify-content-center.border-top-0.pb-4
          = link_to t("user.study_plan.modals.new_chapter_evaluation.take_me_there_button"), evaluations_path(section: section, subsection: subsection), class: "btn btn-section px-5 mb-3", target: "_blank"

- elsif current_user.mission_study_plan_enabled? and (chapter = Chapter.new_chapter_available_for(current_user))
  - Flag.create(user: current_user, flaggable: current_user, label: "new_chapter_acknowledged")
  .modal.auto-trigger#new-chapter-modal
    .modal-dialog.modal-dialog-centered
      .modal-content{ class: "section-content #{chapter.section}" }
        .modal-header.border-bottom-0
          %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
            %span.visually-hidden Close
        .modal-body.text-center.section-content{ class: section }
          %p= image_tag(asset_path("layout/controllers/users/study_plan/book_#{chapter.section}.svg"))
          %h3.section-content.verbal
            = t("user.study_plan.modals.new_chapter.title", name: current_user.first_name)
          %p.message.mt-3.mb-0.fs-18
            = t("user.study_plan.modals.new_chapter.content")
            %span.section-color-2.fw-semibold= chapter.name
        .modal-footer.justify-content-center.border-top-0.pb-4
          = link_to t("user.study_plan.modals.new_chapter.take_me_there_button"), lessons_path(section: chapter.section, subsection: chapter.subsection), class: "btn btn-section px-5 mb-3", target: "_blank"

- elsif current_user.subscription_expiring_soon? and !current_user.access_online_class?
  .modal.auto-trigger#subscription-exiring-soon-modal
    .modal-dialog.modal-dialog-centered
      .modal-content
        .modal-header.border-bottom-0
          = button_to "javascript:void(0)", class: "btn-close", data: { disable_with: "", bs_dismiss: "modal" } do
            %span.visually-hidden Close
        .modal-body.section-content{ class: section }
          %p.text-center
            = image_tag(asset_path("layout/controllers/users/study_plan/course_expiring.svg"))
          %h3.section-content.verbal.text-center
            = t("user.study_plan.modals.subscription_expiring.title")
          .bg-grey-9.mt-4.mb-0.p-4.rounded
            = t("user.study_plan.modals.subscription_expiring.content_html", name: current_user.first_name, plan_name: (current_user.current_subscription&.plan&.name || "subscription"), new_subscription_path: new_subscription_path)
        .modal-footer.border-top-0.mb-3
          = button_to t("user.study_plan.modals.subscription_expiring.skip_button"), "javascript:void(0)", class: "btn btn-outline-secondary", data: { disable_with: "Please wait...", bs_dismiss: "modal" }
          = link_to t("user.study_plan.modals.subscription_expiring.update_button"), new_subscription_path, class: "btn btn-primary"

- elsif current_user.mission_study_plan_enabled? and current_user.study_plan_guide_enabled and !Flag.flagged_after?(current_user, current_user, "not_following_study_plan_acknowledged", 1.week.ago)
  .modal.fade#skipping-study-plan-modal
    .modal-dialog.modal-dialog-centered
      .modal-content
        .modal-header.border-bottom-0
          %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
            %span.visually-hidden Close
        .modal-body
          %p.text-center= image_tag(asset_path("layout/controllers/users/study_plan/skipping.svg"))
          %h3.text-center= t("user.study_plan.modals.skipping.title", name: current_user.first_name)
          = t("user.study_plan.modals.skipping.content_html")

          = form_for current_user, url: customize_study_plan_path, method: :post, html: { id: "disable-mission-reminder-form", class: "show-overlay" } do |_f|
            = hidden_field_tag "user[study_plan_guide_enabled]", false

        .modal-footer.justify-content-center.border-top-0
          %button.btn.btn-outline-secondary{ type: "submit", form: "disable-mission-reminder-form" }
            = t("user.study_plan.modals.skipping.disable_button_html")
          %button.btn.btn-primary.snooze{ type: "button", data: { url: toggle_flag_path(id: current_user.id, type: "user", label: "not_following_study_plan_acknowledged") } }
            = t("user.study_plan.modals.skipping.snooze_button_html")

- elsif current_user.calendar_study_plan_enabled? and (current_user.user_calendar.invalid? or current_user.user_calendar.past_incomplete_calendar_tasks.count > 15)
  .modal.auto-trigger.p-0#calendar-reshuffle-all-tasks{ data: { bs_focus: "true", bs_keyboard: "false", bs_backdrop: "static" } }
    .modal-dialog.modal-dialog-centered
      .modal-content
        .modal-header.border-bottom-0
        .modal-body
          .text-center
            = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/calendar_unavailable_study_day.svg"))
            %h3= t("user.study_plan.modals.reshuffle_all_tasks.title")
            %p= t("user.study_plan.modals.reshuffle_all_tasks.description").html_safe
        .modal-footer.justify-content-center.border-top-0
          - url = user_calendar_path(id: current_user.user_calendar, is_reshuffle_all: true, reshuffle_calendar: true)
          - url = user_calendar_path(id: current_user.user_calendar, regenerate: true) if current_user.user_calendar.invalid?

          = link_to t("user.study_plan.modals.calendar_reshuffle_skip.actions.update_calendar"), url, method: :put, class: "btn btn-primary go-to-study-plan-settings show-overlay"

- elsif current_user.calendar_study_plan_enabled? and current_user.user_calendar.past_incomplete_tasks? and current_user.user_calendar.past_incomplete_calendar_tasks.count <= 15
  .modal.auto-trigger.p-0#calendar-reshuffle-skip-modal{ data: { bs_focus: "true", bs_keyboard: "false", bs_backdrop: "static" } }
    .modal-dialog.modal-dialog-centered.modal-lg
      .modal-content
        = form_for current_user.user_calendar do
          = hidden_field_tag :is_reshuffle_all
          = hidden_field_tag :is_skip_all
          = hidden_field_tag :reshuffle_task_ids
          = hidden_field_tag :skip_task_ids
          = hidden_field_tag :reshuffle_calendar, true

          .modal-header.border-bottom-0
            %h3.text-light-5.mb-0= t("user.study_plan.modals.calendar_reshuffle_skip.title", name: current_user.first_name).html_safe
            = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/reshuffle_skip_modal_header_laptop.svg"), class: "laptop d-none d-md-block")
            = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/2x4_dots.svg"), class: "dots")
          .modal-body
            %p.mb-0= t("user.study_plan.modals.calendar_reshuffle_skip.description").html_safe

            %table.table.ttp-datatable
              %thead.table-light
                %th.fw-semibold= t("user.study_plan.modals.calendar_reshuffle_skip.not_completed", count: current_user.user_calendar.past_incomplete_calendar_tasks.count)
                %th.fw-semibold.text-center.reshuffle-all
                  .d-flex.align-items-center.flex-column.flex-md-row
                    %span.icon{ data: { for: "reshuffle-all" } }
                    %span.action-name.ms-0.ms-md-2= t("user.study_plan.modals.calendar_reshuffle_skip.actions.reshuffle_all").html_safe
                %th.fw-semibold.text-center.skip-all
                  .d-flex.align-items-center.flex-column.flex-md-row
                    %span.icon{ data: { for: "skip-all" } }
                    %span.action-name.ms-0.ms-md-2= t("user.study_plan.modals.calendar_reshuffle_skip.actions.skip_all").html_safe

              %tbody.align-middle
                - current_user.user_calendar.past_incomplete_calendar_tasks.each_with_index do |task, index|
                  %tr
                    %td.position-relative
                      .row
                        .col-12.d-flex.align-items-center
                          %span.numeration.flex-shrink-0.flex-grow-0.section-color-2.my-auto.text-center.d-none.d-md-block.border-2.section-border-color-4.text-blue-2= index + 1
                          %span.task-name.text-primary-1.fw-semibold= calendar_task_title(task, current_user)
                    %td.d-table-cell
                      .d-flex.align-items-center.flex-column.flex-md-row
                        %span.icon{ data: { for: "reshuffle", task_id: task.id } }
                        %span.action-name.text-blue-2.fs-13.text-uppercase.ms-0.ms-md-2= t("user.study_plan.modals.calendar_reshuffle_skip.actions.reshuffle")
                        %i.fal.fa-info-circle{ data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: t("user.study_plan.modals.calendar_reshuffle_skip.actions.reshuffle_tooltip_title") } }
                    %td.d-table-cell
                      .d-flex.align-items-center.flex-column.flex-md-row
                        %span.icon{ data: { for: "skip", task_id: task.id } }
                        %span.action-name.text-red-1.fs-13.text-uppercase.ms-0.ms-md-2= t("user.study_plan.modals.calendar_reshuffle_skip.actions.skip")
                        %i.fal.fa-info-circle{ data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: t("user.study_plan.modals.calendar_reshuffle_skip.actions.skip_tooltip_title") } }

          .modal-footer.justify-content-center.border-top-0
            %button.btn.btn-primary.show-overlay{ type: "submit" }= t("user.study_plan.modals.calendar_reshuffle_skip.actions.update_calendar")

- elsif current_user.calendar_study_plan_enabled? and !Flag.flagged_after?(current_user, current_user, "want_more_tasks_for_day_modal_acknowledged", Date.current.beginning_of_day) and current_user.user_calendar.study_time_left_for_date?(Date.current)
  .modal.auto-trigger.p-0#want-more-tasks-for-day-modal{ data: { bs_focus: "true", bs_keyboard: "false", bs_backdrop: "static" } }
    .modal-dialog.modal-dialog-centered.modal-lg
      .modal-content
        = form_for current_user.user_calendar do
          = hidden_field_tag :push_task_for_current_date, true

        .modal-header.border-bottom-0
          %h3.text-light-5.mb-0= t("user.study_plan.modals.more_tasks_for_day.title", name: current_user.first_name).html_safe
          = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/reshuffle_skip_modal_header_laptop.svg"), class: "laptop d-none d-md-block")
          = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/2x4_dots.svg"), class: "dots")
        .modal-body
          %p.mb-0= t("user.study_plan.modals.more_tasks_for_day.description")

        .modal-footer.justify-content-between.justify-content-sm-center.border-top-0
          = button_to t("user.study_plan.modals.more_tasks_for_day.actions.cancel"), user_calendar_path(id: current_user.user_calendar, push_task_for_current_date: false), method: :put, class: "btn btn-outline-primary show-overlay"
          = button_to t("user.study_plan.modals.more_tasks_for_day.actions.update_calendar"), user_calendar_path(id: current_user.user_calendar, push_task_for_current_date: true), method: :put, class: "btn btn-primary show-overlay"

.modal#questionnaire-attempt-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header
        %h4.modal-title= t("user.study_plan.modals.questionnaire_attempt.title")
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body

.modal#no-questions-for-weakest-topics-test
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body.text-center
        %p= image_tag(asset_path("layout/controllers/users/study_plan/test_unavailable.svg"))
        %h3.mt-3= t("user.study_plan.modals.no_questions_for_weakest_topics_test.title")
        %p.message.mt-3.mb-5.fs-18= t("user.study_plan.modals.no_questions_for_weakest_topics_test.content")

.modal#accuracy-not-met-easy-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body.section-content{ class: section }
        %p.text-center
          = image_tag(asset_path("layout/controllers/users/study_plan/accuracy_not_met.svg"))
        %h3.text-center= t("user.study_plan.modals.accuracy_not_met.title")
        %p.text-center.fs-18= t("user.study_plan.modals.accuracy_not_met.easy.intro")

        .carousel.slide#accuracy-not-met-easy{ data: { bs_ride: "carousel" } }
          .carousel-inner
            = t("user.study_plan.modals.accuracy_not_met.easy.content_html")

          .actions
            .previous
              = link_to "Previous", "#accuracy-not-met-easy", class: "btn btn-outline-secondary", data: { bs_slide: "prev" }
            .indicators
              %ol.carousel-indicators
                %li.active{ data: { bs_slide_to: "0", bs_target: "#accuracy-not-met-easy" } }
                %li{ data: { bs_slide_to: "1", bs_target: "#accuracy-not-met-easy" } }
                %li{ data: { bs_slide_to: "2", bs_target: "#accuracy-not-met-easy" } }
            .next
              = link_to "Next", "#accuracy-not-met-easy", class: "btn btn-primary", data: { bs_slide: "next" }

.modal#accuracy-not-met-medium-a-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body.section-content{ class: section }
        %p.text-center
          = image_tag(asset_path("layout/controllers/users/study_plan/accuracy_not_met.svg"))
        %h3.text-center= t("user.study_plan.modals.accuracy_not_met.title")
        %p.text-center.fs-18= t("user.study_plan.modals.accuracy_not_met.medium_a.intro")
        .carousel.slide#accuracy-not-met-medium-a{ data: { bs_ride: "carousel" } }
          .carousel-inner
            = t("user.study_plan.modals.accuracy_not_met.medium_a.content_html", move_on_destination: current_track_name == "average" ? "next chapter" : "hard-level chapter tests")

          .actions
            .previous
              = link_to "Previous", "#accuracy-not-met-medium-a", class: "btn btn-outline-secondary", data: { bs_slide: "prev" }
            .indicators
              %ol.carousel-indicators
                %li.active{ data: { bs_slide_to: "0", bs_target: "#accuracy-not-met-medium-a" } }
                %li{ data: { bs_slide_to: "1", bs_target: "#accuracy-not-met-medium-a" } }
                %li{ data: { bs_slide_to: "2", bs_target: "#accuracy-not-met-medium-a" } }
            .next
              = link_to "Next", "#accuracy-not-met-medium-a", class: "btn btn-primary", data: { bs_slide: "next" }

.modal#accuracy-not-met-medium-b-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body.section-content{ class: section }
        %p.text-center
          = image_tag(asset_path("layout/controllers/users/study_plan/accuracy_not_met.svg"))
        %h3.text-center= t("user.study_plan.modals.accuracy_not_met.title")
        %p.text-center.fs-18= t("user.study_plan.modals.accuracy_not_met.medium_b.intro")
        %p.text-center.fs-18= t("user.study_plan.modals.accuracy_not_met.medium_b.intro_2")

        .carousel.slide#accuracy-not-met-medium-b{ data: { bs_ride: "carousel" } }
          .carousel-inner
            = t("user.study_plan.modals.accuracy_not_met.medium_b.content_html", move_on_destination: current_track_name == "average" ? "next chapter" : "hard-level chapter tests")

          .actions
            .previous
              = link_to "Previous", "#accuracy-not-met-medium-b", class: "btn btn-outline-secondary", data: { bs_slide: "prev" }
            .indicators
              %ol.carousel-indicators
                %li.active{ data: { bs_slide_to: "0", bs_target: "#accuracy-not-met-medium-b" } }
                %li{ data: { bs_slide_to: "1", bs_target: "#accuracy-not-met-medium-b" } }
                %li{ data: { bs_slide_to: "2", bs_target: "#accuracy-not-met-medium-b" } }
            .next
              = link_to "Next", "#accuracy-not-met-medium-b", class: "btn btn-primary", data: { bs_slide: "next" }

.modal#accuracy-not-met-hard-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body.section-content{ class: section }
        %p.text-center
          = image_tag(asset_path("layout/controllers/users/study_plan/accuracy_not_met.svg"))
        %h3.text-center= t("user.study_plan.modals.accuracy_not_met.title")
        %p.text-center.fs-18= t("user.study_plan.modals.accuracy_not_met.hard.intro")
        .carousel.slide#accuracy-not-met-hard{ data: { bs_ride: "carousel" } }
          .carousel-inner
            = t("user.study_plan.modals.accuracy_not_met.hard.content_html")

          .actions
            .previous
              = link_to "Previous", "#accuracy-not-met-hard", class: "btn btn-outline-secondary", data: { bs_slide: "prev" }
            .indicators
              %ol.carousel-indicators
                %li.active{ data: { bs_slide_to: "0", bs_target: "#accuracy-not-met-hard" } }
                %li{ data: { bs_slide_to: "1", bs_target: "#accuracy-not-met-hard" } }
            .next
              = link_to "Next", "#accuracy-not-met-hard", class: "btn btn-primary", data: { bs_slide: "next" }

.modal.p-0#what-am-i-missing-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body
        - view_type = current_user.calendar_study_plan_enabled? ? "calendar_view" : "mission_view"
        .text-center
          = image_tag(asset_path("layout/controllers/users/study_plan/system_info.svg"))
        %h3.text-center
          = t("user.study_plan.modals.missing.title")
        .bg-light-2.p-4.mt-4
          .content-item
            %span
              = t("user.study_plan.modals.missing.#{view_type}.description", track_range: current_user.track.range).html_safe
      .modal-footer.justify-content-center.border-top-0.pt-4
        %button.btn.btn-primary.snooze{ type: "button", data: { bs_dismiss: "modal" } }
          = t("user.study_plan.modals.missing.got_it")

- if current_user.calendar_study_plan_enabled?
  .modal.p-0#calendar-unavailable-study-day
    .modal-dialog.modal-dialog-centered
      .modal-content
        .modal-header.border-bottom-0
          %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
            %span.visually-hidden Close
        .modal-body
          .text-center
            = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/calendar_unavailable_study_day.svg"))
            %h3= t("user.study_plan.modals.unavailable_study_day.title")
            %p= t("user.study_plan.modals.unavailable_study_day.description").html_safe
        .modal-footer.justify-content-center.border-top-0
          = link_to "Go to <span class='hidden-mobile'>study plan</span> settings".html_safe, adjust_study_plan_path, class: "btn btn-primary go-to-study-plan-settings"

.modal#update-study-plan-confirmation-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body
        .text-center
          = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/update_calendar_tasks.svg"))
          %h3.mt-4.mb-4 Let’s update your calendar
          %span= t("user.study_plan.modals.update_calendar_confirmation.content").html_safe
      .modal-footer.justify-content-center.border-top-0.pt-4
        %button.btn.btn-outline-secondary.snooze{ type: "button", data: { bs_dismiss: "modal" } } CANCEL
        = button_to "Update", user_calendar_path(id: current_user.user_calendar, is_reshuffle_all: true, reshuffle_calendar: true), method: :put, class: "btn btn-primary need-more-task-btn show-overlay"

.modal#update-back-to-self-study-confirmation-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body.text-center.fs-18
        %p= image_tag(asset_path("layout/controllers/users/study_plan/ondemand/back_to_self_study_confirmation.webp"))
        %p.mt-4.mb-0
        = t("user.study_plan.ondemand.modals.turn_off_message")
      .modal-footer.justify-content-center.border-top-0.pb-4.mb-3
        %button.btn.btn-outline-secondary.me-md-3.go-back{ type: "button", data: { bs_dismiss: "modal" } }
          = t("user.study_plan.ondemand.modals.back_btn")
        %button.btn.btn-primary.confirm{ type: "button" }
          = t("user.study_plan.ondemand.modals.turn_off_btn")
