:ruby
  is_demand_user = current_user.ondemand_active?
  calendar_study_plan_extends_banner = Flag.flagged?(current_user, current_user, "calendar_study_plan_extends_banner")
  show_exam_date_banner = current_user.calendar_study_plan_enabled? && show_study_plan_extension_banner? && !calendar_study_plan_extends_banner
  gmat_focus_edition_update_flag = Flag.flagged?(current_user, current_user, "gmat_focus_edition_update_banner")

%section.study-plan-header
  .container
    .row
      .col-12.col-md-12.col-xl-5.study-plan-settings
        %h1
          %span.name #{current_user.first_name}'s
          %span.study-plan-text
            - if is_demand_user
              = t("user.study_plan.ondemand_title")
            - else
              = exam_title
              = t("user.study_plan.title")

        .course-content.pt-1
          %p.align-children-middle.mb-3
            %span.study-plan-button
              = link_to adjust_study_plan_path, class: "show-overlay text-blue-3" do
                %span.text-grey-3.adjust-study-plan Adjust Study Plan
                %i.fal.fa-cog{ data: { bs_toggle: "tooltip", bs_placement: "bottom", bs_trigger: "hover" } }
            %span.separator
            %span.study-plan-button
              = link_to "#{adjust_study_plan_path}#section-accelerated-study-plan", class: "show-overlay text-blue-3" do
                %span.short-study-plan.pe-none
                  = check_box_tag :short_study_plan, "1", current_user.short_study_plan, id: "short-study-plan", class: "awesome-checkbox", data: { icon_checked: "icon-toggle-on", icon_unchecked: "icon-toggle-off", label_content_checked: "<span>Accelerated Study Plan</span>", label_content_unchecked: "<span>Accelerated Study Plan</span>", label_class_checked: "active text-grey-4 align-children-middle", label_class_unchecked: "text-grey-4 align-children-middle", icon_position: "right", url: toggle_study_plan_length_path }

      - if (!current_user.access_ondemand? or current_user.subscription_free?) and user_has_ondemand_access?
        = render "users/study_plan/ondemand_switch"

      .col-12.col-md-12.col-xl-7.mt-1.mt-md-4.pt-2.progress-details
        .d-flex.flex-wrap.flex-lg-nowrap
          .progress-item
            .detail
              %span
                .number
                  %span.study-plan-progress
                  %span.sub-title= t("user.study_plan.header.progress")
                .subtitle-task
                  .sub-title= t("user.study_plan.header.completion")
                  %span{ class: current_user.calendar_study_plan_enabled? ? "tasks-completed" : "modules-completed" }
                  %span= " / "
                  %span{ class: current_user.calendar_study_plan_enabled? ? "total-tasks" : "total-modules" }
              - missing_link_test = is_demand_user ? t("user.study_plan.header.ondemand_missing") : t("user.study_plan.header.missing")
              = link_to missing_link_test, "#", class: "text-blue-3 question-modal hidden-mobile", data: { bs_toggle: "modal", bs_target: "#what-am-i-missing-modal" }

        .progress
          .progress-bar
        .progress-link.hidden-mobile
          - view_type = current_user.calendar_study_plan_enabled? ? "calendar_view" : "mission_view"

        .progress-item
          .test-dates-header
            .number
              %span.date-box
                %span.sub-title= t("user.study_plan.header.next_test", exam_name: EXAM_NAME.upcase)
                %span.date-text
                  - if current_user.next_exam_date.present?
                    = current_user.next_exam_date.strftime("%m/%d/%y")
                  - else
                    = "N/A"
              %span.border-box
              %span.date-box
                %span.sub-title= t("user.study_plan.header.last_test", exam_name: EXAM_NAME.upcase)
                %span.date-text
                  - if current_user.last_exam_date.present?
                    = current_user.last_exam_date.strftime("%m/%d/%y")
                  - else
                    = "N/A"
              - if current_user.calendar_study_plan_enabled?
                %span.border-box
                %span.date-box.study-plan-end-date
                  %span.sub-title= t("user.study_plan.header.study_plan_end_date")
                  %span.date-text{ class: "#{'blue-light' if current_user.next_exam_date.present? and !show_study_plan_extension_banner?}" }
                    - if current_user.user_calendar.present?
                      = current_user.user_calendar.study_plan_end_date.strftime("%m/%d/%y")
                      - if current_user.next_exam_date.present? and !show_study_plan_extension_banner?
                        %span.tooltip-hover
                          = link_to "#", class: "info-button", tabindex: "0" do
                            %i.far.fa-info-circle
                            %span.on-hover-tooltip{ class: "#{'beyond-date' unless show_study_plan_extension_banner?}" }
                              - unless show_study_plan_extension_banner?
                                %p= t("user.study_plan.header.after_exam", exam_name: EXAM_NAME.upcase)
                    - else
                      = "N/A"

- if show_exam_date_banner
  %section.calendar-view-alerts
    = render "users/study_plan/calendar_view/calendar_date_alert_banner", is_demand_user: is_demand_user
    = render "users/study_plan/calendar_view/calendar_date_info_modal"

%section.bg-light-1.pb-3.flex-grow-1{ data: { view_type: current_user.calendar_study_plan_enabled? ? "calendar" : "mission" } }
  - if current_user.calendar_study_plan_enabled?
    - if gmat_exam?
      .calendar-gmat-focus-edition-alert{ class: ((show_exam_date_banner || gmat_focus_edition_update_flag) ? "d-none" : nil), data: { gmat_focus_edition_banner_flag: gmat_focus_edition_update_flag.to_s } }
        = render "users/study_plan/gmat_focus_edition_update_banner"
    .container
      #calendar{ data: { calendar_view_type: current_user.study_plan_setting.calendar_view_type } }
        = render "users/study_plan/calendar_view", user: current_user, study_plan: @study_plan
  - else
    - if gmat_exam?
      .gmat-focus-edition-alert{ class: gmat_focus_edition_update_flag ? "d-none" : nil }
        = render "users/study_plan/gmat_focus_edition_update_banner"
    = render "users/study_plan/mission_view", user: current_user, study_plan: @study_plan

- if current_user.ondemand_active? && !Flag.flagged?(current_user, current_user, "ondemand_congratulations_modal_acknowledged")
  = render "components/ondemand/congratulations_modal"

- if gmat_exam? and !gmat_focus_edition_update_flag
  = render "users/study_plan/gmat_focus_modal"


