.card-ttp.messages.h-100.overflow-hidden
  .header-link
    %i.far.fa-envelope
    %span= t("user.home.messages.title").html_safe
  .message-list
    - if show_referrals_element?
      .referrals-banner-container#referrals-banner-container
        = link_to referrals_path do
          .referrals-banner
            .referrals-program-btn-container
              .referrals-program-btn
            %span.get-2-extra-weeks-for-free.fs-17.fw-semibold
              = t("user.home.messages.referrals_banner.title")
              %br
              = t("user.home.messages.referrals_banner.subtitle")
              %span.free-text
                = t("user.home.messages.referrals_banner.extra_weeks_for_free").html_safe
                %i.fas.fa-arrow-right.fs-15
    - else
      = link_to dashboards_ondemand_path do
        - exam = gmat_exam? ? "gmat" : "ea"
        = image_tag asset_path("home/#{exam}_try_on_ondemand_banner.png"), class: "try-on-ondemand-banner"
    - unless current_user.first_time_free_account?
      .notification-message-list
    - sorted_lesson_feedback_messages(messages).each do |comment|
      - lesson_feedback = comment.lesson_feedback
      - unread_count = lesson_feedback.present? ? unread_message_count(lesson_feedback, messages) : nil
      = render "users/home/<USER>", message: comment, lesson_feedback: lesson_feedback, unread_count: unread_count, is_notification_modal: false

  - classes = []
  - classes << "d-none" if messages.any?
  .no-messages{ class: classes }
    .empty-state
      = image_tag(asset_path("layout/controllers/users/home/<USER>"))
      %h5= t("user.home.messages.no_messages.title")
      %p= t("user.home.messages.no_messages.description")
