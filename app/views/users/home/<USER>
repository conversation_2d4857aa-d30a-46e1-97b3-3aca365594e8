:ruby
  data_attributes = {
    mark_as_read_url: mark_as_read_messages_path,
    message_id: lesson_feedback.messages.last&.id || ""
  }
.modal.feedback-conversation-modal{ id: "lesson-feedback-#{lesson_feedback.id}-modal", data: data_attributes }
  .modal-dialog.modal-dialog-centered.modal-lg
    .modal-content
      .modal-header.border-bottom-1
        %h5.modal-title= "Message Detail"
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
      .lesson-info-container.border-bottom-1
        - lesson = lesson_feedback.lesson
        %span.from= "FROM:"
        %span.chapter= lesson.chapter.name
        %span.lesson= link_to lesson.name, lesson_path(lesson), target: "_blank"
      .modal-body.chat-box
        #conversation
          = render "users/home/<USER>", lesson_feedback: lesson_feedback
        .footer.mt-4
          = form_for Message.new, html: { id: "send-message", class: "show-overlay" }, remote: true, method: :post do |f|
            = f.hidden_field :lesson_feedback_id, value: lesson_feedback.id
            = f.text_area :content, class: "form-control", required: true, rows: 1, placeholder: "Type message here..."
            = button_tag type: "submit", id: "admin-respond-reply", class: "btn btn-primary-fully send-reply-button" do
              %span= "SEND"
