:ruby
  classes = ["notification-#{message.id}"]
  classes << "active" unless message.read?
  data_attributes = lesson_feedback.present? ? { bs_toggle: "modal", bs_target: "#lesson-feedback-#{message.lesson_feedback_id}-modal" } : nil
.message{ class: classes }
  - message_content = message.build_recipient_name(current_user)
  .text{ data: data_attributes }
    %span
      .date{ class: (message.read_at.nil? && is_notification_modal) ? "active" : nil }
        = message.created_at.strftime("%b %-d, %Y").upcase
        - if lesson_feedback.present?
          %span.border-date
            %i.far.fa-comments
            - if unread_count != 0
              %span.unread-count
                = unread_count

    .detail
      = truncate_html(message_content, length: (is_notification_modal == true ? 200 : 90)).html_safe
      - data_attributes = lesson_feedback.present? ? data_attributes : { bs_toggle: "modal", bs_target: "#message-#{message.id}-modal" }
      = link_to t("user.home.messages.view_more"), "#", data: data_attributes, class: (is_notification_modal ? "view-more-message-modal" : nil)

  .actions
    = link_to message_path(message, lesson_feedback_id: lesson_feedback&.id), method: :delete, remote: true, data: { confirm: t("user.actions.confirm_element_deletion", element: "message"), aria: { label: "Delete Message" } } do
      %i.fal.fa-trash
      %span.visually-hidden Delete message
