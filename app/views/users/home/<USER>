.message-list
  - unless current_user.first_time_free_account?
    .referrals-banner-container#referrals-banner-container
      = link_to referrals_path do
        .referrals-banner
          .referrals-program-btn-container
  - sorted_lesson_feedback_messages(messages).each do |comment|
    - lesson_feedback = comment.lesson_feedback
    - unread_count = lesson_feedback.present? ? unread_message_count(lesson_feedback, messages) : nil
    = render "users/home/<USER>", message: comment, lesson_feedback: lesson_feedback, unread_count: unread_count, is_notification_modal: true

- classes = []
- classes << "d-none" if messages.any?
.no-messages{ class: classes }
  .empty-state
    = image_tag(asset_path("layout/controllers/users/home/<USER>"))
    %h5= t("user.home.messages.no_messages.title")
    %p= t("user.home.messages.no_messages.description")
