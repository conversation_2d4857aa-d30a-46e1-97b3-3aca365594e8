- lesson_feedback.messages.order(:created_at).each do |message|
  - sender = message.sender
  .message-group{ class: ("message-sender" unless sender.admin?) }
    - if sender.admin?
      .admin-img-container= image_tag "admin/chat_icon.svg", width: "42px", height: "42px"
    - else
      .img-container
        .sender-letters= sender.letters_for_avatar
    .message-container
      .message-content
        - message_content = message.build_recipient_name(current_user)
        = message_content.html_safe
      .message-date{ class: ("custom-margin-top" unless sender.admin?) }
        = message.created_at.strftime("%b. %d, %Y, %H:%M")

