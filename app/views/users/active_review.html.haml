%section.page-header-light.header-type-2.header-primary
  .container
    .row
      .col-12
        .header-inner
          .title-box
            = link_to resources_path, class: "back-btn-chevron show-overlay" do
              %i.far.fa-chevron-left.text-dark
              %span.visually-hidden Back
            .title-and-subtile-wrapper.d-flex.flex-column.gap-4
              %h1.header-title= image_tag(asset_path("layout/controllers/users/active_review_logo.svg"), alt: "Active Review", width: "198", height: "39", class: "active-review-logo")
              .subtitle.text-grey-2.fs-16.fw-normal.d-none.d-sm-block.pe-2= t("user.dashboard.active_review.subtitle").html_safe
              .subtitle-accordion.d-block.d-sm-none
                %a.learn-more-link.text-blue-3.fw-semibold.ms-2{ href: "#", data: { bs_toggle: "collapse", bs_target: "#active-review-subtitle-collapse", aria_expanded: "false", aria_controls: "active-review-subtitle-collapse" } }
                  %span.learn-more-text= t("user.dashboard.active_review.learn_more")
                  %i.far.fa-chevron-down.ms-1.learn-more-icon
                  %span.show-less-text= t("user.dashboard.active_review.show_less")
                  %i.far.fa-chevron-up.ms-1.show-less-icon
                .collapse.mt-3#active-review-subtitle-collapse
                  .subtitle-content.text-grey-2.fs-16.fw-normal= t("user.dashboard.active_review.subtitle").html_safe
          .select-section-tab-group.d-md-none.nav
            - current_user.active_sections.each do |section|
              = button_tag section_label_name(section), type: "button", class: "bg-transparent select-section-tab px-3 py-2 section-content section-color-1-active section-color-2-hover #{section} #{'active' if current_section == section}", data: { bs_toggle: "tab", bs_target: "#section-information-#{section}" }
%section.bg-light-1.pt-4.pt-md-5.pb-5.flex-grow-1#active-review
  .container
    .row.tab-content
      - sections = current_user.active_sections.reject { |section| %w(awa bwa).include?(section) }
      - sections = sections.reject { |section| %w(ir).include?(section) } unless ea_exam?


      - sections.each_with_index do |section, index|
        - subsections = Chapter.subsections_in(section, current_user)
        .section-content.tabs-visible-from-md.tab-pane.fade.show{ id: "section-information-#{section}", class: "#{(current_user.active_sections.count > 1) ? 'col-md-6' : 'col'} #{current_user.active_sections.include?('bwa') ? 'col-xl-6' : 'col-xl'} #{section} #{'active' if index.zero?}" }
          .row.mb-20
            .col-auto.d-none.d-md-block.pe-0
              .d-inline-flex.section-icon.section-bg-color-1.me-1= section_label_acronym(section)
              - if section_label_name(section) == "reading & writing"
                %h2.d-inline.section-color-1.d-xl-inline.d-none.mb-md-0= titleize_section_label_name(section)
                %h2.d-inline.section-color-1.d-xl-none.mb-md-0= titleize_section_label_name(section, true)
              - else
                %h2.d-inline.section-color-1.mb-md-0{ class: ("subsection-separator" if subsections.any?) }= titleize_section_label_name(section)
            - if subsections.any?
              - active_subsection ||= params[:subsection] || subsections.first
              .col-auto.px-md-1.dropdown-container{ class: ("restricted-dropdown" if @ir_chapter.present? || @awa_chapter.present? || current_user.active_sections.count > 2) }
                .px-0
                  = render "components/subsection_dropdown_filter", subsections: subsections, active_subsection: active_subsection, object: nil, button_with_section_color: true, accronim_on_mobile: true
            - if Chapter.in_track(current_user, section).count > 1
              .col-auto.ms-auto.ps-0
                = button_to download_active_review_zip_all_chapters_path(current_user.track, section: section, format: :zip), form_class: "", class: "download-all-button section-color-1 section-color-2-hover text-uppercase fs-13 bg-transparent border-0 px-0" do
                  download all
                  %i.far.fa-arrow-to-bottom.mx-2
          .row
            .col
              .card{ class: "content-#{section} #{'content-subsection' if subsections.any?}" }
                - if subsections.any?
                  - subsections.each do |subsection|
                    - chapters = Chapter.in_track(current_user, section, subsection).includes(:lesson_tracks).ordered
                    %ol.list-unstyled.filterable.mb-0.flex-shrink-0{ class: ("hidden" unless Chapter.subsections_in(section, current_user).first == subsection), data: { filter: subsection } }
                      = render "users/active_review/active_review_download_chapters_rows", chapters: chapters, user_track: current_user.track
                - else
                  - chapters = Chapter.in_track(current_user, section).where("name NOT ILIKE ?", "%strategy%").includes(:lesson_tracks).ordered
                  %ol.list-unstyled.mb-0.flex-shrink-0
                    = render "users/active_review/active_review_download_chapters_rows", chapters: chapters, user_track: current_user.track

      - if !ea_exam? and (@ir_chapter.present? or @awa_chapter.present?) and (current_user.for_legacy_course || !gmat_exam?)
        - { ir: "Integrated Reasoning", awa: "Analytic Writing Assessment" }.each do |accronym, name|
          - next if accronym.to_s == "ir" && !gmat_exam?

          - if instance_variable_get("@#{accronym}_chapter").present?
            .col.section-content.tabs-visible-from-md.tab-pane.fade.show{ class: "#{(current_user.active_sections.count > 1) ? 'd-xl-none' : 'd-md-none'} #{accronym} #{(@awa_chapter.present? && @ir_chapter.present?) ? 'col-md-6' : 'col-md-12 col-xl'}", id: "section-information-#{accronym}" }
              .row.mb-20.d-none.d-md-block
                .col.d-block.pe-0
                  .d-inline-flex.section-icon.section-bg-color-1.me-1= section_label_acronym(accronym.to_s)
                  %h2.d-inline.section-color-1.mb-md-0= name.titleize
              .row
                .col
                  .card.content-custom
                    %ol.list-unstyled.mb-0
                      = render "users/active_review/active_review_download_chapters_rows", chapters: [instance_variable_get("@#{accronym}_chapter")], user_track: current_user.track

        .col.d-none.section-content{ class: (current_user.active_sections.count > 1) ? "d-xl-block" : "d-md-block" }
          - { ir: "Integrated Reasoning", awa: "Analytic Writing Assessment" }.each do |accronym, name|
            - next if accronym.to_s == "ir" && !gmat_exam?

            - if instance_variable_get("@#{accronym}_chapter").present?
              .row.section-content.mb-20{ class: accronym }
                .col
                  .row.mb-20
                    .col
                      .d-inline-flex.section-icon.section-bg-color-1.me-1= section_label_acronym(accronym.to_s)
                      %h2.d-inline.section-color-1.mb-md-0= name.titleize
                  .row
                    .col
                      .card.content-custom
                        %ol.list-unstyled.mb-0.flex-shrink-0
                          = render "users/active_review/active_review_download_chapters_rows", chapters: [instance_variable_get("@#{accronym}_chapter")], user_track: current_user.track
      - if gmat_exam? and @bwa_chapter.present? and !current_user.for_legacy_course
        - { bwa: "Business Writing Assessment" }.each do |accronym, name|

          - if instance_variable_get("@#{accronym}_chapter").present?
            .col.section-content.tabs-visible-from-md.tab-pane.fade.show{ class: "#{(current_user.active_sections.count > 1) ? 'd-xl-none' : 'd-md-none'} #{accronym} #{@bwa_chapter.present? ? 'col-md-6' : 'col-md-12 col-xl'}", id: "section-information-#{accronym}" }
              .row.mb-20.d-none.d-md-block
                .col.d-block.pe-0
                  .d-inline-flex.section-icon.section-bg-color-1.me-1= section_label_acronym(accronym.to_s)
                  %h2.d-inline.section-color-1.mb-md-0= name.titleize
              .row
                .col
                  .card.content-custom
                    %ol.list-unstyled.mb-0
                      = render "users/active_review/active_review_download_chapters_rows", chapters: [instance_variable_get("@#{accronym}_chapter")], user_track: current_user.track

        .col.d-none.section-content{ class: (current_user.active_sections.count > 1) ? "d-xl-block" : "d-md-block" }
          - { bwa: "Business Writing Assessment" }.each do |accronym, name|
            - if instance_variable_get("@#{accronym}_chapter").present?
              .row.section-content.mb-20{ class: accronym }
                .col
                  .row.mb-20
                    .col
                      .d-inline-flex.section-icon.section-bg-color-1.me-1= section_label_acronym(accronym.to_s)
                      %h2.d-inline.section-color-1.mb-md-0= name.titleize
                  .row
                    .col
                      .card.content-custom
                        %ol.list-unstyled.mb-0.flex-shrink-0
                          = render "users/active_review/active_review_download_chapters_rows", chapters: [instance_variable_get("@#{accronym}_chapter")], user_track: current_user.track
