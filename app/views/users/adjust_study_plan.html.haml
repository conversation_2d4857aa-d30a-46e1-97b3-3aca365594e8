%section.page-header-light.header-type-2
  .container
    .row
      .col-12
        .header-inner
          .title-box
            = link_to study_plan_path, class: "back-btn-chevron section-color-2 section-bg-color-5-hover customize-study-plan-back-btn" do
              %i.far.fa-chevron-left
              %span.visually-hidden Back to study plan
            %h2.section-color-2 Adjust #{exam_title} Study Plan

%section.bg-light-1.pt-4.pb-5.flex-grow-1#must-knows-index
  .container
    .bg-white.adjust-plan-form.border
      = form_for current_user, url: customize_study_plan_path, method: :post, html: { id: "customize-study-plan-form", novalidate: true } do |f|
        = f.hidden_field :study_plan_guide_enabled
        = f.hidden_field :show_og_questions
        = f.hidden_field :short_study_plan

        .study-plan{ data: { legacy_course_enabled: current_user.for_legacy_course.to_s } }
          .change-note
            %i.fal.fa-exclamation-circle
            %span= t("user.study_plan.adjust_study_plan.change_note").html_safe

          - if Setting.for("legacy_course_enabled") && gmat_exam?
            = render "users/study_plan/study_plan_settings/gmat_course_setting", f: f, user: current_user
          - else
            .row
              .adjust-plan-content.col-sm-12.col-md-12.col-lg-7.border-right
                - if multiple_sections_available?
                  - current_section = current_user.active_sections.join(",")
                  = hidden_field_tag :study_plan_sections, current_section
                  .mb-2.label-heading
                    = label_tag :study_plan_sections, t("user.study_plan.modals.settings.study_plan_sections.label")
                  .adjust-plan.w-full.mb-4
                    = render "users/study_plan/study_plan_settings/content_section", current_section: current_section
                .mt-0
                  .mb-2.label-heading
                    = f.label :track_id, t("user.dashboard.settings.score.title")
                  .score-bar-container
                    = f.hidden_field :track_id
                    = render "welcome/score_bar", user: current_user

                  %p.mt-3.mb-4.mb-md-0.text-grey-4= t("user.dashboard.settings.score.intro", exam: exam_title).html_safe
              .adjust-plan-test-date.col-sm-12.col-md-12.col-lg-5.pb-0.pb-lg-4.test-dates-column
                .mb-2.label-heading.mobile-border
                  = f.label :test_dates, t("user.dashboard.settings.general.test_dates.title")
                %p.test_date_description
                  = t("user.dashboard.settings.general.test_dates.test_date_description")
                = render "test_dates/form", test_dates: current_user.test_dates, f: f, is_setup_account_screen: false, disable_link: false

          .adjust-accelerated-study-plan.col-sm-12#accelerated-study-plan
            .row
              .col-sm-12
                .mb-2.label-heading.mb-2
                  = f.label :accelerated_study_plan, t("user.study_plan.modals.settings.accelerated_study_plan.label")
              .col-sm-12.col-md-9.col-lg-10.mb-32.mb-lg-0
                .text-grey-2.fw-normal= t("user.study_plan.modals.settings.accelerated_study_plan.description").html_safe
              .col-sm-12.col-md-3.col-lg-2.mb-32.mb-md-0
                .button-wrapper
                  = link_to "ON", "#", class: "update-form-field-value btn #{'active' if current_user.short_study_plan}", data: { form_id: "customize-study-plan-form", field_name: "user[short_study_plan]", value: true }
                  = link_to "OFF", "#", class: "update-form-field-value btn #{'active' unless current_user.short_study_plan}", data: { form_id: "customize-study-plan-form", field_name: "user[short_study_plan]", value: false }
              .col-sm-12.mt-0.mt-lg-32.recommendation-default-content{ class: current_user.for_legacy_course ? "d-none" : "" }
                = t("user.study_plan.modals.settings.accelerated_study_plan.recommendation_content_html", icon_image_path: asset_path("layout/controllers/users/adjust_study_plan/accelerated_study_plan_check_icon.svg"), exam_name: exam_title(for_legacy_course: false)).html_safe
              .col-sm-12.mt-0.mt-lg-32.recommendation-legacy-content{ class: current_user.for_legacy_course ? "" : "d-none" }
                = t("user.study_plan.modals.settings.accelerated_study_plan.recommendation_content_html_for_legacy", icon_image_path: asset_path("layout/controllers/users/adjust_study_plan/accelerated_study_plan_check_icon.svg"), exam_name: exam_title(for_legacy_course: true)).html_safe

          - if show_og_questions_section?
            .adjust-show-og-questions.col-sm-12
              .row
                .col-sm-12
                  .mb-2.label-heading.mb-2
                    = t("user.study_plan.modals.settings.show_og_questions.label")
                .col-sm-12.col-md-9.col-lg-10
                  .show-og-questions-text
                    = t("user.study_plan.modals.settings.show_og_questions.description", exam: EXAM_NAME.upcase).html_safe
                .col-sm-12.col-md-3.col-lg-2
                  .show-og-tasks
                    = link_to "ON", "#", class: "update-form-field-value btn #{'active' if current_user.show_og_questions}", data: { form_id: "customize-study-plan-form", field_name: "user[show_og_questions]", value: true }
                    = link_to "OFF", "#", class: "update-form-field-value btn #{'active' if !current_user.show_og_questions}", data: { form_id: "customize-study-plan-form", field_name: "user[show_og_questions]", value: false }

          .adjust-study-plan-settings.col-sm-12
            = f.fields_for :study_plan_setting, current_user.study_plan_setting do |study_plan_setting_form|
              = study_plan_setting_form.hidden_field :is_calendar_view_enabled
              = study_plan_setting_form.hidden_field :is_study_hours_per_week

              .view-preferences-wrapper
                .mb-2.label-heading
                  = f.label :view_preferences, "preferred study plan"
                %section#view-preferences
                  = render "users/study_plan/study_plan_settings/view_preference", f: study_plan_setting_form

              .date-time-preferences-and-availability-wrapper{ class: study_plan_setting_form.object.is_calendar_view_enabled ? nil : "d-none" }
                .mb-2.label-heading
                  = f.label :not_available, "Select the days you may NOT be available to study"
                .row.m-0
                  .col-lg-7.col-12.p-0.me-0.me-lg-5
                    %section#availability{ data: { exam_dates: current_user.test_dates.pluck(:next_exam_date).map(&:to_s).join(","), excluded_dates: current_user.study_plan_setting.study_dates_to_exclude.map(&:to_s).join(","), exam_name: EXAM_NAME } }
                      = render "users/study_plan/study_plan_settings/calendar/availability", f: study_plan_setting_form
                  .col.p-0.mt-5.mt-lg-0
                    %section#date-time-preferences
                      .hours-and-start-date-wrapper
                        .mb-2.label-heading
                          = f.label :study_time, "START DATE"
                        .position-relative.mb-32
                          = study_plan_setting_form.label :study_start_date, "Study Start Date", class: "visually-hidden"
                          = study_plan_setting_form.text_field :study_start_date, class: "datepicker form-control input-with-icon", data: { value: study_plan_setting_form.object.study_start_date.try(:strftime, "%Y-%m-%d") || Date.current.strftime("%Y-%m-%d") }
                          %i.far.fa-calendar.text-grey-4

                        .mb-2.label-heading
                          = f.label :study_time, "STUDY TIME"
                        .row.m-0.border.border-colory-grey-6.hours-wrapper
                          .col-12.per-week.choice-card{ class: study_plan_setting_form.object.is_study_hours_per_week? ? "active" : "" }
                            .title.d-flex.align-items-center
                              %i
                              %span.text-grey-2.ms-2= t("user.study_plan.modals.study_plan_settings.date_time_preferences.hours_choice_card.per_week.title")

                            .week-hour-input-wrapper
                              = study_plan_setting_form.label :study_hours_per_week, "TOTAL (Eg. 20, 40)", class: "form-label fs-13"
                              = study_plan_setting_form.number_field :study_hours_per_week, class: "form-control", disabled: !study_plan_setting_form.object.is_study_hours_per_week?, min: 0, style: "-moz-appearance: textfield;"

                          .col-12.per-day.choice-card{ class: study_plan_setting_form.object.is_study_hours_per_week? ? "" : "active" }
                            .title.d-flex.align-items-center
                              %i
                              %span.text-grey-2.ms-2= t("user.study_plan.modals.study_plan_settings.date_time_preferences.hours_choice_card.per_day.title")

                            .days-wrapper
                              - days = %w(mon tue wed thu fri sat sun)
                              - days.each_with_index do |day, index|
                                .text-center.day-hour-input-wrapper
                                  = study_plan_setting_form.label :study_hours_per_day, class: "text-uppercase form-label fs-13" do
                                    = day
                                  = study_plan_setting_form.number_field :study_hours_per_day, class: "form-control", disabled: study_plan_setting_form.object.is_study_hours_per_week?, multiple: true, value: study_plan_setting_form.object.study_hours_per_day[index], min: 0, style: "-moz-appearance: textfield;"

            .adjust-plan-mission-reminder.col-sm-12{ class: current_user.study_plan_setting.is_calendar_view_enabled ? "d-none" : nil }
              .row
                .col-sm-12
                  .mb-2.label-heading.mb-2
                    = t("user.study_plan.modals.settings.mission_reminder.label")
                .col-sm-12.col-md-9.col-lg-10
                  .mission-reminder-text
                    If you turn on the mission reminder, we’ll help you by always keeping you on track on what mission you should do next and also alert you when you missed something before passing to the next topic of study.
                .col-sm-12.col-md-3.col-lg-2
                  .mission-reminder
                    = link_to "ON", "#", class: "update-form-field-value btn #{'active' if current_user.study_plan_guide_enabled}", data: { form_id: "customize-study-plan-form", field_name: "user[study_plan_guide_enabled]", value: true }
                    = link_to "OFF", "#", class: "update-form-field-value btn #{'active' if !current_user.study_plan_guide_enabled}", data: { form_id: "customize-study-plan-form", field_name: "user[study_plan_guide_enabled]", value: false }

        .modal.p-0#exit-study-plan-modal
          .modal-dialog.modal-dialog-centered
            .modal-content
              .modal-header.border-bottom-0
                %button.btn-close{ type: "button", data: { bs_dismiss: "modal", bs_target: "#exit-study-plan-modal" } }
                  %span.visually-hidden Close
              .modal-body.text-center
                = hidden_field_tag :link_href_url, nil
                = image_tag(asset_path("layout/controllers/users/adjust_study_plan/exit_modal.svg"))
                %h3 You have unsaved changes
                %p.mb-0 If you leave now without saving, your changes will be lost.

              .modal-footer.justify-content-center.border-top-0.mb-4
                %button.btn.btn-outline-secondary.me-md-3{ type: "button", data: { bs_dismiss: "modal", bs_target: "#exit-study-plan-modal" } }
                  CANCEL
                %button.btn.btn-primary#exit-modal-submit-button{ class: current_user.mission_study_plan_enabled? ? "show-overlay" : nil, type: "submit", form: "customize-study-plan-form", data: { mission_button_text: t("user.study_plan.modals.settings.mission_save_button"), calendar_button_text: t("user.study_plan.modals.settings.calendar_save_button") } }
                  SAVE

      .modal-footer.justify-content-end.border-top-1
        %button.btn.btn-primary#form-submit-button{ class: current_user.mission_study_plan_enabled? ? "show-overlay" : nil, type: "submit", form: "customize-study-plan-form", data: { mission_button_text: t("user.study_plan.modals.settings.mission_save_button"), calendar_button_text: t("user.study_plan.modals.settings.calendar_save_button") } }
          = t("user.study_plan.modals.settings.#{current_user.calendar_study_plan_enabled? ? 'calendar' : 'mission'}_save_button")

.modal#calendar-regenerate-confirmation-modal
  .modal-dialog.modal-dialog-centered
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body.text-center
        = image_tag(asset_path("layout/controllers/users/study_plan/calendar_view/calendar_regenerate_confirmation_icon.svg"))
        %h3= t("user.study_plan.modals.calendar_regenerate_confirmation.title")
        %p.mb-0= t("user.study_plan.modals.calendar_regenerate_confirmation.description")
      .modal-footer.justify-content-center.border-top-0.pb-4.mb-3
        %button.btn.btn-outline-secondary.me-md-3{ type: "button", data: { bs_dismiss: "modal" } }
          = t("user.study_plan.modals.calendar_regenerate_confirmation.actions.cancel")
        %button.btn.btn-primary.confirm.show-overlay{ type: "button" }
          = t("user.study_plan.modals.calendar_regenerate_confirmation.actions.regenerate")
