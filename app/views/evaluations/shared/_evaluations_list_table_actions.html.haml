.actions-links.d-none.d-xl-flex
  - retake_modal_id = evaluation.is_a?(AdaptiveEvaluation) ? "#retake-test-adaptive" : "#retake-test"

  - if attempt and attempt.finish?
    = link_to evaluation_attempt_path(attempt, section: current_section), { title: t("user.evaluations.list.actions.view_results"), data: { bs_toggle: "tooltip", bs_placement: "top" } } do
      %i.fal.fa-file-alt.section-color-3
      %span.visually-hidden View Results

    = link_to "#", data: { bs_toggle: "modal", bs_target: retake_modal_id, evaluation_url: evaluation_start_path(evaluation, evaluation_restart: true) } do
      %i.fal.fa-redo.section-color-3{ title: t("user.evaluations.list.actions.retake_test"), data: { bs_toggle: "tooltip", bs_placement: "top" } }
      %span.visually-hidden Retake test

  - elsif attempt.present?
    = link_to evaluation_continue_path(attempt), { class: "show-overlay", title: t("user.evaluations.list.actions.resume"), data: { bs_toggle: "tooltip", bs_placement: "top" } } do
      %i.fal.fa-play.section-color-3
      %span.visually-hidden Resume test

    = link_to "#", data: { bs_toggle: "modal", bs_target: retake_modal_id, evaluation_url: evaluation_start_path(evaluation, evaluation_restart: true) } do
      %i.fal.fa-redo.section-color-3{ title: t("user.evaluations.list.actions.retake_test"), data: { bs_toggle: "tooltip", bs_placement: "top" } }
      %span.visually-hidden Restart test

  - elsif evaluation.is_diagnostic?
    = form_tag create_diagnostic_test_path, method: :post, id: "new-diagnostic-form" do
      = hidden_field_tag :test_order, current_user.track.diagnostic_sections.join(",")
      = hidden_field_tag :time_per_question

    = link_to "#", class: "section-color-3", data: { bs_toggle: "modal", bs_target: "#diagnostic-info-modal-to-continue", form_id: "new-diagnostic-form" } do
      %i.fal.fa-play.section-color-3{ title: t("user.evaluations.list.actions.start"), data: { bs_toggle: "tooltip", bs_placement: "top" } }
      %span.visually-hidden Continue diagnostic test
    = render "evaluations/shared/diagnostic_info_modal_to_continue", facade: facade

  - elsif evaluation.adaptive_evaluation?
    = form_tag create_diagnostic_test_path, method: :post, id: "new-diagnostic-form" do
      = hidden_field_tag :test_order, current_user.track.diagnostic_sections.join(",")
      = hidden_field_tag :time_per_question

    = link_to "#", class: "section-color-3", data: { bs_toggle: "modal", bs_target: "#diagnostic-info-modal-to-continue", form_id: "new-diagnostic-form" } do
      %i.fal.fa-play.section-color-3{ title: t("user.evaluations.list.actions.start"), data: { bs_toggle: "tooltip", bs_placement: "top" } }
      %span.visually-hidden Continue diagnostic test
    = render "evaluations/shared/diagnostic_info_modal_to_continue", facade: facade

  - elsif evaluation.persisted?
    = link_to evaluation_start_path(evaluation), { data: { bs_toggle: "modal", bs_target: "#select-time-per-question" } } do
      %i.fal.fa-play.section-color-3{ title: t("user.evaluations.list.actions.start"), data: { bs_toggle: "tooltip", bs_placement: "top" } }
      %span.visually-hidden Start evaluation test
