.modal#select-time-per-question-diagnostic
  .modal-dialog.modal-dialog-centered.modal-sm
    .modal-content
      .modal-header.border-bottom-0
        %button.btn-close{ type: "button", data: { bs_dismiss: "modal" } }
          %span.visually-hidden Close
      .modal-body.text-center
        %p= image_tag(asset_path("layout/time.svg"))

        .default
          %h5= t("user.evaluations.diagnostics.start_modal.title")
          = select_tag "time_per_question", options_for_select(evaluation_time_per_question_options(true), Evaluation.exam_time_per_question), class: "select-time form-select", aria: { label: "Time per Question" }

          - if diagnostic_sections.count > 1 and EXAM_NAME != "ea"
            .mt-4
              :ruby
                default_options = []
                diagnostic_sections.permutation.each do |permutation|
                  options_array = permutation.map { |section| titleize_section_label_name(section) }
                  default_options << [options_array.join(" + "), permutation.join(",")]
                end

                order = { "quant" => 1, "verbal" => 2, "di" => 3 }
                sorted_options = default_options.sort_by { |option| option[1].split(",").map { |item| order[item] } }

              = select_tag :test_order, options_for_select(sorted_options), class: "form-select text-capitalize", aria: { label: "Test Order" }

      .modal-footer.justify-content-center.border-top-0
        .default
          = link_to t("user.evaluations.diagnostics.start_modal.start_test"), "#", { class: "btn btn-primary", id: "time-per-question-diagnostic-modal-button", disable_with: "Please wait..." }
