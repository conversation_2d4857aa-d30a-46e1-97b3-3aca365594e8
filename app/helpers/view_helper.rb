module ViewHelper
  include OndemandResourcesHelper

  def inline_background_size_percentage(percentage)
    "background-size: #{percentage}% 100%;"
  end

  def problem_flagged?(problem, flagged_problems_ids)
    flagged_problems_ids.present? and flagged_problems_ids.include?(problem.id)
  end

  def difficulty_level_by_letter(letter)
    case letter
    when "E" then t("user.evaluations.difficulty_level.easy")
    when "M" then t("user.evaluations.difficulty_level.medium")
    when "H" then t("user.evaluations.difficulty_level.hard")
    when "C" then t("user.evaluations.difficulty_level.challenge")
    end
  end

  def chapter_completed_css_class(percentage)
    percentage == 100 ? "done" : ""
  end

  def today_date_in_pst
    Time.now.in_time_zone("Pacific Time (US & Canada)").to_date
  end

  def editor?
    current_user.editor?
  end

  def problem_editor?
    current_user.problem_editor?
  end

  def format_date(date)
    date.strftime("%Y/%-m/%-d")
  end

  def format_date_abbreviated_month(date)
    date.strftime("%b %d, %Y")
  end

  def generate_editor_problem_content(problem)
    ProblemContentPresenter.new(problem).to_html(:editor, current_user.problem_editor?)
  end

  def replace_img_for_asset(content, content_assets)
    return content.html_safe if content.to_s.include?("ckeditor") && content_assets.blank?

    (content || "").gsub(/\[img=(.*?)\]/) do
      asset = ContentAsset.asset_by_name(content_assets, Regexp.last_match[1])
      if asset.nil?
        "[[BAD IMAGE REF]]"
      else
        image_tag(asset.url)
      end
    end
  end

  def get_tab_names(content)
    (content || "").scan(/<p>\[\[tab\]\] (.*)<\/p>/i)
  end

  def chapter_topics_flagged(lesson, completed_topics, review_topics)
    ChapterTopicsFlaggedListPresenter.new(
      lesson,
      completed_topics,
      review_topics,
      current_user
    )
  end

  def generate_content(lesson)
    LessonContentPresenter.new(lesson, current_user).to_html
  end

  def generate_problem_content(problem, problem_attempt = nil, navigator = nil, evaluation_attempt = nil)
    ProblemContentPresenter.new(problem, problem_attempt, navigator, evaluation_attempt).to_html
  end

  def problem_avg_time_text(problem)
    avg_time = problem.avg_time || 0
    avg_time.positive? ? Time.at(avg_time).strftime("%M:%S") : "N/A"
  end

  def problem_percentage_correct_text(problem)
    problem.percentage_correct.present? ? "#{problem.percentage_correct}%" : "N/A"
  end

  def math_js_tag
    # OLD MathJax Tags
    # javascript_include_tag('https://cdn.mathjax.org/mathjax/2.4-latest/MathJax.js' + '?config=TeX-AMS-MML_HTMLorMML&delayStartupUntil=configured')
    # javascript_include_tag('https://cdn.mathjax.org/mathjax/2.4-latest/MathJax.js' + '?config=TeX-AMS-MML_HTMLorMML')
    # javascript_include_tag('https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.4.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML')
    javascript_include_tag(asset_path("/vendor/javascript/mathjax-2.4/MathJax.js?config=TeX-AMS-MML_HTMLorMML"),
      async: true)
  end

  def evaluation_time_per_question_options(per_question = false)
    UserEvaluation::SECONDS.each_with_index.map do |time, index|
      unit = t("user.custom_test.settings.time_per_question_unit").pluralize(index + 1)
      ["#{pretty_seconds(time)} #{unit}#{' per question' if per_question}", time]
    end
  end

  def class_for_answer(value_a, value_b = nil)
    if value_b
      value_a == value_b ? "correct" : "incorrect"
    else
      value_a ? "correct" : "incorrect"
    end
  end

  def pretty_seconds(seconds)
    return "N/A" if seconds.nil?

    minutes = (seconds / 60).floor.to_s.rjust(1, "0")
    seconds = (seconds % 60).to_s.rjust(2, "0")
    "#{minutes}:#{seconds}"
  end

  def pretty_hours_and_minutes(seconds)
    return "N/A" if seconds.nil?

    hours = seconds / 3600
    minutes = (seconds % 3600) / 60
    "#{hours} hr#{'s' if hours > 1} #{minutes} min"
  end

  def pretty_minutes_and_hours_or_seconds(seconds)
    return "N/A" if seconds.nil?

    minutes = (seconds % 3600) / 60

    if seconds >= 3600
      hours = seconds / 3600
      "#{hours} h #{minutes} min"
    else
      seconds -= (minutes * 60)
      "#{minutes} min #{seconds} s"
    end
  end

  def pretty_percent(value)
    return "N/A" if value.blank?
    return "0 %" if value.to_i.zero?

    "#{value} %"
  end

  def class_for_percentage_correct(percentage_correct = 0)
    if percentage_correct.nil?
      ""
    elsif percentage_correct < 33
      "hard"
    elsif percentage_correct < 66
      "medium"
    else
      "easy"
    end
  end

  def ontarget_dashboard_accuracy_class(accuracy)
    if accuracy > 80
      "high"
    elsif accuracy > 70
      "medium-high"
    elsif accuracy > 60
      "medium-low"
    else
      "low"
    end
  end

  def smart_lessons_path(options)
    from = options.delete(:from)

    if from == "list"
      lessons_list_path options.merge(section: (options[:section] || current_section))
    else
      lessons_path options.merge(section: (options[:section] || current_section))
    end
  end

  def class_for_avg_time_result(avg_time, max_time)
    avg_time <= max_time ? "green" : "red"
  end

  def class_for_improvement(improvement)
    improvement >= 100 ? "icn-arrow-3-up" : "icn-arrow-3-down"
  end

  def mobile?
    request.user_agent =~ /Mobile|webOS/
  end

  def truncate_and_remove_media(content, options = {})
    options[:replacement] ||= "<p>click to see full content</p>"

    new_content = truncate_html(content.gsub(/(&nbsp;)+/, " "), length: options[:length]).gsub(
      %r{<table.*?>.*?</table>|<video.*?>.*?</video>|<iframe.*?>.*?</iframe>|<blockquote.*?>.*?</blockquote>}im,
      options[:replacement]
    )

    # This extra step is to fix the MathML tags
    original_mml_tags = content.scan(/(<span class="math-tex-or-mml".*?>.*?<\/span>)/im).flatten

    new_content.gsub!(/(<span class="math-tex-or-mml".*?>.*?<\/span>)/i).with_index do |_data, index|
      original_mml_tags[index]
    end

    new_content.html_safe
  end

  def section_color_for(section)
    section_colors = {
      "quant" => "#297EB3",
      "verbal" => "#A377B5",
      "ir" => "#B18B72",
      "di" => "#1EA1A1"
    }

    section_colors[section]
  end

  def section_color_by_number_for(section, number)
    colors = {
      "quant" => %w(#1D5B81 #297EB3 #ACD3EC #ACD3EC #CDE5F4),
      "verbal" => %w(#704681 #A377B5 #CAB3D6 #E8DEED #F8F5FA),
      "di" => %w(#0F5C61 #1EA1A1 #1FBDC7 #CDF4E6 #EFF7F8),
      "ir" => %w(#755641 #B18B72 #D7AE92 #E5DBD4 #F3EFEC)
    }

    colors[section][number - 1] || colors[section][4]
  end

  def string_sanitizer(string_to_sanitize, regular_expression)
    string_to_sanitize&.gsub(regular_expression, "")
  end

  def subsection_span_with_color_coding(subsection, study_module = nil)
    label = subsection.name || subsection.chapter&.name || subsection.section.titleize
    label = study_module.subtitle if study_module.present? and subsection.section == "bwa"

    label = "#{label} for GMAT" if label == "Coordinate Geometry" && gmat_focus_edition?

    content_tag :span, class: [subsection.section, "section-content"] do
      safe_join(
        [
          content_tag(:span, subsection_code(subsection), class: "badge section-bg-color-4 section-color-1"),
          content_tag(:span, label, class: "section-color-1")
        ]
      )
    end
  end

  def subsection_code(subsection)
    chapter = subsection.chapter

    if %w(ir awa di bwa).include?(subsection.section)
      subsection.section.upcase
    elsif chapter&.subsection.present?
      chapter.subsection.split.map { |s| s[0] }.join
    else
      section_label_name(subsection.section).to_s.split.map { |s| s[0] }.join
    end
  end

  def options_for_custom_test_creation_filter(section)
    options_for_custom_test_creation_filter = []

    Evaluation.problems_filters[section.to_sym].each do |filter|
      title = I18n.t("user.custom_test.settings.questions.modal.filters.#{filter}_title")
      options_for_custom_test_creation_filter << [title, filter]
    end

    options_for_custom_test_creation_filter
  end

  def completion_and_class_for_category_slider(category_name, category_total_score)
    case category_name
    when "Accuracy and Careless Errors"
      case category_total_score
      when -Float::INFINITY..0
        [100, "green"]
      when 1..4
        [75, "yellow"]
      when 5..8
        [50, "orange"]
      else
        [10, "red"]
      end
    when "Time Management"
      case category_total_score
      when -Float::INFINITY..0
        [100, "green"]
      when 1..10
        [75, "yellow"]
      when 11..16
        [50, "orange"]
      else
        [10, "red"]
      end
    when "Health and Wellness"
      case category_total_score
      when -Float::INFINITY..0
        [100, "green"]
      when 1..3
        [75, "yellow"]
      when 4..6
        [50, "orange"]
      else
        [10, "red"]
      end
    when "Test Anxiety"
      case category_total_score
      when -Float::INFINITY..0
        [100, "green"]
      when 1..4
        [75, "yellow"]
      when 5..8
        [50, "orange"]
      else
        [10, "red"]
      end
    when "Confidence"
      case category_total_score
      when -Float::INFINITY..0
        [100, "green"]
      when 1..4
        [75, "yellow"]
      when 5..8
        [50, "orange"]
      else
        [10, "red"]
      end
    when "Best Practices"
      case category_total_score
      when -Float::INFINITY..0
        [100, "green"]
      when 1..4
        [75, "yellow"]
      when 5..8
        [50, "orange"]
      else
        [10, "red"]
      end
    else
      [2, "red"]
    end
  end

  def subsections_per_section(section)
    subsections = Chapter.subsections_in(section.to_s, current_user)
    subsections.any? ? subsections : [nil] # Allows one iteration
  end

  def authy_request_sms_link(opts = {})
    title = opts.delete(:title) { I18n.t("request_sms", scope: "devise").html_safe }

    opts = {
      id: "authy-request-sms-link",
      method: :post,
      remote: true
    }.merge(opts)

    link_to(title, url_for([resource_name, :request_sms]), opts)
  end

  def section_label_name(section, acronym = false)
    return "r & w" if acronym and sat_exam? and section&.downcase == "verbal"

    Chapter.section_label_name(section)
  end

  def titleize_section_label_name(section, acronym = false)
    return unless section

    return "R & W" if acronym and sat_exam? and section&.downcase == "verbal"
    return "DI" if acronym and gmat_exam? and section&.downcase == "di"
    return "Data Insights" if gmat_exam? and section&.downcase == "di"

    Chapter.titleize_section_label_name(section)
  end

  def student_avatar_icon(student)
    icon = t("user.static_pages.partner_organizations.shared.testimonials.students.student_#{student}.icon")
    return asset_url("layout/controllers/content_pages/forte/#{icon}") if [15, 16, 17].include?(student)

    "https://targettestprep.s3.amazonaws.com/static_pages/partner_organizations/mlt/#{icon}"
  end

  def show_range_for_org(org_name)
    case org_name
    when "MLT"
      (1..6)
    when "FORTÉ"
      [15, 16, 17, 12, 8, 3]
    else
      [4, 5, 6]
    end
  end

  def gmat_legacy_score_format
    [
      %w(Overall 770 200 800 t overall 10),
      %w(Quant 51 0 51 q quant 1),
      %w(Verbal 49 0 51 v verbal 1),
      %w(IR 6 1 8 ir ir 1)
    ]
  end

  def score_format(user_for_legacy_course = false)
    if Setting.for("legacy_course_enabled")
      user_for_legacy_course ? gmat_legacy_score_format : ExamScore::SCORES_FORMAT
    else
      ExamScore::SCORES_FORMAT
    end
  end

  def exam_title(for_legacy_course: current_user&.for_legacy_course)
    EXAM_NAME.upcase
  end

  def exam_sections(for_legacy_course: false)
    case EXAM_NAME
    when "gmat"
      for_legacy_course ? "Q, V, IR, AWA" : "Q, V, DI, BWA"
    when "sat"
      "M, W"
    else
      "Q, V"
    end
  end

  def active_sections_for_custom_test
    current_user.active_sections.select { |section| Problem.in_track(current_user, section).present? }
  end

  def section_label_acronym(section)
    return "" if section.blank?
    return section.upcase if section.length == 2

    section_label_name(section).split.map(&:first).join[0..1].upcase
  end

  def display_calculator?(exercise = nil)
    return false unless Setting.for("calculator_enabled")
    return true unless %w(gmat ea).include?(EXAM_NAME)

    %w(di ir).include?(exercise.dynamic_section(current_user&.for_legacy_course))
  end

  def matriculation_years_list
    current_year = Time.zone.now.year
    years_count = 4
    (1..years_count).map { |i| (current_year + i).to_s }
  end

  def admissions_free_consultation_data
    {
      consultants: ["No Preference", "Joanna Graham", "Laura Nelson", "Emily Currin", "Emily Lammers"],
      type_of_program_options: ["Full-Time MBA", "Part-Time MBA", "EMBA", "One-Year/Accelerated MBA", "Specialized Masters", "Other"],
      application_date_options: matriculation_years_list,
      referred_by_options: ["Search (Google, Bing, etc)", "Word of Mouth (from a friend or colleague)", "Social Media", "Ranking/Review sites", "GMAT Club", "Reddit", "Other"],
      gmat_score: ["Up to 560", "570 - 630", "640 - 690", "700 - 730", "740+"],
      gre_score: ["Up to 300", "301 - 309", "310 - 321", "322 - 331", "332+"],
      ea_score: ["Up to 134", "135 - 144", "145 - 154", "155 - 164",  "165+"]
    }
  end

  def check_trial_or_expire_user
    current_user.on_trial? or (!current_user.on_trial? and !current_user.active? and !current_user.onlineclass_subscription?) or (current_user.was_trial? and (!current_user.active? and !current_user.onlineclass_subscription?))
  end

  def set_exam_background_image
    if only_gmat_or_ea
      Setting.for("ondemand_enabled") ? "background_image_for_gmat_ondemand_plan_list" : "background_image_for_gmat"
    elsif sat_exam?
      "background_image_for_sat"
    elsif gre_exam?
      "background_image_for_gre"
    else
      "background_image_for_ea"
    end
  end

  def set_option_list
    if gmat_exam? or gre_exam?
      (1..5)
    else
      (1..4)
    end
  end

  def benefits_option_list
    if gre_exam?
      ("a".."l")
    elsif gmat_exam?
      ("a".."k")
    else
      ("a".."j")
    end
  end

  def gmat_focus_edition?
    gmat_exam? && current_user && !current_user.for_legacy_course
  end

  def show_billing_recurrence_info?(plan_code)
    Setting.for("recurly_monthly_plan_code") == plan_code or RecurlyProxy::TRIAL_PLAN_CODES.include?(plan_code)
  end

  def only_gmat_or_gre
    %w(gmat gre).include? EXAM_NAME
  end

  def only_gmat_or_ea
    %w(gmat ea).include? EXAM_NAME
  end

  def user_has_ondemand_access?
    Setting.for("ondemand_enabled") && !current_user.for_legacy_course
  end

  def only_gmat_or_gre_or_ea
    %w(gmat gre ea).include? EXAM_NAME
  end

  def ondemand_enabled_for_gmat?
    gmat_exam? and Setting.for("ondemand_enabled")
  end

  def ondemand_enabled_for_gmat_or_ea?
    %w(gmat ea).include?(EXAM_NAME) and Setting.for("ondemand_enabled")
  end

  def render_marketing_modal(modal_page)
    # fetch active admin marketing modal
    admin_marketing_modal = MarketingModal.for(today_date_in_pst).find_by(modal_page: modal_page)

    # Render and return admin marketing modal
    if admin_marketing_modal.present? and should_show_admin_marketing_modal?(admin_marketing_modal)
      Flag.create(user: current_user, flaggable: admin_marketing_modal, label: "marketing_modal_acknowledged") if current_user

      return render partial: "components/#{admin_marketing_modal.image_only? ? 'image_only_' : ''}marketing_modal", locals: { marketing_modal: admin_marketing_modal }
    end

    # Check if user-specific marketing modal data is available and if the page is "homescreen"
    user_modal_data = marketing_modal_data_for(current_user) if current_user
    return unless user_modal_data and modal_page == "homescreen"

    render "users/home/<USER>", desktop_image_url: user_modal_data[:desktop_image_url], mobile_image_url: user_modal_data[:mobile_image_url], path: user_modal_data[:path], flag_label: user_modal_data[:flag_label]
  end

  def marketing_modal_data_for(user)
    days_since_signup = (Date.current - user.created_at.to_date).to_i

    if days_since_signup == 1 and user.on_trial? and gmat_exam? and !Flag.flagged?(user, user, "signup_day_1_modal_acknowledged")
      {
        desktop_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_1_desk.webp"),
        mobile_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_1_mob.webp"),
        path: "/subscriptions/new",
        flag_label: "signup_day_1_modal_acknowledged"
      }
    elsif days_since_signup == 2 and user.on_trial? and gmat_exam? and !Flag.flagged?(user, user, "signup_day_2_modal_acknowledged")
      {
        desktop_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_2_desk.webp"),
        mobile_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_2_mob.webp"),
        path: "/subscriptions/new",
        flag_label: "signup_day_2_modal_acknowledged"
      }
    elsif days_since_signup == 3 and gmat_exam? and !Flag.flagged?(user, user, "signup_day_3_modal_acknowledged")
      {
        desktop_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_3_desk.webp"),
        mobile_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_3_mob.png"),
        path: "/online_classes",
        flag_label: "signup_day_3_modal_acknowledged"
      }
    elsif days_since_signup == 4 and gmat_exam? and !Flag.flagged?(user, user, "signup_day_4_modal_acknowledged")
      {
        desktop_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_4_desk.png"),
        mobile_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_4_mob.png"),
        path: "/online_tutoring",
        flag_label: "signup_day_4_modal_acknowledged"
      }
    elsif days_since_signup == 5 and gmat_exam? and !Flag.flagged?(user, user, "signup_day_5_modal_acknowledged") and !user.full_ondemand_access
      {
        desktop_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_5_desk.webp"),
        mobile_image_url: asset_path("home/marketing_modals/gmat_marketing_modal_5_mob.jpg"),
        path: "/home/<USER>",
        flag_label: "signup_day_5_modal_acknowledged"
      }
    elsif days_since_signup == 14 and %w(gmat gre).include?(EXAM_NAME) and !Flag.flagged?(user, user, "tutoring_modal_for_14_days")
      {
        desktop_image_url: asset_path("home/marketing_modals/#{EXAM_NAME}_tutoring_modal_day_14_desktop.webp"),
        mobile_image_url: asset_path("home/marketing_modals/#{EXAM_NAME}_tutoring_modal_day_14_mobile.webp"),
        path: gmat_exam? ? "https://ttp.typeform.com/to/iGNzOJ" : "https://ttp.typeform.com/to/TNWJ8gAB",
        flag_label: "tutoring_modal_for_14_days"
      }
    end
  end

  def should_show_admin_marketing_modal?(admin_marketing_modal)
    return true if current_user.nil?

    admin_marketing_modal&.show_to?(current_user) && !Flag.flagged_after?(current_user, admin_marketing_modal, "marketing_modal_acknowledged", today_date_in_pst.beginning_of_day)
  end

  def user_initials(user)
    return "" unless user&.first_name || user&.last_name

    "#{user.first_name&.chr}#{user.last_name&.chr}".upcase
  end

  def show_ai_example_toolbox?(section, question_type)
    (gmat_exam? and section != "di" and !%w(two_part_analysis graphics_interpretation table_analysis multi_source_reasoning).include?(question_type)) || (gre_exam? and section != "awa")
  end

  def get_redirect_path_by_option(option)
    case option
    when "a"
      root_path
    when "b"
      ondemand_path
    when "c"
      liveteach_path
    when "d"
      tutoring_path
    end
  end

  def test_prep_bundles
    if Setting.for("ondemand_enabled")
      { a: "self_study_with_mba_admissions", b: "ondemand_with_mba_admissions", c: "live_classes_with_mba_admissions", d: "tutoring_with_mba_admissions" }
    else
      { a: "self_study_with_mba_admissions", c: "live_classes_with_mba_admissions", d: "tutoring_with_mba_admissions" }
    end
  end

  def exam_guarantee_score
    ea_exam? ? "165" : "715"
  end

  def ai_assist_enabled?
    gmat_exam?
  end

  def calculate_time_for_guide(study_module_item)
    question_numbers = study_module_item.study_module_subsection.chapter.official_guide_question_numbers.find_by(guide_version: current_user.guide_year)&.question_numbers
    question_numbers.present? ? question_numbers.values.flatten.size * 300 : 0
  end

  def fetch_instructor_information(exercise, default_instructor)
    return {} unless exercise.instructor.present?

    {
      ondemand: {
        name: default_instructor&.name,
        title: default_instructor&.title,
        image: default_instructor.present? ? default_instructor.image.url : "admin/blank-avatar.png",
        visible: "d-none"
      },
      screenshare: {
        name: exercise.instructor&.name,
        title: exercise.instructor&.title,
        image: exercise.instructor.image.present? ? exercise.instructor.image.url : "admin/blank-avatar.png",
        visible: ""
      }
    }
  end
end
