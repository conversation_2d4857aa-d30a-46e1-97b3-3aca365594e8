module HomeHelper
  def time_chart_data_week
    dates = *(Date.current.beginning_of_week..Date.current.end_of_week)

    {
      labels: dates.map { |date| date.strftime("%A")[0] },
      series: dates.map do |date|
        date.future? ? -1 : current_user.user_times.where(date: date).sum(:all_page).to_f / 60
      end
    }
  end

  def time_chart_data_month
    dates = *(Date.current.beginning_of_month..Date.current.end_of_month)

    {
      labels: dates.map { |date| date.strftime("%-d") },
      series: dates.map do |date|
        date.future? ? -1 : current_user.user_times.where(date: date).sum(:all_page).to_f / 60
      end
    }
  end

  def time_chart_data_6months
    dates = [5, 4, 3, 2, 1, 0].map { |i| i.months.ago.beginning_of_month }

    {
      labels: dates.map { |date| date.strftime("%B")[0..2] },
      series: dates.map do |date|
        current_user.user_times.where(date: date.beginning_of_month..date.end_of_month).sum(:all_page).to_f / 60
      end
    }
  end

  def unread_message_count(lesson_feedback, messages)
    messages.where(lesson_feedback: lesson_feedback).unread.count
  end

  def sorted_lesson_feedback_messages(messages)
    messages = messages.group_by(&:lesson_feedback).map { |lesson_feedbacks, comments| (lesson_feedbacks.nil? ? comments : comments.first) }.flatten
    messages.sort_by(&:created_at).reverse
  end

  def show_tutoring_modal_for?(user)
    days_since_signup = (Date.current - user.created_at.to_date).to_i

    days_since_signup == 5 && %w(gre ea).include?(EXAM_NAME) && !Flag.flagged?(user, user, "tutoring_modal_acknowledged")
  end

  def weekly_office_hours_active?(weekly_hour)
    current_user&.full_ondemand_access and weekly_hour.present?
  end

  def formatted_office_hours(start_time, end_time)
    "#{start_time} - #{end_time} (EST)"
  end

  def show_referrals_element?
    !ondemand_enabled_for_gmat_or_ea? or current_user.ondemand_enabled?
  end
end
