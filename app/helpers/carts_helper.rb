module CartsHelper
  def purchase_btn_copy_tag(cart, coupon, discounted_price, ondemand = false)
    price = coupon.present? ? discounted_price[1] : cart.plan.sale_price
    translation_key = if RecurlyProxy::TRIAL_PLAN_CODES.include?(cart.plan_code) && price.to_f.zero?
      "user.static_pages.plans.purchase_modal.start_free_trial"
    elsif price.to_f.zero?
      "user.static_pages.plans.purchase_modal.start_now"
    elsif ondemand
      "user.plans.modal.ondemand_checkout.button_purchase"
    else
      "user.static_pages.plans.purchase_modal.payment_button"
    end
    t(translation_key)
  end
end
