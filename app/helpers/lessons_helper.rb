module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def lessons_back_button(lesson)
    classes_for_link = "fw-bold navigator-back text-nowrap d-inline-flex"
    data_for_link = {}

    back_to_param = request.referer&.include?("back_to") ? URI.decode_www_form(URI.parse(request.referer).query || "").assoc("back_to")&.last : ""

    if params[:back_to] == "repository" or back_to_param == "repository"
      url = repository_ondemand_resources_path(lesson.section, subsection: lesson.subsection)
      source = t("user.lessons.show.ondemand.back_to_library")
    elsif params[:back_to] == "resources" or back_to_param == "resources"
      url = resources_path
      source = t("user.lessons.show.back_to_resources")
    elsif params[:chapter_id].present? or params[:topic_id].present?
      url = study_plan_path
      source = t("user.lessons.show.back_to_study_plan")
    elsif params[:lesson_from_on_target].present?
      url = chapter_details_analytics_path(chapter_id: lesson.chapter_id, anchor: "tab-lesson-analysis")
      source = t("user.lessons.show.back_to_on_target")
    elsif params[:action] == "end_chapter"
      source = t("user.lessons.show.back_to_study_plan")

      facade = PersonalizedEvaluationsFacade.new(current_user, lesson.chapter)
      if facade.show_personalized_evaluation_modal?
        url = "#"
        classes_for_link += " personalized-test-modal-trigger"
        data_for_link = { bs_target: "#personalized-test-modal", bs_toggle: "modal" }
      elsif facade.show_examples_not_completed_modal?
        url = "#"
        data_for_link = { bs_target: "#examples-not-completed", bs_toggle: "modal" }
      else
        classes_for_link += " show-overlay"
        url = study_plan_path
      end
    elsif params[:bookmark_chapter_id].present? or lesson.archived?
      url = chapter_flags_path(section: lesson.section, chapter_id: lesson.chapter.id, flaggable_type: "topic")
      source = t("user.lessons.show.back_to_bookmarks")
    else
      url = smart_lessons_path(chapter: lesson.chapter.id, from: params[:from], section: lesson.chapter.section, subsection: lesson.chapter.subsection)
      source = t("user.lessons.show.back")
    end

    link_to url, class: classes_for_link, data: data_for_link do
      safe_join(
        [
          content_tag(:i, nil, class: "far fa-arrow-left"),
          content_tag(:span, "Back to", class: "d-md-block d-none ms-1 me-1"),
          content_tag(:span, source, class: "d-md-block d-none text-capitalize")
        ]
      )
    end
  end

  def fetch_tag_details(lesson)
    return [lesson.name, lesson_path(lesson)] unless lesson.type.eql?("Chapter")

    ["Chapter: #{lesson.name}", lesson_path(lesson.topics.in_track(current_user).first)]
  end

  def section_color_knob(section)
    return "#E3DDC3" if gmat_focus_edition?

    section_colors = {
      "quant" => "#CDE5F4",
      "verbal" => "#E8DEED",
      "di" => "#CDF4E6"
    }

    section_colors[section]
  end

  def coordinate_geometry_lesson_bar?(lesson)
    current_user.track.for_legacy_course == false and gmat_exam? and lesson.chapter.name == "Coordinate Geometry"
  end

  def fetch_section_image_path(score_type, section)
    case section
    when "di", "ir"
      image_tag("layout/controllers/lessons/show/#{score_type}_#{section}.svg")
    else
      image_tag("layout/controllers/lessons/show/#{score_type}.svg")
    end
  end

  def ai_summaries_enabled(lesson)
    (gmat_exam? && (lesson.section != "di")) || (gre_exam? && (lesson.section != "awa"))
  end

  def ai_assist_enabled(lesson)
    ((gmat_exam? && (lesson.section != "di")) || (gre_exam? && (lesson.section != "awa"))) && !lesson.archived?
  end
end
