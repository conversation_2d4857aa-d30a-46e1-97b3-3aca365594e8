module DashboardsHelper
  def fetch_diagnostic_path(diagnostic_attempt)
    return "#" if diagnostic_attempt.nil?

    if diagnostic_attempt.finish?
      [evaluation_attempt_path(diagnostic_attempt, section: current_section), t("free_accounts.prep_toolbox.diagnostic_test.see_result")]
    else
      [evaluation_continue_path(diagnostic_attempt), t("free_accounts.prep_toolbox.diagnostic_test.continue")]
    end
  end

  def fetch_last_studied_deck
    FlashCardDeck.for_user(current_user).joins(:flash_card_sessions).where(flash_card_sessions: { user_id: current_user.id }).order("flash_card_sessions.created_at DESC").limit(1).first
  end

  def session_assets_icon_path(name)
    "layout/controllers/online_classes/#{name}"
  end

  def zoom_link_active(session)
    (session.zoom_link.present? or session.online_class.zoom_link.present?) and Time.current >= (session.date.to_date - 1).beginning_of_day
  end

  def liveteach_action_btn_attributes(session, is_completed)
    recording_url = session.recording_url
    zoom_link = session.zoom_link

    if is_completed and recording_url.present?
      {
        icon: session_assets_icon_path("clapperboard_play_icon.svg"),
        disabled: false,
        title: t("user.home.next_liveteach_class.view_class_recording"),
        target: "#online-class-session-#{session.id}-recording",
        url: nil,
        class_name: "online-class-recording",
        icon_text: t("user.home.next_liveteach_class.view_class_recording")
      }
    elsif is_completed and recording_url.blank?
      {
        icon: session_assets_icon_path("clapperboard_play_disabled.svg"),
        disabled: true,
        title: t("user.home.next_liveteach_class.no_recording_url"),
        target: nil,
        url: nil,
        class_name: "disabled",
        icon_text: t("user.home.next_liveteach_class.view_class_recording")
      }
    elsif zoom_link_active(session)
      {
        icon: session_assets_icon_path("zoom_meet_icon.svg"),
        disabled: false,
        title: t("user.home.next_liveteach_class.open_zoom_link"),
        target: nil,
        url: zoom_link || session.online_class.zoom_link,
        class_name: "zoom-meet-icon",
        icon_text: ""
      }
    else
      {
        icon: session_assets_icon_path("zoom_meet_icon_disabled.webp"),
        disabled: true,
        title: t("user.home.next_liveteach_class.no_zoom_link"),
        target: nil,
        url: nil,
        class_name: "zoom-meet-icon-disabled disabled",
        icon_text: t("user.home.next_liveteach_class.open_zoom_link")
      }
    end
  end

  def see_files_btn_attributes(session)
    {
      type: "button",
      data: {
        bs_toggle: "tooltip",
        bs_placement: "top",
        bs_title: t("user.home.next_liveteach_class.see_session_files"),
        bs_target: "#online-class-session-#{session.id}-files",
        stop_propagation: true
      }
    }
  end

  def sessions_homework_partial_name(homework)
    if %w(pre_reading custom_test chapter_test).include?(homework.task_type)
      "dashboards/#{homework.task_type}_homework"
    else
      "dashboards/checkable_homework"
    end
  end

  def fetch_topic_from_homework_data(topic_id)
    Topic.find_by(id: topic_id)
  end

  def task_completed?(topic, current_user)
    return false if topic.nil?

    Topic.completed_for(current_user).include?(topic)
  end

  def homework_groups_completed?(online_class_session, current_user)
    online_class_session.homework_groups.all? do |group|
      homework_group_completed?(group, current_user)
    end
  end

  def homework_group_completed?(group, user)
    group.homework_tasks.all? { |task| task.generate_completion_for(user)[:percentage] == 100 }
  end

  def first_incomplete_group_for_session(session, user)
    session.homework_groups.find do |group|
      group.homework_tasks.any? { |task| task.generate_completion_for(user)[:percentage] < 100 }
    end
  end

  def homework_task_completion(task, user)
    completion = task.generate_completion_for(user)
    { completion: completion, completed: completion[:percentage] == 100 }
  end
end
