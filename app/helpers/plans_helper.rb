module Plans<PERSON><PERSON>per
  def comprehensive_prep_benefits_tabs
    { a: t("user.static_pages.plans.ondemand_plans_mobile_view.comprehensive_prep.ai_tutor", exam: EXAM_NAME.upcase), b: t("user.static_pages.plans.ondemand_plans_mobile_view.comprehensive_prep.unlimited_practice"), c: t("user.static_pages.plans.ondemand_plans_mobile_view.comprehensive_prep.instant_answer"), d: t("user.static_pages.plans.ondemand_plans_mobile_view.comprehensive_prep.tools_for_mastery") }
  end

  def select_comprehensive_prep_bg(key)
    case key
    when "a"
      "layout/controllers/content_pages/plans/ondemand_plans_mobile/ai_tutor_bg.webp"
    when "b"
      "layout/controllers/content_pages/plans/ondemand_plans_mobile/unlimited_practice_bg.webp"
    when "c"
      "layout/controllers/content_pages/plans/ondemand_plans_mobile/instant_answers_bg.webp"
    else
      "layout/controllers/content_pages/plans/ondemand_plans_mobile/tools_for_mastery_bg.webp"
    end
  end

  def show_mobile_plan_design_for_gmat?(plans_mobile_view = false)
    gmat_exam? and Setting.for("ondemand_enabled") and current_user.blank? and plans_mobile_view
  end
end
