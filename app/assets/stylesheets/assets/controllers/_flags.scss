body[data-controller="flags"] {
  &[data-action="chapter_list"] {
    .numeration {
      font-size: 14px;
      font-weight: 600;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      line-height: 28px;
      margin-right: 30px;
    }

    table {
      tbody {
        tr {
          height: 67px;

          td {
            > div {
              @media (max-width: 576px) {
                padding-top: 14px;
                padding-bottom: 14px;
              }
            }

            a {
              letter-spacing: 0.48px;

              &:hover {
                text-decoration: underline;
              }

              i {
                width: 45px;
                height: 45px;
                line-height: 45px;
              }
            }

            &:first-of-type {
              padding-right: 24px;
              border-left: 24px solid transparent;
            }

            &:last-of-type {
              padding-left: 24px;
              border-right: 24px solid transparent;
            }
          }

          &:last-of-type {
            td {
              &:first-of-type {
                border-left: 0;
                padding-left: 24px;
              }

              &:last-of-type {
                border-right: 0;
                padding-right: 24px;
              }
            }
          }
        }
      }
    }

    @media (max-width: 767px) {
      table {
        tbody tr {
          td {
            &:last-of-type {
              padding-left: 0;
            }
          }
        }
      }
    }
  }

  &[data-action="chapter"] {
    .section-icon {
      width: 28px;
      height: 28px;
      margin-right: 12px;
    }

    .shadow-card {
      box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);

      .card-header {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        min-height: 68px;
        display: flex;

        a {
          i {
            margin-left: 14px;
          }

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .note-bookmark {
      li:nth-of-type(1n+3) {
        @media (max-width: 1400px) {
          display: none !important;
        }
      }
    }

    #filter-chapter,
    #filter-topic {
      margin-left: 20px;
      margin-right: 20px;
    }
  }

  &[data-action="index"],
  &[data-action="notes"] {
    .bookmark-nav-container {
      a.list-group-item {
        color: $grey-4;
        line-height: 1.33;
        letter-spacing: 0.39px;
        border-color: transparent;
        font-weight: normal;
        padding: 16px 22px;

        &.active {
          background-color: transparent !important;
          color: $dark-1;
          font-weight: 600;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .separator {
        height: 28px;
      }
    }

    #bookmark-chapter-dropdown {
      button {
        border-style: none;
        background-color: transparent;
        padding: 4px 14px;
        display: inline-flex;
        align-items: center;

        i {
          color: $dark-1;
          margin-left: 14px;
          flex: 0 1;
          transition: transform .2s ease-in-out;
        }

        &::after {
          content: none;
        }

        &.show {
          i {
            color: inherit;
            transform: rotate(180deg);
          }
        }

        @media (max-width: 767px) {
          padding-top: 12px;
          padding-bottom: 12px;
        }
      }

      ul {
        padding-top: 0;
        padding-bottom: 0;
        box-shadow: 2px 8px 20px 0 rgba(183, 195, 209, 0.3);

        a {
          color: $grey-1;
          height: 44px;
          line-height: 34px;
          padding-left: 20px;
          padding-right: 20px;

          &.active,
          &:active {
            color: $grey-1;
            font-weight: 600;
            background-color: transparent !important;
          }

          &:hover {
            background-color: $grey-8 !important;
          }

          &.disabled {
            color: $grey-5;
          }
        }
      }
    }

    .filter-flaggable-type-responsive {
      padding-top: 10px;
      padding-bottom: 10px;

      > span {
        margin-right: 8px;
      }

      button {
        background-color: transparent;
        border: transparent;
        padding: 7px 8px;

        &::after {
          content: none;
        }

        i {
          flex: 0 0 auto;
          margin-left: 12px;
          transition: transform .2s ease-in-out;
        }

        &.show i {
          transform: rotate(180deg);
        }

        &:hover {
          background-color: $grey-8;
        }
      }

      ul.dropdown-flaggable-type-menu {
        padding-top: 0;
        padding-bottom: 0;
        box-shadow: 2px 8px 20px 0 rgba(183, 195, 209, 0.3);

        li {
          color: $grey-1;
          min-height: 49px;
          padding: 0;
          font-weight: 600;

          a {
            color: $grey-1;
            padding-left: 20px;
            padding-right: 16px;
            padding-top: 14px;
            padding-bottom: 14px;
            min-height: 49px;

            &:hover,
            &:active {
              color: $grey-1;
              background-color: #E9ECEF;
            }

            &.active {
              font-weight: 600 !important;
              background-color: transparent !important;
            }
          }
        }
      }
    }

    button.filter-chapter-responsive {
      background-color: transparent;
      border-color: transparent;
      width: 28px;
      height: 28px;

      &:hover {
        background-color: $grey-8;
        font-weight: 400;
      }
    }

    .filterable-flags-menu {
      max-height: 375px;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .tab-content {
      margin-top: 48px;
    }

    .content-bookmarks-chapter,
    .content-bookmarks-instructor {
      .chapter-title {
        margin-bottom: 17px;
        margin-left: 0;

        .numeration {
          margin-right: 12px;
          width: 28px;
          height: 28px;
        }

        .view-all-link {
          padding-right: 0;

          p {
            line-height: 22px;
          }

          i {
            line-height: 17px;
            margin-left: 6px;
            width: 20px;
            height: 20px;
          }

          &:hover {
            @media (max-width: 767px) {
              i {
                margin-left: 0;
                background-color: $grey-7;
              }
            }
          }
        }
      }

      .chapter-content,
      .instructor-content {
        margin-bottom: 64px;

        &:last-of-type {
          margin-bottom: 0;
        }

        .card-header {
          padding: 20px 28px;
        }
      }
    }

    #note-bookmarks-tab {
      .bookmark-nav-container {
        min-height: 55px;
      }

      .note-bookmark {
        padding-left: 12px;
        padding-right: 12px;
      }
    }
  }

  &[data-action="details"] {
    .division {
      margin-bottom: 27.5px;
      flex: 1 0 0%;
    }

    .card-header {
      padding: 20px 28px;
    }

    .dropdown-menu {
      max-width: 400px;

      @media (max-width: 576px) {
        max-width: 320px;
      }
    }

    .accordion-item {
      box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);

      button {
        height: 70px;
        border-radius: 2px;
        font-size: 20px;
        box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .125);

        &:hover {
          box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .125);
        }

        &.collapsed {
          background-color: $light-3 !important;
          border-bottom-width: 1px;
          box-shadow: none;
        }

        &:not(.collapsed)::after {
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
        }

        .numeration {
          height: 22px;
          font-size: 13px;
          font-weight: 600;
          line-height: normal;
          padding: 4px 8px;
          margin-right: 20px;
          border-radius: 13px;
        }

        a:hover {
          span:not(.numeration) {
            text-decoration: underline;
          }
        }
      }

      .accordion-collapse {
        border-width: 0 1px 1px;
        border-bottom-left-radius: 2px;
        border-bottom-right-radius: 2px;
      }
    }
  }

  .topic-bookmark,
  .must_know-bookmark,
  .example-bookmark,
  .concept_mastery-bookmark,
  .problem-bookmark {
    padding-left: 0;
    padding-right: 0;

    li {
      display: flex;

      .content {
        flex-basis: 100%;

        > a {
          i {
            margin-right: 20px;
          }

          .quantitative_comparison {
            font-size: 18px;
            font-weight: 300;

            h4 {
              font-weight: 300;
              color: #212529;
              display: inline-block;
            }

            .quantity-a {
              width: calc(30% - 1rem);
              margin-right: 50px;
            }

            .quantity-b {
              width: calc(30%);
            }

            @media (max-width: 1439px) {
              .quantity-a {
                width: calc(40% - 1rem);
              }

              .quantity-b {
                width: calc(40%);
              }
            }

            @media (max-width: 767px) {
              .quantity-a {
                margin-right: 0;
              }

              .quantity-a,
              .quantity-b {
                width: 100%;
              }
            }
          }
        }

        .bookmark-content {
          flex: 1 0 calc(100% - 28px);

          p {
            font-weight: 300;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      &:last-of-type .content {
        border-bottom: 0 !important;
      }
    }
  }

  p {
    margin-bottom: 0;

    img {
      width: auto !important;
      height: inherit !important;
      padding-bottom: 1rem;
    }
  }

  .topic-bookmark {
    li {
      .content {
        > a {
          padding-top: 21.5px;
          padding-bottom: 21.5px;
        }
      }

      &:last-of-type .content {
        border-bottom: 0 !important;
      }
    }
  }

  .example-bookmark,
  .concept_mastery-bookmark,
  .problem-bookmark {
    li {
      .content {
        > a {
          padding-top: 32px;
          padding-bottom: 32px;
        }
      }
    }
  }

  .example-bookmark,
  .problem-bookmark {
    p {
      img {
        max-width: 15%;

        @media (max-width: 1799px) {
          max-width: 24%;
        }

        @media (max-width: 1399px) {
          max-width: 33%;
        }

        @media (max-width: 1199px) {
          max-width: 42%;
        }

        @media (max-width: 991px) {
          max-width: 51%;
        }

        @media (max-width: 767px) {
          min-width: 80%;
          max-width: 60%;
        }

        @media (max-width: 575px) {
          max-width: 69%;
        }
      }
    }
  }

  .must_know-bookmark {
    li {
      blockquote.must-know {
        margin: 12px 0;
        display: flex;

        div {
          &:first-of-type {
            flex: 1 1 auto;

            .header {
              border: none !important;
            }

            .content {
              padding-right: 0 !important;

              p {
                line-height: 1.5;
              }
            }

            &::before {
              background-size: auto 18px;
            }
          }

          &:last-of-type:not(.content) {
            margin-top: auto;
            margin-bottom: auto;
            flex-direction: row;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;

            a {
              margin-bottom: 6px;
              margin-left: 24px;

              &:hover {
                background-color: white !important;
              }
            }
          }
        }
      }

      & > div {
        border-bottom: none !important;
      }

      &:first-of-type {
        blockquote {
          margin-top: 12px !important;
        }
      }

      &:last-of-type {
        blockquote {
          margin-bottom: 12px !important;
        }
      }
    }

    @media (max-width: 767px) {
      blockquote.must-know {
        .header {
          border-bottom: 0 !important;
        }

        p {
          font-size: 18px;
          line-height: 1.44 !important;
        }

        div:last-of-type:not(.content) {
          a {
            margin-left: 16px;
          }
        }
      }
    }

    @media (max-width: 576px) {
      blockquote.must-know {
        position: relative;

        .header {
          padding-bottom: 2px;
        }

        .content {
          margin-bottom: 2px;
        }

        div:last-of-type:not(.content) {
          position: absolute;
          right: 20px;
          top: 10px;
          margin-top: 0;
          margin-bottom: 0;
          margin-left: 0;

          .badge {
            position: static;
          }

          a {
            margin-top: 5px;
            margin-left: 5px !important;
            margin-bottom: 0;
          }
        }

        &::before {
          background-size: auto 13px;
        }
      }
    }
  }

  #bookmark-chapter-dropdown,
  #bookmark-instructor-dropdown,
  #bookmark-chapter-select-responsive,
  #bookmark-instructor-select-responsive,
  #bookmark-topic-dropdown,
  #bookmark-topic-select-responsive {
    button {
      border-style: none;
      background-color: transparent;
      padding: 4px 14px;
      display: inline-flex;
      align-items: center;

      i {
        color: $dark-1;
        margin-left: 14px;
        flex: 0 1;
        transition: transform .2s ease-in-out;
      }

      &::after {
        content: none;
      }

      &.show {
        i {
          color: inherit;
          transform: rotate(180deg);
        }
      }

      &:hover {
        background-color: $grey-8;
      }

      @media (max-width: 767px) {
        padding-top: 12px;
        padding-bottom: 12px;
      }
    }

    ul {
      padding-top: 0;
      padding-bottom: 0;
      box-shadow: 2px 8px 20px 0 rgba(183, 195, 209, 0.3);

      a {
        color: $grey-1;
        min-height: 64px;
        padding: 20px;

        &.active,
        &:active {
          color: $grey-1;
          font-weight: 600;
          background-color: transparent !important;
        }

        &:hover {
          background-color: $grey-8 !important;
        }

        &.disabled {
          color: $grey-5;
        }
      }
    }
  }

  button.filter-chapter-responsive,
  button.filter-topic-responsive {
    background-color: transparent;
    border-color: transparent;
    width: 28px;
    height: 28px;

    &:hover {
      background-color: $grey-8;
    }
  }

  .call-out {
    padding: 18px 15px;
    border-left-width: 5px !important;
  }

  .gutter-24 {
    --bs-gutter-x: 24px;
    --bs-gutter-y: 24px;
  }

  a.delete-bookmark {
    display: inline-flex;
    width: 30px;
    height: 30px;

    &:hover {
      background-color: $light-5;
    }
  }

  .no-flags-container {
    display: flex;
    align-items: center;
    min-height: 400px;

    @media (max-width: 767px) {
      min-height: auto;
    }
  }

  .no-flaggables {
    flex: auto;

    img {
      width: auto;
      min-height: 58px;
      max-width: 175px;
    }

    p {
      max-width: 320px;
      margin: 0 auto;
    }
  }

  .note {
    .noteable {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      font-weight: 600;

      > a {
        i {
          font-size: 8px;
          margin-right: 5px;
          vertical-align: middle;
        }

        > p {
          display: inline;
          font-weight: 600;
          margin-bottom: 0;
          vertical-align: middle;
        }
      }
    }

    .annotation {
      margin-bottom: 20px;
      font-weight: 600;
    }

    .selected-text {
      overflow: hidden;

      p {
        display: inline;

        &:last-child {
          margin: 0;
        }
      }

      img {
        display: block;
        margin: 0 auto;
      }
    }
  }

  select {
    color: $grey-3;
  }

  // Style overrides for ADA compliance

  &.di {
    #bookmark-chapter-dropdown {
      button {
        p,
        i {
          color: $cyan-di-1 !important;
        }
      }
    }

    button.filter-chapter-responsive,
    button.filter-topic-responsive {
      color: $cyan-di-1 !important;
    }

    .view-all-link {
      color: $cyan-di-1 !important;
    }

    .content-bookmarks-chapter,
    .content-bookmarks-instructor {
      .numeration {
        background-color: #0F5C61 !important;
      }

      h5 {
        color: #0F5C61 !important;
      }
    }

    .filter-flaggable-type-responsive {
      button {
        i {
          color: $cyan-di-1 !important;
        }

        p {
          color: $cyan-di-1 !important;
        }
      }
    }
  }

  &.verbal {
    .content-bookmarks-chapter,
    .content-bookmarks-instructor {
      .chapter-title {
        .view-all-link {
          color: #8F54A7 !important;
        }
      }
    }

    .filter-flaggable-type-responsive {
      button {
        color: #8F54A7 !important;
      }
    }
  }

  .online-class-session-bookmark {
    .list-group {
      position: relative;
      padding-top: 16px;
      background-color: transparent;
      border: none;
      width: 100%;

      .list-item {
        .card {
          box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
          border: 1px solid;
          background-color: $white;
          position: relative;
          border-radius: 4px;
          border-image-slice: 1;
          background-origin: border-box;
          background-clip: content-box, border-box;
          border-image-source: linear-gradient(to left, $grey-6 71%, #C4CCF2 30%);
          background-image: linear-gradient(to right, #F3F4FC -4%, #FFF 9%, #FFF 95%, #F3F4FC 100%), linear-gradient(to left, $grey-6 71%, #C4CCF2 30%);
          border-left-width: 4px;
          cursor: pointer;

          .session-video {
            position: relative;
            width: 190px;

            .video-link {
              width: 100%;
              height: 100%;
              display: flex;
            }

            .video-thumbnail {
              width: 100%;
              height: 116px;
            }

            .play-button {
              position: absolute;
              margin: 0 auto;
              left: 0;
              right: 0;
              text-align: center;
              top: 0;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              background-image: linear-gradient(171deg, rgba(255, 255, 255, 0.1) -13%, rgba(189, 200, 234, 0.8) 99%);
            }
          }

          .session-info-block {
            padding-left: 24px;
            display: -webkit-box !important;
            -webkit-line-clamp: 10;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;

            .session-details {
              padding-top: 18px;

              .session-topic {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #545E64;
                font-size: 16px;
                line-height: 1.5;

                p {
                  margin-bottom: 0;
                }
              }

              .session-title {
                text-align: left;
                color: $blue-2;
                margin-bottom: 10px !important;
                font-size: 18px;
                font-weight: 600;
                line-height: 1.33;
              }
            }
          }

          .action-btn-group {
            margin-left: auto;
            padding-right: 41px !important;
            gap: 6px;

            .delete-bookmark {
              display: inline-flex;

              &:hover {
                background-color: $blue-5;
              }
            }

            .action-btn-icon-group {
              &:first-child {
                padding: 4px 6px 5px 6px;
              }

              &:nth-child(2) {
                font-size: 18px;
                padding-top: 6px;
              }

              .clapperboard-play-icon {
                width: 20px;
                height: 18px !important;
              }

              &:hover {
                background-color: $blue-5;
              }
            }
          }

          &:hover {
            border-color: $blue-3;
            border-image-source: none;

            .session-video {
              .play-button {
                background-image: linear-gradient(171deg, rgba(255, 255, 255, 0.1) -13%, rgba(110, 128, 188, 0.8) 99%);
              }
            }
          }
        }
      }

      .online-class-session-recording {
        .modal-dialog {
          .modal-content {
            .modal-header {
              padding-left: 34px;
            }
          }
        }
      }
    }
  }

  @media (max-width: 1200px) {
    .online-class-session-bookmark {
      .list-group {
        .list-item {
          .card {
            .session-info-block {
              width: calc(100% - 280px);

              .session-topic {
                font-size: 15px;
              }
            }

            .action-btn-group {
              padding-right: 22px !important;
            }
          }
        }
      }
    }
  }

  @media (max-width: 767px) {
    .online-class-session-bookmark {
      .list-group {
        .list-item {
          .card {
            max-width: 320px;
            margin: 0 auto;

            .session-video {
              width: 100%;

              .video-thumbnail {
                height: 180px;
              }

              .play-button {
                .play-icon {
                  width: 54px;
                }
              }
            }

            .session-info-block {
              width: calc(100% - 90px);
              padding-bottom: 20px;

              .session-details {
                .session-topic {
                  font-size: 15px;
                  display: inline-block;
                }
              }
            }

            .action-btn-group {
              padding-right: 22px !important;
            }
          }
        }
      }
    }
  }
}
