body[data-controller="referrals"] {
  input:not([type="checkbox"]),
  .icon > button {
    min-height: 44.2px;
  }

  .icon > button {
    min-width: 44.2px;
    background-color: transparent;
    border: 0;
    color: $blue-3;
    font-size: 20px;

    &:hover {
      background-color: $primary-5;
    }
  }

  .row.referral {
    box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
    background-color: #FFF;
    border-radius: 5px;

    .referral-form {
      .header {
        background-image: asset-url("layout/controllers/referral/header_background.png");
        background-size: cover;
        background-repeat: no-repeat;
        border-top-left-radius: 5px;

        .wrapper {
          padding: 38px 54px;
          text-align: center;

          img {
            max-width: 372px !important;
          }

          h3 {
            line-height: 1.5;
            margin-bottom: 0;
          }
        }
      }

      .body {
        padding: 21px 58px 150px 58px;

        .icon-wrapper {
          font-size: 14px;
          border-radius: 50%;
          width: 25px;
          height: 25px;
          line-height: 25px;
          box-shadow: 0 4px 8px 0 rgba(14, 45, 66, 0.2);
          background: linear-gradient(135deg, #A0AEFA 20%, #2682B7 105%);
          color: $white;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .share-link {
          button {
            width: 155px;
            height: 44px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
          }

          .form-control:disabled {
            background-color: transparent;
          }
        }

        .seperator {
          margin: 45px 0 30px 0;
          display: flex;
          justify-content: center;
          align-items: center;

          .fade-rule {
            height: 1px;
            background-color: #56A6D5;
            flex-grow: 1;
            margin: 0 auto;
            background-image: -webkit-linear-gradient(left, white 2%, #1E698F 50%, white 98%);
          }

          .text {
            font-size: 16px;
            font-weight: 600;
            line-height: normal;
            color: $gmat-0;
            position: absolute;
            background-color: white;
            padding: 0 10px;
          }
        }

        .referred-link {
          @extend .col-12;
          @extend .col-md;
          @extend .ps-0;
          @extend .order-1;
          @extend .order-md-2;
          @extend .gx-0;
          @extend .ms-md-1;
        }

        .blurred-referred-link {
          @extend .referred-link;

          input[type="text"] {
            color: transparent;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
          }
        }

        .email-friends {
          .second-way-description,
          .second-way-blurred-email {
            display: inline;
          }

          .second-way-blurred-email {
            filter: blur(5px);
          }
        }

        form {
          .email-input-wrapper {
            .invalid-feedback {
              display: none;
            }

            &.with-errors {
              .invalid-feedback {
                display: block;
              }
            }
          }
        }
      }

      @media only screen and (max-width: 991px) {
        .header {
          border-top-right-radius: 5px;
        }

        .body {
          padding-bottom: 80px;
        }
      }

      @media only screen and (max-width: 767px) {
        .header {
          .wrapper {
            padding: 38px;

            img {
              max-width: 280px !important;
            }
          }
        }

        .body {
          padding-right: 28px;
          padding-left: 28px;
        }
      }

      @media only screen and (max-width: 413px) {
        .header {
          .wrapper {
            padding: 20px;

            h3 {
              font-size: 20px;
              line-height: 1.33;
            }

            img {
              max-width: 200px !important;
            }
          }
        }

        .body {
          padding-right: 20px;
          padding-left: 20px;
        }
      }
    }

    .referral-instructions {
      background-image: asset-url("layout/controllers/referral/referral_instructions_desktop.webp");
      background-size: cover;
      background-repeat: no-repeat;
      height: 100%;
      min-height: 831px;
      padding: 80px 48px 0 40px;

      .wrapper {
        max-width: 400px;

        .title {
          width: 314.41px;
          font-weight: 600;
          font-size: 25px;
          line-height: 37px;
          color: $dark-1;

          span {
            &.highlight {
              background: linear-gradient(138.24deg, #3B87B3 5.05%, #87D4FF 100%);
              border-radius: 5px;
              font-weight: 600;
              line-height: 30px;
              color: $light-5;
              text-shadow: 0 0 3.59322px $primary-2;
              transform: translateX(-0.25em);
              display: inline-block;
            }
          }
        }

        .instructions {
          margin-top: 60px;
          margin-left: 10px;

          .instruction {
            font-size: 18px;
            line-height: 24px;
            color: $dark-2;

            &:not(:first-child) {
              margin-top: 31px;
            }

            img {
              margin-top: -3px;
            }

            p {
              margin-left: 16px;
              margin-bottom: 0;
            }

            sup {
              top: 0;
              font-size: 18px;
            }
          }
        }

        .note {
          margin-left: 48px;
          margin-top: 24px;
          border-top: 1px solid;
          border-image-source: linear-gradient(90deg, #CCCED7 2.97%, #FFF 100%);
          border-image-slice: 1;
          padding-top: 8px;

          span {
            font-weight: 400;
            font-size: 14px;
            line-height: 26px;
            color: $grey-4;

            sup {
              top: 0;
              font-size: 14px;
            }
          }
        }
      }

      @media only screen and (max-width: 1199px) {
        background-image: asset-url("layout/controllers/referral/referral_instructions_tablet.webp");
      }

      @media only screen and (max-width: 767px) {
        background-image: asset-url("layout/controllers/referral/referral_instructions_mobile.webp");
        padding: 70px 48px 0 48px;
        height: 1016px;

        .wrapper {
          max-width: 100%;

          .title {
            margin: 0 auto;
            font-size: 20px;
            text-align: center;
            max-width: 270px;

            span {
              &.highlight {
                padding: 4px;
              }
            }
          }

          .instructions {
            max-width: 344px;
            margin: 0 auto;
            margin-top: 95%;

            .instruction {
              font-size: 16px;

              &:not(:first-child) {
                margin-top: 16px;
              }
            }

            .note {
              margin-top: 6px;
              padding-top: 6px;
            }
          }
        }
      }

      @media only screen and (max-width: 419px) {
        padding: 17% 24px 0 24px;

        .wrapper {
          .title {
            max-width: 100%;
          }

          .instructions {
            margin-top: 225px;
          }
        }
      }
    }

    @media only screen and (min-width: 1800px) {
      .form-column {
        width: calc(100% - 600px);

        .referral-form {
          .header {
            .wrapper {
              h3 {
                br {
                  display: none !important;
                }
              }
            }
          }

          .body {
            max-width: 750px;
            margin: 0 auto;
          }
        }
      }

      .instruction-column {
        width: 600px;
      }
    }
  }
}
