body[data-controller="problem_solving"] {
  #loading-overlay {
    display: block;
  }

  @mixin problem-solving-mobiles {
    .question {
      .question-content {
        form {
          border: none;
          border-radius: 0;
          padding: 0;
        }
      }
    }

    &.gre {
      nav.question-nav {
        position: relative;
        padding: 12px;
        border-radius: 10px 10px 0 0;

        .pull-right {
          .timers {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            background-color: #EDF8FF;
            padding: 6px 10px;

            .question-time,
            .test-time {
              color: $dark-1;
              padding: 0;

              &.out-of-time {
                .time {
                  color: #E66A6A;
                }
              }
            }

            .test-time {
              float: right;
            }

            @media only screen and (max-width: 767px) {
              font-size: 14px;
            }

            @media only screen and (max-width: 414px) {
              font-size: 12px;
            }
          }

          span.last.number {
            top: 10px;
            right: 10px;
          }

          .navigation-buttons { display: none; }

          .navigation-problem,
          .navigation-on-review-section {
            position: fixed;
            top: $navbar-height;
            left: 0;
            right: 0;
            background-color: #062031;
            text-align: center;
            display: flex;
            align-items: center;
            padding: 11px 0 10px 12px;
            z-index: 1;
          }

          .navigation-problem {
            a i.fas,
            a i.far,
            a,
            .flag-wrapper a i.fa,
            span,
            .flag-wrapper a {
              color: white;
              border-color: white;
              font-size: 16px;
            }

            .item {
              border-right: solid 1px white;
              width: 20%;
              display: flex;
              align-items: center;
              justify-content: center;

              &:last-child { border: none; }
            }

            .navigation-buttons-mobile {
              display: inline-flex;
              justify-content: space-evenly;

              button {
                border: none;
                background-color: white;
                font-size: 16px !important;
                margin: 0;
                padding: 8px 14px;

                i.fa-chevron-left,
                i.fa-chevron-right {
                  margin: 0 !important;
                  vertical-align: middle;
                }
              }
            }
          }

          .navigation-on-review-section .item {
            width: 50%;
            font-size: 16px;
            border-right: solid 1px white;
            color: white;
          }
        }

        @media only screen and (max-width: 991px), only screen and (min-width: 1200px) and (max-width: 1399px) {
          margin: 120px 10px 0;
        }
      }

      &.hide-container {
        nav.question-nav {
          @media only screen and (max-width: 991px), only screen and (min-width: 1200px) and (max-width: 1399px) {
            margin: 55px 10px 0;
          }
        }
      }

      .question,
      &.on-review #review-test-status {
        width: auto;
        margin: 20px 10px;
        padding-top: 0;
        border-radius: 0 0 10px 10px;
      }

      &.on-review #review-test-status { padding-top: 20px; }

      &.final-page {
        nav.question-nav {
          height: 85px;
          min-height: 85px !important;

          .pull-right {
            float: right;
            display: flex;
            align-items: center;
            width: 100%;

            .hide-timer.item {
              white-space: nowrap;
              padding: 0 5px;
            }

            .navigation-problem,
            .navigation-on-review-section {
              position: initial;
              background-color: initial;
              display: inline-block;
              padding: 0;
            }

            .navigation-problem {
              a.show-review {
                padding: 0 10px;
                width: initial;
                border-right: solid 1px $dark-1;

                i.fas,
                i.far,
                span { color: $dark-1; }
              }
            }

            .timers { top: 60px; }

            .navigation-on-review-section {
              padding-left: 10px;
              display: none;
            }

            .navigation-buttons {
              display: flex;
              margin-left: auto;

              .btn {
                padding: 7px 5px;
                width: 70px;
              }
            }
          }

          &.timer-hidden {
            height: 55px !important;
            min-height: 55px !important;
          }
        }

        &.on-review nav.question-nav .pull-right {
          .navigation-buttons,
          .navigation-problem { display: none; }

          .navigation-on-review-section { display: inline-block; }
        }
      }

      @media (min-width: 1201px) and (max-width: 1400px) {
        &.hide-container {
          padding-top: 0;

          .container {
            margin-top: 0;

            .question-nav {
              margin-top: 72px;
            }
          }
        }

        &.quiz_mode {
          .container {
            .question-nav {
              margin-top: 110px;

              span.text-capitalize.fw-semibold {
                position: relative;
                top: -2px;
              }

              .font-switch-buttons {
                top: -2px;
                position: relative;
              }
            }
          }
        }
      }

      @media (max-width: 1200px) {
        &.hide-container {
          padding-top: 8px;

          .container {
            margin-top: 0;

            .question-nav {
              margin-top: 0;
            }
          }
        }

        &.quiz_mode {
          padding-top: 8px;

          .container {
            margin-top: 0;

            .question-nav {
              margin-top: 58px;

              span.text-capitalize.fw-semibold {
                position: relative;
                top: -2px;
              }

              .font-switch-buttons {
                top: -2px;
                position: relative;
              }
            }
          }
        }
      }

      @media (max-width: 991px) {
        &.hide-container {
          padding-top: 15px;

          .container {
            .question-nav {
              margin-top: 57px;
            }
          }
        }

        &.quiz_mode {
          .container {
            .question-nav {
              margin-top: 122px !important;
            }
          }
        }
      }
    }
  }

  .modal {
    &#pause-test-time,
    &#exit-test,
    &#exit-test-and-score,
    &#confirm-review-answer-change-modal {
      .modal-content {
        max-width: 430px;
        margin: 0 auto;

        .btn-close {
          font-size: 15px;
        }

        .btn {
          min-height: 44px;
          min-width: 180px;
        }
      }
    }

    &#pause-test-time,
    &#exit-test {
      .modal-content {
        .btn {
          &.resume,
          &.exit {
            margin: 24px 0 60px 0;
          }
        }
      }
    }

    &#pause-test-time {
      backdrop-filter: blur(5px);
    }

    &#exit-test,
    &#exit-test-and-score {
      .modal-content {
        .modal-body {
          p {
            font-size: 18px;
            line-height: 1.44;

            a {
              color: $primary-2;
              text-decoration: none;
            }
          }
        }
      }
    }

    &#exit-test-and-score {
      .modal-content {
        .btn {
          &.exit {
            margin: 3px 0 44px 0;
          }
        }
      }
    }

    &#confirm-review-answer-change-modal {
      .modal-content {
        .modal-body {
          p {
            font-size: 18px;
            font-weight: 600;
            line-height: 1.44;
            color: $blue-1;
            margin-top: 32px;
            margin-bottom: 0;
          }
        }
      }
    }
  }

  #review-test-status {
    h3 {
      font-size: 1.5625rem;
    }

    .explanation-text {
      margin-top: 80px;
      color: #4D4D4D;

      p {
        font-size: 1rem;
        margin-bottom: 14px;
        font-family: "proxima-nova", Helvetica, Arial, sans-serif;

        &:last-child {
          margin-bottom: 20px;
        }
      }
    }

    .status-tables {
      text-align: center;

      table {
        color: #798291;
        display: inline-block;
        vertical-align: top;
        caption-side: top;
        margin-right: 20px;
        font-family: "proxima-nova", Helvetica, Arial, sans-serif;

        thead {
          tr {
            th {
              padding: 6px;
              font-size: 0.8125rem;
              font-weight: 600;
            }
          }
        }

        tbody {
          tr {
            &.active {
              background-color: #FAFAFA;
            }

            &.not-seen {
              color: #D8D8D8;
            }

            &.clickable_row:hover {
              cursor: pointer;
              background-color: #FAFAFA;
            }

            td {
              font-size: 14px;
              padding: 4px 6px;

              &:nth-child(2) {
                border-left: 1px solid #E6E6E6;
              }

              &:last-child {
                border-right: 1px solid #E6E6E6;
              }
            }
          }
        }

        &.half-width {
          width: initial;
        }

        &.hiden_on_mobile {
          display: none;
        }
      }
    }

    @media only screen and (min-width: 1200px) {
      .explanation-text {
        display: block !important;
      }

      .status-tables {
        table {
          &.half-width {
            width: initial;

            &:nth-child(2) {
              display: inline-block;
              margin-left: 40px;
            }
          }

          thead {
            tr {
              th {
                padding: 6px 20px;
              }
            }
          }

          tbody {
            tr {
              td {
                padding: 2px 20px;
              }

              &.hiden_on_desktop {
                display: none;
              }
            }
          }
        }

        &.hiden_on_mobile {
          display: inline-block;
        }
      }
    }
  }

  a {
    cursor: pointer;
  }

  section#problem-solving,
  section#end-intermediate-section {
    padding-top: 20px;
    background-color: #F7F7F7;
    min-height: 100vh;

    .pull-right {
      float: right;
    }

    .question-actions {
      text-align: left;
      border: none;
      background-color: #EDF8FF;
      padding: 10px;

      .btn {
        background-color: $primary-1;
        border: none;
        color: white;
        margin: 0;
        padding: 9px;
        text-transform: capitalize;
        font-size: 0.8125rem;
        line-height: 0.8125rem;
        min-width: 100px;

        &.exit { float: none; }

        &#pause-btn-on-skip {
          margin-right: 10px;

          @media only screen and (max-width: 1399px) {
            margin-right: 0;
          }
        }

        &.gray {
          background-color: #CCC;
          cursor: default;
          pointer-events: none;
        }
      }

      @media only screen and (max-width: 767px) {
        text-align: center !important;

        .btn {
          float: none !important;
          width: 100%;
          max-width: 100px;
          margin: 2px 0;
        }
      }
    }

    &.gre {
      .question-actions {
        text-align: center;
        background-color: #F3E0E2;

        .btn {
          background-color: $dark-1;
          margin: 0 5px;
        }

        a.exit { float: none; }
      }

      .container {
        max-width: none;
        margin-top: 44px;
        padding-left: 0;
        padding-right: 0;
        width: 100%;
      }

      nav.question-nav {
        background-color: #F3E0E2;
        border-radius: 0;
        height: 60px;
        padding: 12px 9%;

        > span {
          font-size: 18px;
          color: $dark-1;
        }

        .pull-right {
          font-size: 12px;

          .timers,
          div,
          a {
            display: inline-block;
          }

          .item {
            border-right: solid 1px white;
            color: $dark-1;
            padding: 0 10px;

            &.last {
              border: none;
              padding-right: 0;
            }
          }

          .flag-wrapper,
          .whiteboard-wrapper,
          .calculator-wrapper {
            font-size: 14px;

            a {
              i {
                &.far,
                &.fas {
                  color: $dark-1;
                  border: 1px solid $dark-1;
                  padding: 4px 5px;
                  border-radius: 3px;
                }

                &.fas.fa-bookmark {
                  display: none;
                }
              }

              &.flagged {
                i {
                  &.far.fa-bookmark {
                    display: none;
                  }

                  &.fas.fa-bookmark {
                    display: inline-block;
                  }
                }
              }
            }
          }

          i.far,
          i.fas,
          .flag-wrapper a i { color: $dark-1; }

          i.far.fa-calculator {
            font-size: 18px;
            vertical-align: text-bottom;
          }

          i.fa-calculator,
          i.fa-list-ul { margin-right: 5px; }

          .flag-wrapper a i.fas,
          .flag-wrapper a i.far,
          a.hide-timer span { border-color: $dark-1; }

          .navigation-buttons,
          .navigation-on-review-section {
            .btn {
              text-transform: capitalize;
              padding: 7px 0;
              border: none;
              margin-bottom: 0;
              width: 110px;

              &.back-btn,
              &.return-btn {
                color: $dark-1;
                background-color: white;
                margin-right: 10px;

                &:hover {
                  background-color: #77848D;
                }
              }

              &.next-btn,
              &.continue-btn {
                color: white;
                background-color: #0E2D42;

                &:hover {
                  background-color: #77848D;
                }
              }
            }
          }

          a.mark-problem {
            i { margin-right: 5px; }

            &:not(.flagged) i.fa-check-square { display: none; }

            &.flagged i.fa-square { display: none; }
          }

          .timers {
            font-size: 16px;

            i {
              font-size: 16px;
              display: inline-block;
              vertical-align: middle;
            }

            span {
              vertical-align: middle;
              font-weight: 600;
            }

            .time-box {
              vertical-align: middle;

              .time {
                min-width: 30px;
                padding-left: 5px;
                text-align: right;
              }
            }

            .out-of-time .time {
              color: #D00;
            }
          }

          a.hide-timer {
            span {
              border: solid 1px $dark-1;
              padding: 4px 5px;
              border-radius: 4px;
              font-size: 12px;
            }

            &.active {
              span.inactive { display: none; }
            }

            &:not(.active) {
              span.active { display: none; }
            }
          }

          .navigation-buttons-mobile { display: none; }
        }

        @media only screen and (min-width: 1399px) and (max-width: 1500px) {
          padding: 12px 6%;
        }
      }

      .question {
        margin: 20px auto;
        width: 82%;
        border: solid 2px #EBEBEB;
        border-radius: 10px;
      }

      &.on-review {
        nav.question-nav .pull-right {
          .navigation-problem,
          .hide-timer,
          .navigation-buttons { display: none; }

          .navigation-on-review-section { font-weight: bold; }
        }

        .question { display: none; }

        #review-test-status {
          width: 82%;
          text-align: justify;
          border: solid 2px #EBEBEB;
          border-radius: 10px;
          padding: 20px;
          margin: 20px auto;
          background-color: white;

          i.far.fa-check { color: #39C974; }

          .explanation-text { margin-top: 20px; }

          .status-tables table {
            border-collapse: collapse;
            display: table;

            caption {
              color: green;
              text-align: left;
              font-weight: bold;
              margin-bottom: 5px;
            }

            tbody {
              border: 1px solid #E6E6E6;
              border-top: none;
            }

            th {
              background-color: #39C974;
              text-transform: capitalize;
              border-radius: 0;
              color: white;
            }

            tr {
              line-height: 2em;

              th,
              td {
                border: none;
              }

              &:nth-child(even) {
                background-color: #F8F8F8;
              }

              &.active td {
                background-color: #153144;
                color: white;
              }
            }
          }
        }
      }

      &:not(.on-review) {
        nav.question-nav .pull-right .navigation-on-review-section { display: none !important; }

        #review-test-status { display: none !important; }
      }
    }

    &.reading_comprehension {
      .question-actions {
        @media screen and (min-width: 768px) {
          padding: 10px 30px 10px 25px !important;
        }
      }
    }

    &:not(.gre, .sat) {
      nav.question-nav {
        background-color: $primary-1;
        padding: 4px 16px;
        border-radius: 4px 4px 0 0;

        .question-type {
          color: $white;
          white-space: nowrap;
        }

        .actions-wrapper {
          display: flex;
          width: 100%;
          justify-content: space-between;

          .navigation-problem {
            & > * {
              margin-left: 5px;
            }
          }

          .timers,
          div,
          a {
            display: inline-flex;
            align-items: center;
          }

          .font-switch-buttons {
            button {
              display: flex;
              align-items: center;
              height: 24px;
            }
          }

          .item {
            border-right: solid 1px white;
            color: $white;
            padding: 0 10px;
            display: flex;
            align-items: center;
            height: 16px;

            &.number {
              white-space: nowrap;
            }

            &:first-child {
              padding-left: 0;
            }

            &:last-child {
              border: none;
              padding-right: 0;
            }

            @media screen and (max-width: 480px) {
              padding: 0 8px;
            }
          }

          .review-flag-wrapper,
          .whiteboard-wrapper,
          .calculator-wrapper {
            font-size: 14px;

            a {
              i {
                &.far,
                &.fas {
                  color: white;
                  border: 1px solid white;
                  padding: 4px 5px;
                  border-radius: 3px;
                }

                &.fas.fa-bookmark {
                  display: none;
                }
              }

              &.flagged {
                i {
                  &.far.fa-bookmark {
                    display: none;
                  }

                  &.fas.fa-bookmark {
                    display: inline-block;
                  }
                }
              }
            }
          }

          .flag-wrapper {
            a {
              i {
                &.fas.fa-bookmark {
                  display: none;
                }
              }

              &.flagged {
                i {
                  &.far.fa-bookmark {
                    display: none;
                  }

                  &.fas.fa-bookmark {
                    display: inline-flex;
                  }
                }
              }
            }
          }

          .timers {
            font-size: 16px;
            color: $white;

            i {
              font-size: 16px;
              display: inline-block;
              vertical-align: middle;
            }

            span {
              font-weight: 600;
              margin-left: 8px;
            }

            .time-box {
              .time {
                min-width: 30px;
                padding-left: 5px;
                text-align: right;
                font-weight: bold;
              }
            }

            .out-of-time .time {
              color: #FCD9D5;
            }
          }

          a.hide-timer {
            display: inline-flex;

            span {
              border: solid 1px white;
              color: white;
              padding: 2px;
              border-radius: 4px;
              font-size: 10px;
            }

            &.active {
              span.inactive { display: none; }
            }

            &:not(.active) {
              span.active { display: none; }
            }
          }
        }
      }

      &.sat {
        nav.question-nav {
          background-color: $sat-6;

          .navigation-problem {
            .reference-guide {
              cursor: pointer;
            }
          }
        }

        .question-actions {
          background-color: $sat-5;

          .btn {
            &.gray {
              background-color: #CCC;
              cursor: default;
              pointer-events: none;
            }

            background-color: $sat-6;
          }
        }

        #reference-guide-modal {
          .modal-title {
            font-size: 20px;
          }

          .equations,
          .mobile-equations {
            img {
              width: 1000px;
            }
          }
        }
      }
    }

    @media only screen and (min-width: 768px) and (max-width: 1399px) {
      @include problem-solving-mobiles;

      &.gre {
        nav.question-nav {
          align-items: center;
          height: 46px !important;
          min-height: 79px;
        }
      }

      &.gmat,
      &.ea {
        .question {
          margin-top: 0;
        }
      }
    }

    @media only screen and (max-width: 1399px) {
      &.gre {
        nav.question-nav {
          height: 75px;

          &.timer-hidden {
            height: 45px;
            min-height: 45px !important;
          }
        }
      }
    }

    @media only screen and (max-width: 991px), only screen and (min-width: 1199px) {
      .container {
        margin-top: $navbar-height;
        margin-bottom: 20px;
      }
    }

    @media only screen and (max-width: 1199px) {
      &:not(.gre) {
        nav.question-nav {
          position: relative;
          height: 62px;

          &.timer-hidden {
            height: 32px;
          }

          .actions-wrapper {
            .timers {
              position: absolute;
              bottom: 0;
              width: 100%;
              right: 0;
              display: flex;
              justify-content: space-between;
              background-color: #EFF8FE;
              color: black;
              padding: 6px 16px;
              height: 30px;

              &.out-of-time {
                .time {
                  color: #E66A6A;
                }
              }

              .question-time,
              .test-time {
                color: black;

                &.out-of-time {
                  .time {
                    color: #E66A6A;
                  }
                }
              }
            }
          }
        }

        &.sat {
          nav.question-nav {
            .actions-wrapper {
              .timers {
                background-color: #D7F4F1;
              }
            }
          }
        }
      }
    }

    @media only screen and (max-width: 991px) {
      &.sat {
        nav.question-nav {
          height: auto;

          .navigation-problem {
            .calculator-icon {
              position: absolute;
              bottom: 34px;
              display: flex;
              border: 1px solid white;
              width: calc(100% - 32px);
              right: 16px;
              justify-content: center;
            }
          }
        }
      }
    }

    @media only screen and (max-width: 767px) {
      @include problem-solving-mobiles;

      .container {
        padding: 0 10px;
      }

      &.gmat,
      &.ea {
        .question {
          margin-top: 0;
        }
      }

      &.gre {
        nav.question-nav .pull-right {
          .navigation-problem {
            i.fa-calculator,
            i.fa-list-ul,
            a.mark-problem i {
              padding-right: 0;
              margin-right: 0;
            }

            a.mark-problem i {
              font-size: 20px;
              vertical-align: middle;
            }

            .item { width: 17%; }

            .navigation-buttons-mobile { min-width: 32%; }

            .timers .time { min-width: auto; }
          }
        }

        &.on-review table tr.visible-mobile { display: table-row !important; }
      }

      &:not(.gre) {
        nav.question-nav {
          padding: 4px 5px;
          font-size: 14px;

          .navigation-problem {
            :first-child {
              margin-left: 0;
            }
          }

          .actions-wrapper {
            .font-switch-buttons {
              button {
                padding-left: 11px;
                padding-right: 11px;
              }
            }
          }
        }
      }
    }

    @media only screen and (max-width: 413px) {
      &:not(.gre) {
        nav.question-nav {
          padding: 4px 3px;
          font-size: 12.5px;

          .actions-wrapper {
            .item {
              padding: 0 5px;
            }

            .timers {
              padding: 6px;

              .question-time,
              .test-time {
                font-size: 12px;
              }
            }

            .navigation-problem {
              a {
                i {
                  padding: 4px 3px !important;
                }
              }
            }
          }
        }

        .actions-wrapper {
          .font-switch-buttons {
            button {
              padding-left: 10px !important;
              padding-right: 10px !important;
              font-size: 12px;
            }
          }
        }
      }
    }

    &.practice_mode {
      &.reading_comprehension {
        .toggle-sentence-numbering,
        .section-navigation {
          display: none;
        }
      }

      .answers {
        .answer {
          margin-bottom: 16px;

          label {
            margin-top: 6px;

            @media only screen and (max-width: 991px) {
              margin-top: 4px;
            }
          }
        }
      }

      &.solution-shown {
        nav.question-nav {
          height: 32px;
        }

        .question {
          .problem {
            min-height: initial !important;
          }

          .question-content > form {
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            padding-bottom: 40px;
          }

          .info-box {
            margin-top: 0;
            border: solid 2px #EBEBEB;
            border-top: none;
            border-radius: 0 0 5px 5px;
          }
        }

        .answers {
          .answer {
            label {
              .styledRadio,
              .styledCheckbox {
                &.correct,
                &.incorrect {
                  background-color: transparent !important;
                  border-color: transparent !important;

                  &::before {
                    font-weight: 100;
                  }
                }

                &.correct {
                  &::before {
                    color: $green-1 !important;
                    content: "\f00c" !important;
                  }
                }

                &.incorrect {
                  &::before {
                    content: "\f00d" !important;
                    color: $red-1 !important;
                  }
                }
              }
            }
          }
        }

        &.reading_comprehension {
          .toggle-sentence-numbering {
            display: inline-flex;
          }

          .section-navigation {
            display: block;
          }

          .info-box-content {
            p,
            span {
              font-size: 16px;
            }

            p:not(.solution-title),
            span:not(.sentence-number, .option span, .solution span, .sort-by) {
              font-weight: normal;
              line-height: 29px;
            }

            .solution {
              font-size: 16px;
            }
          }

          &.with-large-font {
            .info-box-content {
              p,
              span {
                font-size: 20px;
              }

              p:not(.solution-title),
              span:not(.sentence-number, .option span, .solution span, .sort-by) {
                font-weight: 300;
                line-height: 38px;
              }

              .interrogation_part {
                margin-bottom: 30px;
              }

              .solution {
                font-size: 20px !important;
              }
            }
          }
        }
      }
    }
  }

  &.sat {
    .modal-body {
      .btn {
        border-color: $sat-0;
        background-color: $sat-6;
      }
    }
  }

  @media (max-width: 1399px) {
    &.gre {
      nav.question-nav {
        .pull-right {
          .item {
            border-right: none !important;
          }
        }
      }
    }
  }

  @media (max-width: 991px) {
    .tooltip {
      display: none;
    }
  }
}
