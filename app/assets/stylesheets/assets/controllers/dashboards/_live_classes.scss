body[data-controller="dashboards"] {
  &[data-action="online_classes"] {
    section {
      .turbocharge-card {
        background-image: asset-url("layout/controllers/users/home/<USER>");
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        display: flex;
        justify-content: start;
        height: fit-content;
        width: 100%;
        padding: 59px 0px 48px 59px;

        .turbocharge-detail {
          width: 60%;
          color: $dark-2;

          span {
            color: #1E698F;
          }

          img.check {
            width: 13px;
            height: 13px;
            flex-grow: 0;
            object-fit: contain;
            align-self: start;
            margin-top: 7px;
            margin-right: 7px;
          }

          .learn-more-btn,
          .find-class-btn {
            text-align: center;
            border-radius: 4px;
            box-shadow: 0 4px 44px 0 rgba(88, 185, 241, 0.5);
            padding: 8px 24px;
          }

          .learn-more-btn {
            color: $dark-2;
            background-image: linear-gradient(104deg, $white 0%, #E3E3E3 100%);
          }

          .find-class-btn {
            color: $white;
            background-image: linear-gradient(101deg, #61C6FE 0%, #348ABE 100%);
          }

          .promotional-liveteach-description {
            line-height: 1.81;
            margin-top: 24px;
            margin-right: 2rem;

            .description {
              color: $grey-2;
            }
          }

          .button-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: start;
            margin-top: 24px;
          }

          .button-container a {
            margin: 0;
          }

          &.gmat-focus {
            .promotional-liveteach-description {
              .exam-title {
                color: white !important;
                text-shadow: 0 2px 6px rgba(131, 106, 17, 0.7);
                box-shadow: 0 1px 10px 0 rgba(185, 144, 1, 0.5);
                border-image-source: linear-gradient(to bottom, #F1C81B 0%, #B19017);
                background-image: linear-gradient(101deg, #8A6C01 -14%, #CEA81F 61%), linear-gradient(to bottom, #F1C81B 0%, #B19017);
                border-radius: 4px;
                font-weight: 600;
                padding-right: 0.5rem;
                padding-left: 0.5rem;
                margin-right: 0.25rem;
                margin-left: 0.25rem;
              }
            }
          }
        }

        .turbocharge-head {
          .turbocharge-title {
            line-height: normal !important;
            font-size: 36px;
          }

          &::after {
            width: 38px;
            height: 4px;
            flex-grow: 0;
            border-radius: 2px;
            background-color: $dark-1;
            content: "";
            display: block;
            margin-top: 10px;
          }
        }
      }

      &.live_classes {
        padding: 24px 0;

        .live-class-card {
          border-radius: 2px;
          box-shadow: 0 0 26px 2px rgba(129, 149, 255, 0.2);
          border: solid 1px #DCDEEB;
          background-color: $white;
          margin-bottom: 27px;

          .card-header {
            padding: 21.6px 22px 21.9px 22px;
            border: solid 1px $grey-6;
            background-color: $white;
          }

          .card-body {
            background-image:
            asset-url(
                "layout/controllers/users/home/<USER>"
              );
            background-size: cover;
            background-position: top right;
            background-repeat: no-repeat;
            padding: 23px 21px;

            .card-date {
              padding: 0.5px 9px 0.5px 5.2px;
              border-radius: 4px;
              background-image: linear-gradient(92deg, #505FBC 11%, #719FDC 117%);
              color: $white;
              width: max-content;
            }
          }

          .live-class-title-card {
            line-height: 1.33;
            color: $dark-1;

            .title {
              margin: 0 0 0 14px;
              font-weight: 600;
            }

            .fas {
              font-weight: 300;
            }
          }

          .session-title {
            text-align: left;
            color: $blue-quant-2;
            margin: 17px 0 6px 0;
            line-height: 1.33;
          }

          .session-detail {
            text-align: left;
            color: #545E64;
            line-height: 1.5;

            .heading {
              color: $blue-quant-2;
            }
          }
        }

        ul.online-class-sessions-list {
          li {
            .status {
              width: 22px;
              height: 22px;
              line-height: 22px;
              border-radius: 50%;
              text-align: center;
              font-size: 10px;
              z-index: 2;
              border: 2px solid $grey-6;
              background-color: $light-4;

              &.completed {
                border: none;
                color: $white;
                background-color: $gre-2;
              }
            }

            .next-item-indicator {
              position: absolute;
              top: calc(50% - 9px);
              left: 1px;
              background-image: image-url("layout/controllers/users/home/<USER>");
              width: 55px;
              height: 22px;
              object-fit: contain;
              border-radius: 4.3px;
              color: $white;
              font-size: 14.1px;
              font-weight: 600;
              background-repeat: no-repeat;
              z-index: 9;
              padding-top: 1px;
              padding-left: 6px;
            }

            &.online-class-session {
              position: relative;

              .bar {
                position: absolute;
                height: 130%;
                width: 1px;
                left: 10px;
                top: 50%;
                z-index: 1;

                &.completed {
                  background-color: $gre-2;
                }
              }

              > .status {
                position: absolute;
                top: 50%;
                left: 0;
              }

              .classes-sessions-detail-sections {
                padding-left: 27px;

                .schedule-card {
                  box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
                  border: 1px solid;
                  background-color: $white;
                  margin-bottom: 27px;
                  position: relative;
                  border-radius: 4px;
                  border-image-slice: 1;
                  background-origin: border-box;
                  background-clip: content-box, border-box;
                  border-image-source: linear-gradient(to left, $grey-6 71%, #C4CCF2 30%);
                  background-image: linear-gradient(to right, #F3F4FC -4%, #FFF 9%, #FFF 95%, #F3F4FC 100%), linear-gradient(to left, $grey-6 71%, #C4CCF2 30%);

                  &.active {
                    border-image-source: linear-gradient(106deg, #886AFF 10%, #7BDFFF 31%, #7BC7FF 77%, #7D7BFF 100%);
                    background-image: linear-gradient(to right, #F3F4FC -4%, #FFF 9%, #FFF 95%, #F3F4FC 100%), linear-gradient(106deg, #886AFF 10%, #7BDFFF 31%, #7BC7FF 77%, #7D7BFF 100%);

                    .schedule-card-title {
                      color: $blue-quant-2;
                    }

                    .schedule-time {
                      color: $blue-quant-2;
                    }
                  }

                  &::before {
                    width: 6px;
                    height: 100%;
                    position: absolute;
                    left: 0;
                    content: "";
                    background: #C9D0DE;
                    display: block;
                    border-radius: 4px 0 0 4px;
                    top: 0;
                  }

                  &.active::before {
                    background-image: linear-gradient(175deg, #9975FF 4%, rgba(66, 207, 251, 0.41) 75%);
                  }

                  .schedule-date-card {
                    border-right: 1px solid $grey-6;
                    padding: 10px;

                    .schedule-time {
                      text-align: left;
                      line-height: 1.38;
                      color: #545E64;
                      margin: 0;
                      width: 120px;

                      span {
                        color: $gmat-1;
                      }
                    }
                  }

                  .schedule-card-session {
                    padding-left: 29px;
                    display: -webkit-box !important;
                    -webkit-line-clamp: 10;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    p {
                      margin-bottom: 0;
                    }

                    .schedule-card-title {
                      text-align: left;
                      color: $dark-1;
                      margin-bottom: 10px !important;
                    }
                  }

                  .row {
                    padding: 15px 30px;
                  }
                }
              }

              &:first-child {
                > .status {
                  top: 50%;
                }
              }

              &:last-child {
                .online-class-session-seperator-line {
                  display: none;
                }

                .bar {
                  display: none;
                }
              }

              @media only screen and (max-width: 991px) {
                .bar {
                  height: 124%;
                }

                > .status {
                  left: 0;
                }

                .classes-sessions-detail-sections {
                  padding-left: 34px;
                }
              }

              @media only screen and (max-width: 767px) {
                .bar {
                  left: 13px;
                }

                > .status {
                  left: 3px;
                }

                .schedule-card {
                  margin-left: 0;
                }
              }
            }
          }
        }

        .schedule-download-btn {
          padding: 6px 11px 4px 6px;
          border-radius: 2px;
          border: solid 1px $gmat-1;
          text-align: center;
          color: $gmat-1;
          width: max-content;
          background-color: transparent;
          min-width: 185px;
          text-transform: uppercase;
        }

        .cohort-card {
          .card-body {
            background-image: unset;

            .exam-logo {
              justify-content: center;
              display: flex;

              span {
                text-shadow: 0 1.1px 3.3px rgba(131, 106, 17, 0.7);
                background: linear-gradient(101deg, #8A6C01 -14%, #CEA81F 61%), linear-gradient(to bottom, #F1C81B 0%, #B19017);
                border-radius: 2px;
                text-transform: capitalize;
                font-weight: 600;
                font-size: 14px;
                color: $white;
                padding: 1px 8px;
              }
            }

            .exam-chip-title {
              color: $blue-quant-2;
            }

            .profile {
              margin: 23px 0 25px 0;

              .profile-title {
                color: $dark-1;
              }

              .profile-dis {
                color: #545E64;
              }

              img.instructor-img {
                width: 50px;
                max-height: 50px;
                border-radius: 100%;
              }
            }

            .email-btn {
              padding: 2px 16px;
              border-radius: 2px;
              background-color: $primary-2;
              color: $white;
              text-transform: uppercase;
              width: 100%;
              height: 30px;
              border: 0;
            }
          }
        }
      }
    }

    @media (min-width: 768px) and (max-width: 991px) {
      section {
        .turbocharge-card {
          background-image: asset-url("layout/controllers/users/home/<USER>");
          background-position: bottom;
          padding: 42px 0px 38px 40px;

          .turbocharge-detail {
            width: 60%;

            .promotional-liveteach-description {
              margin-top: 24px;
            }

            .button-container {
              text-align: center;
              margin-top: 10px;
            }

            .button-container a {
              margin-right: 0 !important;
            }
          }

          .turbocharge-head {
            .turbocharge-title {
              font-size: 30px;
              width: 118%;
            }
          }
        }
      }
    }

    @media only screen and (max-width: 767px) {
      section {
        .turbocharge-card {
          background-image: asset-url("layout/controllers/users/home/<USER>");
          background-position: bottom;
          width: 94%;
          padding: 48px 31px 374px 31px;

          .turbocharge-detail {
            width: 100%;
            padding-bottom: 6rem;

            .promotional-liveteach-description {
              margin-top: 60px;
            }

            .button-container {
              display: grid;
              text-align: center;
              justify-content: center;
              margin-top: 24px;
            }

            .button-container a {
              margin-right: 0 !important;
            }

            .learn-more-btn,
            .find-class-btn {
              padding: 10px 0;
              width: 243px;
            }
          }

          .turbocharge-head {
            .turbocharge-title {
              font-size: 30px;
            }
          }
        }
      }
    }
  }
}
