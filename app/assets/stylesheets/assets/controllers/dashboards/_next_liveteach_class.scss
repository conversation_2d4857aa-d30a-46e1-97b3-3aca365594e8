body[data-controller="dashboards"] {
  &[data-action="next_liveteach_class"] {
    background-color: $light-1;

    section.next-liveteach-class {
      margin-bottom: auto;

      .next-liveteach-class-header {
        width: 100%;
        box-shadow: 0 2px 2px 0 rgba(183, 195, 209, 0.1);
        background-color: $light-5;
        padding: 16px 0 18px 0;
      }

      .cohort-card {
        .instructor-info {
          align-items: center;
          padding-right: 12px;

          .back-arrow {
            height: 79px;

            .back-link {
              color: $blue-3;
              height: 100%;
              padding-right: 27px;
              display: flex;
              align-items: center;
              justify-content: center;

              &:hover {
                color: $primary-2;
              }
            }
          }

          .instructor-image {
            width: 49px;
            height: 49px !important;
            border: solid 1px #6967CC;
            border-radius: 50%;
          }

          .instructor-name {
            padding-left: 11px;

            h2 {
              color: $dark-2;
              margin-bottom: 5px;
            }

            .see-other-cohorts {
              color: $blue-3;
              font-size: 16px;
              display: flex;
              align-items: center;

              i {
                margin-right: 8px;
              }

              .underline {
                text-decoration: underline;
                font-weight: 600;

                &:hover {
                  color: $primary-3;
                }
              }
            }

            .instructor-title {
              display: none;
            }
          }
        }

        .schedule-info {
          background-color: #E8F6FF;
          padding: 14px 12px 15px 12px;
          border-radius: 6px;
          min-height: 46px;
          display: flex;
          align-items: center;
          margin-right: 29px;

          .date-range {
            color: $blue-2;
            font-size: 18px;
            display: flex;
            align-items: center;
            padding-right: 15px;
            margin-right: 19px;
            position: relative;

            &::after {
              content: "";
              display: inline-block;
              width: 1px;
              height: 31px;
              background-color: $gmat-4;
              position: absolute;
              right: 0;
            }

            .start-date {
              margin-left: 14px;
            }

            .arrow {
              margin: 0 9px 0 9px;
            }

            .calendar-icon {
              margin-bottom: 1px !important;
            }
          }

          .class-timing {
            color: $grey-1;
            font-size: 16px;
            line-height: 1.38;
          }
        }

        .office-hours {
          margin-left: auto;
          position: relative;
          padding-left: 26px;

          &::before {
            content: "";
            display: inline-block;
            width: 1px;
            height: 78px;
            background-color: $grey-6;
            position: absolute;
            left: 0;
          }

          .office-title {
            font-size: 13px;
            color: $grey-1;
            margin-bottom: 11px;
            font-weight: bold;
            letter-spacing: 0.13px;
            text-transform: uppercase;
          }

          .office-schedule {
            background-color: #E8F6FF;
            border-radius: 6px;

            .day-time {
              padding: 5px 22px 5px 18px;
              position: relative;
              white-space: nowrap;

              &::after {
                content: "";
                width: 1px;
                height: 40px;
                background-color: #CDE5F3;
                position: absolute;
                right: -1px;
                top: 6px;
              }

              .weekly-office-day {
                align-items: center;
                font-size: 16px;
                font-weight: 600;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.33;
                letter-spacing: normal;
                text-align: left;
                gap: 10px;
                margin-bottom: 3px;
                color: $grey-1;

                img {
                  color: $blue-2;
                }
              }

              .weekly-office-time {
                align-items: center;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.33;
                letter-spacing: normal;
                text-align: left;
                color: $grey-1;
                gap: 10px;

                img {
                  color: $blue-2;
                }
              }
            }

            .instructors {
              padding: 5px 18px 5px 18px;
              width: calc(100% - 215px);
              font-size: 13px;
              font-weight: 600;
              font-stretch: normal;
              font-style: normal;
              line-height: 1.33;
              letter-spacing: normal;
              text-align: left;
              color: #000;

              .instructor-avatars {
                display: flex;
                justify-content: flex-start;
                margin-top: 5px;
                width: 104px;
                height: 20.8px;
                object-fit: contain;

                img {
                  width: 20.8px;
                  box-shadow: 0 1.1px 2.3px 0 rgba(187, 153, 97, 0.6);
                  border: solid 0.5px #FDE6C0;
                  background-color: $white;
                  margin-left: -3px;
                  object-fit: cover;
                }
              }
            }
          }
        }
      }

      .next-liveteach-class-body {
        background-color: $light-1;
        position: relative;
        padding-bottom: 30px;

        .disabled {
          color: $grey-5;
          cursor: not-allowed;
          text-decoration: none;
        }

        .online-class-details {
          ul.online-class-sessions-list {
            .online-class-sessions-list-group {
              position: relative;
              padding-top: 26px;

              .accordion-item {
                background-color: transparent;
                border: none;
                width: 100%;
                display: inline-block;
              }
            }

            .status {
              position: absolute;
              left: -15px;
              top: 71px;
              background: $light-1;
              width: 22px;
              height: 22px;
              font-size: 12px;
              border-radius: 50%;
              color: rgba(255, 255, 255, 0);
              display: flex;
              justify-content: center;
              align-items: center;
              border: 2px solid $grey-5;

              &.completed {
                background: linear-gradient(17deg, rgba(71, 172, 127, 1) 0%, rgba(118, 255, 214, 1) 88%);
                color: rgba(255, 255, 255, 1);
                border: none;
              }
            }

            li {
              &.online-class-session {
                position: relative;

                > .status {
                  position: absolute;
                  top: 50%;
                  left: 0;
                }

                .classes-sessions-detail-sections {
                  padding-left: 23px;

                  .schedule-card {
                    box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
                    border: 1px solid;
                    background-color: $white;
                    position: relative;
                    border-radius: 4px;
                    border-image-slice: 1;
                    background-origin: border-box;
                    background-clip: content-box, border-box;
                    border-image-source: linear-gradient(to left, $grey-6 71%, #C4CCF2 30%);
                    background-image: linear-gradient(to right, #F3F4FC -4%, #FFF 9%, $white 95%, #F3F4FC 100%), linear-gradient(to left, $grey-6 71%, #C4CCF2 30%);

                    &.session-expanded,
                    &:hover {
                      border-color: #4243FB;
                      border-image-source: none;
                    }

                    &::before {
                      width: 6px;
                      height: 100%;
                      position: absolute;
                      left: 0;
                      content: "";
                      background: #C9D0DE;
                      display: block;
                      border-radius: 4px 0 0 4px;
                      top: 0;
                    }

                    &.active::before {
                      background-image: linear-gradient(175deg, #9975FF 4%, rgba(66, 207, 251, 0.41) 75%);
                    }

                    .schedule-date-card {
                      border-right: 1px solid $grey-6;
                      padding-top: 5px;
                      width: auto;
                      padding-right: 20px;

                      .schedule-card-title,
                      .schedule-card-title-mobile {
                        padding: 3px 7px;
                        border-radius: 4px;
                        background-color: #E8F3F7;
                        text-align: center;
                        color: $dark-2;
                        font-size: 16px;
                        font-weight: 600;
                        margin-bottom: 12px;
                        display: inline-block;
                      }

                      .schedule-card-title-mobile {
                        display: none;
                      }

                      .schedule-time {
                        text-align: left;
                        color: #545E64;
                        margin: 0;
                        font-size: 13px;
                        font-weight: 600;
                        line-height: 1.69;

                        span {
                          color: $gmat-1;
                        }
                      }
                    }

                    .schedule-card-session {
                      padding-left: 24px;
                      padding-top: 5px;
                      display: -webkit-box !important;
                      -webkit-line-clamp: 10;
                      -webkit-box-orient: vertical;
                      overflow: hidden;
                      text-overflow: ellipsis;

                      .schedule-card-session-title-block {
                        min-height: 100%;
                      }

                      .session-details {
                        padding-top: 3px;
                      }

                      .actions-links-mobile {
                        line-height: 1;

                        .custom-dropdown {
                          line-height: 1.5;
                          padding: 0;
                          transform: translate3d(-131px, 0, 0) !important;
                          border: none;
                          box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.3);
                          width: 240px;
                          inset: auto !important;

                          a,
                          button,
                          > div {
                            padding-top: 10px;
                            padding-bottom: 10px;
                            display: flex;
                            align-items: center;

                            &:first-child {
                              padding-top: 18px;
                            }

                            &:last-child {
                              padding-bottom: 18px;
                            }
                          }

                          a,
                          button {
                            &:active {
                              color: initial;
                              background-color: $grey-8;
                            }
                          }

                          .icon-container {
                            flex: 0 0 20px;
                            text-align: center;
                            line-height: 1;

                            .see-session-files {
                              font-size: 20px;
                              color: $blue-2;

                              &.disabled {
                                color: $grey-5;
                                cursor: not-allowed;
                              }
                            }
                          }

                          .icon-text {
                            padding-left: 21px;
                            color: $grey-1;

                            &.disabled {
                              color: $grey-5;
                              cursor: not-allowed;
                            }
                          }
                        }
                      }

                      p {
                        margin-bottom: 0;
                        color: #545E64;
                        font-size: 16px;
                        line-height: 1.5;
                      }

                      .schedule-card-title {
                        text-align: left;
                        color: $blue-2;
                        margin-bottom: 10px !important;
                        font-size: 18px;
                        font-weight: 600;
                        line-height: 1.33;
                      }
                    }

                    .schedule-data-btn-group {
                      margin-left: auto;

                      .session-files {
                        padding: 6px 7px;
                        border-radius: 2px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        &:hover {
                          background-color: $blue-5;
                        }
                      }

                      .see-session-files {
                        max-width: 22px;
                        color: $blue-2;

                        &.see-session-files {
                          &.disabled {
                            color: $grey-5;
                            cursor: not-allowed;
                          }
                        }
                      }

                      button.session-resources-icon-group {
                        border: none;
                        background-color: transparent;
                      }

                      .session-resources-icon-group {
                        margin-left: 10px;
                        padding-top: 2px;
                        padding-bottom: 4px;

                        &.zoom-meet-icon {
                          padding: 4px 6px;
                        }

                        img {
                          min-width: 22px;
                          width: 22px;
                        }

                        &:hover {
                          background-color: $blue-5;
                        }
                      }

                      .border-btn-group {
                        width: 1px;
                        height: 64.8px;
                        background-color: $grey-6;
                        margin: 0 0 0 25px;
                      }

                      .accordion-button {
                        background-color: transparent;
                        box-shadow: none;
                      }
                    }

                    .row {
                      padding: 15px 30px;
                    }

                    &.active {
                      border-image-source: linear-gradient(106deg, #886AFF 10%, #7BDFFF 31%, #7BC7FF 77%, #7D7BFF 100%);
                      background-image: linear-gradient(to right, #F3F4FC -4%, $white 9%, $white 95%, #F3F4FC 100%), linear-gradient(106deg, #886AFF 10%, #7BDFFF 31%, #7BC7FF 77%, #7D7BFF 100%);

                      .schedule-date-card {
                        .schedule-card-title,
                        .schedule-card-title-mobile {
                          color: $blue-2;
                        }
                      }
                    }
                  }

                  .session-accordion {
                    border-radius: 4px;
                    background-image: linear-gradient(to top, #EFF1F5, #E7E9EE);
                    padding: 0 30px 0 59px;

                    &.no-session-found {
                      padding: 0 30px 0 20px;
                    }

                    &.accordion-collapse.collapse,
                    &.accordion-collapse.collapsing {
                      transition: height 0.3s ease-in-out;
                    }

                    .accordion-body {
                      padding: 0;

                      .session-homework-head {
                        margin-bottom: 15px;
                        display: flex;
                        align-items: center;
                        font-size: 14px;
                        font-weight: 600;
                        line-height: 1.33;
                        color: $grey-1;
                        padding-top: 24px;

                        span {
                          white-space: nowrap;
                          text-transform: uppercase;
                        }

                        .border-session {
                          border-bottom: solid 1px #C7D3DD;
                          height: 1px;
                          margin-left: 30px;
                          width: 100%;
                        }
                      }

                      &:not(.completed) {
                        .session-homework {
                          &.next-task {
                            .next-item-indicator {
                              display: block;
                              padding: 2px 6px 2px 1px;
                              font-size: 10px;
                              line-height: 14px;
                              font-weight: 600;
                              text-transform: uppercase;
                              color: $white;
                              position: absolute;
                              top: 28px;
                              left: -47px;
                              border-radius: 2px;
                              border-top-right-radius: 0;
                              border-bottom-right-radius: 0;
                              background-image: asset-url("layout/controllers/online_classes/next_arrow_icon.svg");
                              background-size: contain;
                              background-repeat: no-repeat;
                              object-fit: contain;
                              width: 51px;
                              text-align: center;
                              z-index: 9;
                            }

                            &::before {
                              content: " ";
                              display: block;
                              width: 2px;
                              position: absolute;
                              top: 10%;
                              bottom: 0;
                              left: -24px;
                              background-color: $grey-6;
                            }
                          }

                          &.next-task-shown {
                            &::before {
                              content: " ";
                              display: block;
                              width: 2px;
                              position: absolute;
                              top: 0;
                              bottom: 0;
                              left: -24px;
                              background-color: $grey-6;
                            }
                          }
                        }
                      }

                      &.completed .homework-group > .bar,
                      .homework-group > .bar.highlighted {
                        &::before {
                          content: " ";
                          display: block;
                          position: absolute;
                          top: 0;
                          bottom: 0;
                          width: 2px;
                          background-color: #57C89C;
                        }
                      }

                      &.completed .homework-group:last-child > .bar {
                        height: 95px;
                      }

                      .homework-group {
                        margin-bottom: 45px;
                        position: relative;

                        .bar {
                          position: absolute;
                          display: block;
                          left: -24px;
                          top: -57px;
                          bottom: 0;
                          background-color: $grey-6;
                          width: 2px;
                        }

                        .indicator {
                          position: absolute;
                          display: block;
                          border: 5px solid $grey-8;
                          border-radius: 50%;
                          width: 30px;
                          height: 30px;
                          background-color: $blue-2;
                          left: -38px;
                          top: 10px;
                          line-height: 20px;
                          text-align: center;
                          color: $white;
                          font-size: 15px;
                          text-transform: uppercase;
                          z-index: 1;

                          &::before {
                            content: " ";
                            display: block;
                            position: absolute;
                            width: 32px;
                            height: 32px;
                            border: 2px solid $grey-6;
                            border-radius: 50%;
                            left: -6px;
                            top: -6px;
                          }

                          &.highlighted {
                            &::before {
                              border-color: #57C89C;
                            }
                          }
                        }

                        .group-header {
                          border-radius: 2px;
                          box-shadow: 0 4px 13px 0 rgba(183, 195, 209, 0.5);
                          border: solid 1px $grey-6;
                          background-image: linear-gradient(to right, #E9EDFF -5%, $white 12%, #E9F0F5 47%, #E9EDFF 71%);
                          background-origin: border-box;
                          background-clip: content-box, border-box;
                          position: relative;
                          display: flex;
                          align-items: center;
                          z-index: 9;

                          &::after {
                            position: absolute;
                            right: 145px;
                            top: 5px;
                            content: "";
                            background-image: asset-url("layout/controllers/online_classes/dots_3x4_white.svg");
                            width: 39px;
                            height: 27px;
                            background-size: cover;
                            background-position: top;
                            background-repeat: no-repeat;
                            opacity: 0.9;
                          }

                          .group-name {
                            margin: 15px 0 15px 20px;
                            font-size: 18px;
                            font-weight: 600;
                            line-height: 1.44;
                            text-align: left;
                            color: $blue-2;
                          }
                        }
                      }

                      .session-homework {
                        position: relative;

                        .header {
                          padding: 16px 17px 16px 29px;
                          background-color: $white;
                          display: flex;
                          border-top: 1px solid $grey-7;

                          &:hover {
                            background-color: $blue-5;
                          }

                          .header-info {
                            flex: auto;
                            display: flex;
                            align-items: center;

                            .status-icon {
                              position: relative;
                              align-self: center;

                              .icon-container {
                                width: 26px;
                                height: 26px;
                                background-color: $grey-8;
                                border-radius: 50%;
                                text-align: center;
                                line-height: 26px;
                                position: absolute;
                                top: 2px;
                                left: 2px;
                                box-shadow: 0 3px 15px 0 rgba(183, 195, 209, 0.4);
                                color: #5E5CB0;

                                i {
                                  position: absolute;
                                  bottom: 5.5px;
                                  right: 4.5px;
                                  font-size: 14px;
                                  color: $grey-5;

                                  &.completed {
                                    color: #57C89C;
                                  }

                                  &.check-circle-icon {
                                    bottom: -5px;
                                    right: -3px;
                                    background-color: $white;
                                    border-radius: 50%;
                                  }
                                }
                              }

                              .knob-container {
                                width: 30px;
                                height: 30px;
                              }
                            }

                            .header-title {
                              margin-bottom: 0;
                              margin-left: 13px;
                              font-size: 16px;
                              line-height: 1.33;
                              color: $grey-2;
                              display: flex;
                              flex: auto;
                              align-items: center;
                              padding: 10px 0;

                              p {
                                font-weight: inherit;
                                margin: 0;
                              }
                            }

                            .status {
                              padding: 0 10px;
                              flex: 0 0 auto;
                              display: flex;
                              align-items: center;
                              line-height: 0;
                              color: $grey-5;
                              text-transform: uppercase;
                              font-size: 13px;

                              span {
                                font-size: 13px;
                                font-weight: 600;
                                color: $grey-4;
                              }

                              &::after {
                                width: 8px;
                                height: 8px;
                                content: "";
                                background-color: $gre-2;
                                display: block;
                                margin: 0 5px;
                                border-radius: 100%;
                              }
                            }
                          }

                          .knob-container {
                            width: 23px;
                            height: 23px;
                          }

                          .completion-status {
                            &::after {
                              width: 8px;
                              height: 8px;
                              content: "";
                              background-color: $gre-2;
                              display: block;
                              margin: 0 5px;
                              border-radius: 100%;
                            }

                            &.completed {
                              &::after {
                                background-color: $grey-5;
                              }
                            }

                            .text-completed {
                              color: $grey-2 !important;
                              font-size: 13px;
                              font-weight: 600;
                              line-height: 1.33;
                              white-space: nowrap;
                            }

                            .tasks-completed {
                              color: #57C89C;
                            }
                          }

                          .arrows {
                            flex: 0 0 50px;
                            font-size: 16px;
                            color: $grey-4;
                            text-align: center;
                            padding: 0 20px;
                            display: flex;
                            align-items: center;

                            .fa-chevron-down {
                              display: none;

                              &:hover {
                                background-color: $white;
                                border-radius: 2px;
                              }
                            }

                            .fa-chevron-up {
                              padding: 10px;

                              &:hover {
                                background-color: $white;
                                border-radius: 2px;
                              }
                            }
                          }

                          &.collapsed {
                            .arrows {
                              .fa-chevron-down {
                                padding: 10px;
                                display: inline-block;
                              }

                              .fa-chevron-up {
                                display: none;
                              }
                            }
                          }
                        }

                        .main-content {
                          > .content {
                            padding: 30px 50px;
                            background-color: $white;
                            border-radius: 0 0 4px 4px;

                            .chapter-test-link {
                              text-decoration: underline;
                              text-decoration-color: $blue-5;
                            }

                            .homework-task-comments {
                              display: flex;
                              gap: 20px;
                              margin-bottom: 25px;

                              .instructor-image {
                                width: 41px;
                                height: 41px !important;
                                padding: 1px;
                                border-radius: 50%;
                                background: linear-gradient(to bottom, #6967CC, #9ED6FF);
                              }

                              .comment-info {
                                background-color: #EFFEFF;
                                border: solid 1px #B2E0E3;
                                padding: 6px 32px 7px 21px;
                                border-radius: 5px;
                                width: calc(100% - 41px);
                                position: relative;

                                .comment-box-left-arrow {
                                  width: 22px;
                                  position: absolute;
                                  left: -13px;
                                  top: 10px;

                                  img {
                                    max-width: 100%;
                                  }
                                }

                                .comment-box-top-arrow {
                                  width: 22px;
                                  position: absolute;
                                  left: 13px;
                                  top: -15px;

                                  img {
                                    max-width: 100%;
                                  }
                                }

                                .instructor-name {
                                  font-size: 16px;
                                  font-weight: bold;
                                  color: #114577;
                                  line-height: 1.5;
                                }

                                .comment {
                                  font-size: 16px;
                                  font-weight: normal;
                                  color: #114577;
                                  line-height: 1.5;
                                }
                              }
                            }

                            .my-task-takeaways {
                              text-align: right;

                              .takeways-link {
                                color: $blue-3;
                                font-size: 15px;
                                font-weight: 600;
                                text-decoration: underline;
                                text-decoration-color: $primary-3;

                                .open-file-icon {
                                  margin-right: 8px;
                                }

                                &:hover {
                                  color: $blue-1;
                                }
                              }
                            }

                            > label,
                            .btn {
                              margin-top: 20px;
                              display: block;
                              width: -moz-fit-content;
                              width: fit-content;
                              text-decoration: none;
                            }

                            .mark-complete-btn {
                              width: 200px;

                              > label,
                              .btn {
                                margin-top: 0;
                              }

                              .awesome-checkbox-label {
                                color: $blue-3;
                                border-color: $blue-3;

                                &:hover {
                                  color: $blue-1;
                                  border-color: $blue-1;
                                }
                              }
                            }

                            > .title {
                              font-weight: 600;
                              color: $grey-1;
                              margin-bottom: 16px;
                            }

                            > a,
                            p > a {
                              color: $primary-1 !important;
                              text-decoration: underline;
                            }
                          }
                        }

                        .tests-list {
                          background-image: url("layout/controllers/online_classes/homework_task_bg_shadow.webp");
                          background-color: $grey-10;
                          padding: 24px 60px 17px 43px;
                          background-repeat: no-repeat;
                          background-size: contain;

                          .subtitle {
                            color: $grey-2;
                            font-size: 16px;
                            margin-bottom: 6px;
                          }

                          .test-item {
                            display: flex;
                            align-items: center;
                            padding: 15px 0;
                            border-top: 1px solid $grey-6;
                            justify-content: space-between;

                            > .title {
                              padding-right: 20px;
                              color: $dark-1;
                              margin-bottom: 0;
                              display: flex;
                              align-items: center;

                              .test-incomplete {
                                width: 17px;
                                min-width: 17px;
                                height: 17px;
                                background: $grey-7;
                                border-radius: 50%;
                                color: rgba(255, 255, 255, 0);
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                border: 1px solid $grey-5;
                                margin-right: 22px;
                                font-size: 12px;

                                &.test-completed {
                                  background: linear-gradient(17deg, rgba(71, 172, 127, 1) 0%, rgba(118, 255, 214, 1) 88%);
                                  color: rgba(255, 255, 255, 1);
                                  border: none;
                                }
                              }
                            }

                            > .reading-title {
                              width: calc(100% - 134px);
                            }

                            > .accuracy {
                              display: flex;
                              align-items: center;

                              .knob-container {
                                flex: 0 0 auto;
                                height: 20px;
                                width: 20px;
                                margin-right: 15px;

                                .tasks-completed {
                                  color: #57C89C;
                                }
                              }

                              .your-accuracy {
                                flex: 0 0 auto;
                                margin-right: 10px;
                                color: $grey-2;
                              }

                              .your-accuracy-title {
                                flex: 1 0 auto;
                                color: $grey-3;
                              }
                            }

                            > .reading-link-container {
                              width: 134px;
                              white-space: nowrap;
                            }

                            > .link-container {
                              text-align: right;
                              padding: 4px 7px 4px 13px;

                              &:hover {
                                background-color: $blue-5;
                                border-radius: 2px;
                              }

                              a {
                                text-decoration: none;
                                color: $blue-3;

                                span {
                                  vertical-align: middle;
                                }

                                i {
                                  vertical-align: middle;
                                  margin-left: 10px;
                                  font-size: 12px;
                                }
                              }
                            }
                          }
                        }

                        .body {
                          padding: 33px 50px 32px 51px;

                          .description {
                            font-size: 16px;
                            line-height: 1.5;
                            color: #545E64;
                            margin-bottom: 27px;
                          }
                        }

                        &.active {
                          border: 1px solid $blue-2;
                          border-radius: 4px;

                          > .header {
                            background-color: #EDF6FB;
                            border-radius: 4px 4px 0 0;

                            .header-title {
                              color: $blue-3;
                              font-weight: 600;
                            }
                          }

                          &.next-task {
                            &::before {
                              left: -25px;
                              bottom: -1px;
                              top: 10%;
                            }
                          }

                          &.next-task-shown {
                            &::before {
                              top: -1px;
                              bottom: -1px;
                              left: -25px;
                            }
                          }
                        }
                      }
                    }
                  }

                  .online-class-session-recording {
                    .modal-header {
                      .bookmark-icon-link {
                        .bookmark-icon-group {
                          padding: 3px 6px 0 6px;

                          &:hover {
                            background-color: $blue-5;
                          }
                        }

                        &.flagged i.far {
                          display: none;
                        }

                        &:not(.flagged) i.fas {
                          display: none;
                        }
                      }

                      .modal-title {
                        margin-left: 16px;
                      }
                    }
                  }
                }

                &:first-child {
                  > .status {
                    top: 50%;
                  }
                }

                &:last-child {
                  .online-class-session-seperator-line {
                    display: none;
                  }
                }
              }
            }
          }
        }
      }

      @media (min-width: 768px) and (max-width: 1200px) {
        .next-liveteach-class-header {
          .cohort-card {
            .instructor-info {
              .back-arrow {
                .back-link {
                  padding-right: 20px;
                }
              }
            }

            .schedule-info.weekly-office-hour-active {
              margin-right: 0;
              padding: 8px 10px;
              flex-direction: column;
              align-items: flex-start;
              margin-left: auto;

              .date-range {
                white-space: nowrap;
                margin-right: 0;
                padding-right: 0;
                width: 100%;
                margin-bottom: 16px;
                padding-bottom: 11px;

                &::after {
                  width: 100%;
                  height: 1px;
                  bottom: 0;
                }
              }
            }

            .weekly-office-hour-inactive {
              padding: 8px 12px 8px 12px;
              margin-right: 0;
              margin-left: auto;

              .date-range {
                margin-right: 16px;
              }
            }

            .office-hours {
              padding-left: 17px;
              margin-left: 17px;

              &::before {
                height: 115px;
              }

              .office-title {
                margin-bottom: 4px;
              }

              .office-schedule {
                flex-direction: column;
                padding: 4px 12px;
                width: 201px;

                .day-time {
                  padding: 5px 0 6px 0;
                  border-bottom: solid 1px $gmat-4;
                  margin-bottom: 1px;

                  .weekly-office-day {
                    gap: 8px;

                    img {
                      font-size: 11px;
                    }
                  }

                  .weekly-office-time {
                    gap: 8px;

                    img {
                      width: 11px;
                    }
                  }

                  &::after {
                    display: none;
                  }
                }

                .instructors {
                  width: 100%;
                  padding: 5px 0 5px 0;
                  display: flex;
                  align-items: center;
                  gap: 10px;

                  .instructor-avatars {
                    justify-content: flex-start;
                    margin-top: 3px;
                  }
                }
              }
            }
          }
        }

        .online-class-details {
          ul.online-class-sessions-list {
            li.online-class-session {
              .classes-sessions-detail-sections {
                .schedule-card {
                  .schedule-card-session {
                    max-width: calc(100% - 286px);
                  }

                  .schedule-data-btn-group {
                    width: 142px;
                    justify-content: flex-end;

                    .accordion-button {
                      width: auto;
                    }

                    &.file-icon-hidden {
                      width: 125px;
                    }
                  }
                }
              }
            }
          }
        }
      }

      @media (min-width: 768px) and (max-width: 992px) {
        .next-liveteach-class-header {
          .cohort-card {
            .schedule-info.weekly-office-hour-active {
              width: 183px;
            }
          }
        }

        .next-liveteach-class-body {
          .online-class-details {
            ul.online-class-sessions-list {
              li.online-class-session {
                .classes-sessions-detail-sections {
                  padding-left: 25px;

                  .schedule-card {
                    .schedule-date-card {
                      padding-right: 12px;
                      width: 118px;

                      .schedule-card-title {
                        margin-bottom: 10px;
                      }
                    }

                    .schedule-card-session {
                      max-width: calc(100% - 263px);
                    }
                  }

                  .session-accordion {
                    .accordion-body {
                      .session-homework {
                        .header {
                          padding-right: 0;

                          .header-info {
                            .header-title {
                              max-width: 338px;
                            }
                          }

                          .arrows {
                            padding-left: 20px;
                            padding-right: 10px;
                          }
                        }

                        .main-content {
                          .content {
                            padding: 20px 35px;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      @media (max-width: 991px) {
        .next-liveteach-class-body {
          .online-class-details {
            ul.online-class-sessions-list {
              li.online-class-session {
                .classes-sessions-detail-sections {
                  .schedule-card {
                    .schedule-date-card {
                      .schedule-card-title-mobile {
                        display: inline-block;
                      }

                      .schedule-card-title {
                        display: none;
                      }
                    }

                    .schedule-data-btn-group {
                      .session-files {
                        &:hover {
                          background-color: transparent;
                        }
                      }

                      .session-resources-icon-group {
                        &:hover {
                          background-color: transparent;
                        }
                      }
                    }
                  }

                  .online-class-session-recording {
                    .modal-header {
                      .bookmark-icon-link {
                        .bookmark-icon-group {
                          &:hover {
                            background-color: transparent;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      @media only screen and (max-width: 767px) {
        .next-liveteach-class-header {
          .cohort-card {
            flex-direction: column;
            align-items: flex-start !important;

            .instructor-info {
              margin-bottom: 10px;

              .instructor-name {
                padding-top: 18px;

                .instructor-title {
                  display: block;
                }

                .see-other-cohorts {
                  display: none;
                  color: #545E64;
                }
              }
            }

            .schedule-info {
              width: 94%;
              margin: 0 auto;

              .date-range {
                white-space: nowrap;

                &::after {
                  display: inline-block;
                }

                .end-date {
                  padding-right: 12px;
                }
              }
            }

            .office-hours {
              border-top: solid 1px $grey-6;
              padding-top: 22px;
              padding-left: 0;
              width: 94%;
              margin: 21px auto 0 auto;

              &::before {
                display: none;
              }

              .office-schedule {
                padding-right: 8px;

                .day-time {
                  padding: 5px 12px 5px 10px;
                }

                .instructors {
                  padding: 5px 8px 5px 12px;
                  width: calc(100% - 200px);
                  justify-content: flex-start;

                  .instructor-avatars {
                    img {
                      width: 19px;
                    }
                  }
                }
              }
            }
          }
        }

        .next-liveteach-class-body {
          .online-class-details {
            ul.online-class-sessions-list {
              li.online-class-session {
                .classes-sessions-detail-sections {
                  .schedule-card {
                    .row {
                      padding: 15px 27px 15px 24px;
                    }

                    .schedule-date-card {
                      padding-right: 9px;
                      width: 111px;
                    }

                    .schedule-card-session {
                      width: calc(100% - 111px);
                      padding-left: 14px;
                      overflow: visible;

                      .session-details {
                        padding-right: 10px;

                        .hide-icon {
                          display: block;
                        }
                      }
                    }

                    .schedule-data-btn-group {
                      width: 98%;
                      border-top: solid 1px $grey-6;
                      margin-top: 21px;
                      padding-top: 8px;

                      a,
                      button.session-resources-icon-group,
                      .border-btn-group,
                      .session-files {
                        display: none;
                      }

                      .accordion-button {
                        &::after {
                          margin-right: auto;
                        }
                      }
                    }
                  }

                  .session-accordion {
                    padding-left: 43px;
                    padding-right: 14px;

                    .accordion-body {
                      .homework-group {
                        .group-header {
                          &::after {
                            display: none;
                          }
                        }
                      }

                      .session-homework {
                        .header {
                          flex-direction: column;
                          position: relative;
                          padding-left: 21px;

                          .header-info {
                            gap: 15px;
                            padding-right: 42px;

                            .status-icon {
                              align-self: flex-start;
                            }

                            .header-title {
                              padding: 0;
                              margin: 0;
                            }
                          }

                          .completion-status {
                            margin-top: 10px;
                            padding-left: 45px;
                          }

                          .arrows {
                            position: absolute;
                            right: 0;
                            top: 44%;
                          }
                        }

                        .main-content {
                          .tests-list {
                            padding-left: 30px;
                            padding-right: 27px;

                            .test-item {
                              flex-direction: column;
                              align-items: flex-start;
                              gap: 16px;

                              .reading-title {
                                width: calc(100% - 0px);
                              }

                              .accuracy {
                                padding-left: 0;
                              }

                              .link-container {
                                padding-left: 0;
                              }
                            }
                          }

                          .content {
                            padding: 22px 25px;

                            .homework-task-comments {
                              flex-direction: column;

                              .comment-info {
                                width: 100%;
                              }
                            }

                            .comments-btn-section {
                              flex-direction: column;
                              align-items: center !important;
                              margin-top: 25px;

                              .mark-complete-btn {
                                max-width: 100%;

                                .awesome-checkbox-label {
                                  width: 100%;
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .homework-takeaways-modal {
      .modal-dialog {
        max-width: 527px;
      }

      .modal-header {
        padding: 19px 24px 16px 24px;

        .modal-title {
          font-weight: normal;
          font-size: 23px;
          text-align: left;
          color: $blue-3;
        }

        .btn-close {
          opacity: 0.7;

          &:hover {
            opacity: 1;
          }
        }
      }

      .modal-footer {
        border-top: 1px solid $grey-6;
        padding: 16px 0;
        justify-content: center;

        .btn {
          width: 120px;
          text-transform: uppercase;
          letter-spacing: 1.33px;
          padding: 8px 31px;
          font-size: 14px;
          font-weight: bold;
          border-radius: 3px;

          &.submit-btn {
            background-color: $blue-2;

            &:hover {
              background-color: $blue-3;
            }
          }

          &.cancel-btn {
            border: solid 1px $primary-2;
            color: $primary-2;

            &:hover {
              background-color: $primary-5;
            }
          }
        }
      }

      .cke_contents {
        height: 185px !important;
      }
    }
  }
}
