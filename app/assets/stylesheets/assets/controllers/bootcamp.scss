body[data-controller="bootcamp"] {
  strong,
  b {
    font-weight: 600;
  }

  .gmat-exam-tag-classes {
    color: $white !important;
    font-size: 13px;
    text-align: center;
    padding: 2px 10px;
    border-radius: 3px;
    width: fit-content;
    align-items: center;
    margin-left: 15px;
    background-image: linear-gradient(114deg, #2DC2FF 0%, #0474A2 100%), linear-gradient(109deg, #00A2E5 -2%, #98DDFF 104%);

    &.gmat-focus-tag-classes {
      background-image: linear-gradient(99deg, #F0C81A 23%, #C8A01B 132%), linear-gradient(268deg, #FFCD28 115%, #9F8017 -120%);
    }
  }

  .ellipse-decorator-1 {
    width: 112px;
    height: 128px;
    opacity: 0.6;
    filter: blur(84px);
    background-color: $primary-2;
    z-index: -1;
  }

  .live-teach-btn {
    padding: 7px 20px;
    display: inline-block;
    font-weight: 600;
    color: $white;
      border-radius: 5px;
    margin: 0 auto;
    text-align: center;
    background-image: linear-gradient(to right, #0582dd 19%, #034677 78%, #0582dd 147%);
  }

  .ellipse-decorator-2 {
    width: 63px;
    height: 314px;
    opacity: 0.3;
    filter: blur(84px);
    background-color: $purple-verbal-3;
    top: 40%;
    z-index: -1;
  }

  .ellipse-decorator-3 {
    width: 108px;
    height: 106px;
    object-fit: contain;
    opacity: 0.5;
    filter: blur(84px);
    background-color: $primary-2;
  }

  .instructor-img {
    border: solid 1px $grey-6;
    border-radius: 100%;
    padding: 5px;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      border-radius: 50%;
      max-width: 44px;
      max-height: 44px;
      line-height: 44px;
      object-fit: cover;
      padding: 0;
      border-width: 1px;
      border-style: solid;
      border-color: $grey-6;
    }

    &.lg {
      img {
        border-width: 3px;
        max-width: 85px;
        max-height: 85px;
        line-height: 85px;
      }
    }
  }

  .instructor-img-box {
    .instructor-img {
      overflow: hidden;
    }

    .checkout-instructor-img {
      border: 2px solid #BB8E41;
    }

    .instructor-info {
      width: calc(100% - 56px);
    }
  }

  @media (min-width: 1200px) {
    .container {
      max-width: 1310px;
    }
  }

  @media (min-width: 1800px) {
    .container {
      max-width: 1600px;
    }
  }

  &[data-action="index"] {
    dt {
      line-height: 1.44;
    }

    .hero {
      background-image: asset-url("layout/controllers/bootcamp/index/bootcamp_bg.webp");
      padding-left: 16px;
      padding-right: 16px;
      padding-top: 36px;
      z-index: 0;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: top left;
      border-radius: 8px;

      .top-dots img {
        width: 23.6px;
        height: 15.4px;
        opacity: 0.3;
      }

      .bottom-dots img {
        width: 23.6px;
        height: 19.6px;
        opacity: 0.3;
      }

      h2 {
        font-size: 22px;
        line-height: 1.5;
        color: $white;
        display: block;
        margin: 0 auto;
      }

      .emphasyzed-subtitle {
        border-radius: 8px;
        background: linear-gradient(102deg, rgba(183, 195, 209, 0.15) 0%, rgba(255, 255, 255, 1) 100%);
        padding: 13px 30px 13px 17px;
        border: 1px solid $white !important;
        margin-bottom: 20px;

        .words {
          font-weight: 300;
          color: #E5CA9B;
          font-size: 15px;
          line-height: 23px;

          .hours-background-color {
            background: linear-gradient(0deg, rgba(86, 166, 213, 1) 0%, rgba(5, 195, 255, 1) 100%);
            padding: 0 4px 0 5px;
            font-size: 16px;
            color: $white;
            border-radius: 5.2px;
            margin-right: 5px;
          }

          strong {
            font-weight: 600;
          }
        }

        .price {
          .words {
            font-size: 30px;
            background-image: linear-gradient(to top, #0d82a4 40%, #00b2df 70%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }

      .liveteach-price-container {
        border-radius: 8px;
        background: linear-gradient(166deg, $white -134%, #D9DFEC);
        padding: 1px !important;
        margin-bottom: 20px;

        .inner-container {
          padding: 20px 30px;
          background-image: linear-gradient(166deg, $white 16%, #D9DFEC 80%, rgb(217, 223, 236));

          .discounted-price-badge {
            border-radius: 5.1px;
            background-image: linear-gradient(259deg, #069A57 50%, #00CA84);
            text-shadow: 0 2.5px 5.1px rgba(0, 55, 78, 0.4);
            padding: 2px 7px;
            right: -12px;
            top: 3px;
            box-shadow: 0 1.3px 10.1px 0 rgba(15, 160, 185, 0.4);
          }

          .original-price {
            margin-top: 7px;
            color: #819DBF;
          }

          .words {
            font-weight: 300;
            color: #E5CA9B;
            font-size: 15px;
            line-height: 23px;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            background-image: linear-gradient(to top, #0d82a4 40%, #00b2df 70%);

            strong {
              font-weight: 600;
            }
          }

          .hours-background-color {
            background: linear-gradient(0deg, rgba(86, 166, 213, 1) 0%, rgba(5, 195, 255, 1) 100%);
            padding: 0 4px 0 5px;
            font-size: 16px;
            color: $white;
            border-radius: 5.2px;
            margin-right: 5px;
            line-height: 1.25;
          }

          .price {
            .words {
              font-size: 30px;
              background-image: linear-gradient(to top, #0d82a4 40%, #00b2df 70%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }

      .plus-icon {
        font-size: 65px !important;
        font-weight: bold !important;
        line-height: 0.8;
        text-transform: uppercase;
        background-image: linear-gradient(155deg, #4BABDC 0%, #09C2FE 85%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        padding-top: 5px;
      }

      .award-winning {
        margin-bottom: 37px;
        margin-top: 8px;
        line-height: 25px;
        text-align: left !important;

        .month {
          padding: 2px 4px;
          margin-right: 5px;
          border-radius: 4.9px;
          box-shadow: 0 1px 10px 0 rgba(188, 195, 214, 70%);
        }
      }

      .subtitle {
        padding-left: 28px;
        padding-right: 28px;
        margin-bottom: 38px;
        line-height: 1.5;
        color: #1648a7;

        .gmat-focus-tag {
          font-size: 16px;
        }
      }

      .full-details {
        margin-bottom: 25px;
        padding: 5px;
        background-image: linear-gradient(96deg, #61C6FE 0%, #348ABE 100%);
        line-height: 1.63;
        border-radius: 4px;
        cursor: pointer;
        box-shadow: 0 4px 20px 0 rgba(88, 185, 241, 70%);
        font-size: 0.8125rem;

        &:hover {
          background-image: linear-gradient(96deg, $primary-1 0%, $primary-1 100%);
        }
      }

      .benefits {
        i {
          margin-top: 7px;

          &.fal {
            font-size: 10px;
          }

          &::before {
            color: $light-5;
            padding: 5px;
            border-radius: 100%;
            background-color: #829ABF;
            font-weight: 500;
            background-image: linear-gradient(110deg, #829ABF 7%, #6380AC 90%);
          }
        }

        .item-color {
          color: $blue-3 !important;
        }

        li:first-child {
          margin-bottom: 40px;
        }
      }
    }

    &.ea {
      .hero {
        .benefits {
          li:first-child {
            margin-bottom: 25px !important;
          }
        }
      }
    }

    .schedule-section {
      border-radius: 10px;
      overflow: hidden;

      .content {
        min-height: 600px;
      }

      .bg-light-blue {
        background-color: #EFF4F8;
      }

      .title {
        background-image: asset-url("layout/controllers/online_classes/index/online_class_schedule_img.png");
        min-height: 84px;
        background-size: cover;
        background-position: 100%;
        padding: 0 28px;

        h2 {
          color: $gmat-0;
        }

        &:first-child {
          font-weight: 300;
        }

        .dots-decorator {
          opacity: 0.7;
        }
      }

      .classes-options {
        padding: 24px;

        .class-option-wrapper {
          .range-date {
            color: $white !important;
            text-transform: uppercase;
            font-size: 13px;
            text-align: center;
            padding: 2px 7px;
            border-radius: 6px;
            background-image: linear-gradient(97deg, #6865CB 0%, #6FA7DC 108%);
            width: fit-content;
            align-items: center;
          }

          .instructor-detail {
            align-items: center;

            .instructor-modal-link {
              margin-top: 5px;

              .free-online-class {
                color: $gmat-1;
                position: relative;

                &::after {
                  width: 100%;
                  height: 2px;
                  content: "";
                  display: block;
                  position: absolute;
                  bottom: 2px;
                  background-image: linear-gradient(100deg, #56A5D5 2%, #97D8FF 88%);
                  opacity: 0.4;
                }
              }
            }
          }
        }

        #accordion-online-classes {
          .calendar-img {
            margin-top: 2rem;

            &:first-child {
              margin-top: 0;
            }
          }

          .accordion-item {
            border: none;
            background-color: transparent;

            .accordion-header {
              border: 1px solid rgba(0, 0, 0, 0.125);
              box-sizing: content-box;
              border-radius: 8px;
              position: relative;
              background: linear-gradient(180deg, rgba(255, 255, 255, 1) 50%, rgba(224, 225, 235, 0.4) 100%);

              &::before {
                width: 7px;
                height: 100%;
                position: absolute;
                left: 0;
                content: "";
                background: #C9D0DE;
                display: block;
                border-radius: 8px 0 0 8px;
              }

              .class-option {
                padding: 16px 14px;

                .hotspot-alert {
                  border-color: transparent !important;
                  padding: 0 12px 4px 12px;
                  line-height: 1.54;
                  letter-spacing: 0.13px;
                  font-size: 14px;
                  border-radius: 4px;
                  white-space: nowrap;
                  display: flex !important;
                  align-items: baseline;
                  gap: 5px;
                  justify-content: center;
                  width: 150px;
                  font-weight: 600;
                  color: $red-1;

                  &::before {
                    min-width: 10px;
                    height: 10px;
                    content: "";
                    display: block;
                    background-color: $red-1;
                    border-radius: 100%;
                  }

                  i {
                    font-size: 14px;
                  }
                }

                .btn-box-head {
                  display: flex;
                  gap: 8px;
                  justify-content: flex-end;
                  flex-direction: column;

                  .trial-class {
                    min-width: 146px;
                    text-align: center;

                    .btn {
                      padding: 5px 16px;
                      border-radius: 2px;
                      margin: 0;
                      max-width: 114px;

                      &.disabled {
                        opacity: 1 !important;
                      }
                    }
                  }
                }

                .separator-line {
                  border-top: 1px solid $primary-4;
                  left: 0;
                  right: 0;
                }
              }

              button.accordion-button {
                &::after {
                  filter: invert(54%) sepia(69%) saturate(312%) hue-rotate(158deg) brightness(96%) contrast(101%);
                }

                &:hover .accordion-header {
                  border-color: $primary-2;
                }

                &:not(.collapsed) {
                  box-shadow: none;
                }
              }

              .instructor-img {
                padding: 4px;
                border-radius: 100%;
              }
            }

            &.opened {
              .accordion-header {
                border-color: rgba(38, 136, 204, 0.41);

                &::before {
                  background: rgb(102, 102, 204);
                  background-image: linear-gradient(174deg, #66C 4%, rgba(38, 136, 204, 0.41) 76%);
                }
              }
            }

            &:hover {
              .accordion-header {
                border-color: rgba(38, 136, 204, 0.41);
              }
            }

            .accordion-button {
              background-color: transparent;
            }

            .accordion-body {
              padding: 25px;
              margin-top: -3px;
              background-color: #E5E8F0 !important;

              .session-description {
                background-image: linear-gradient(97deg, #FFF -4%, rgba(255, 255, 255, 0.42) 121%);
                padding: 20px;
                gap: 30px;
                margin-bottom: 15px;
                border-radius: 6px;

                h3 {
                  color: $gmat-1;
                }

                h4 {
                  color: $gmat-1;
                }

                p {
                  margin-bottom: 0;
                }

                &:first-child {
                  padding-top: 0;
                }

                &:last-child {
                  border: none !important;
                }
              }
            }
          }
        }

        .closed-classes {
          .class-option-wrapper {
            .range-date {
              background-color: #879CB8;
              background-image: none;
            }
          }

          .border-line {
            text-align: center;

            .border-span {
              border-bottom: solid 1px $grey-5;
              display: inline-block;
              width: 100%;
              margin-bottom: -20px;
            }

            p {
              display: inline-block;
              margin-top: -20px;
              background-color: #EFF4F8;
              padding: 5px 26px;
              margin-bottom: 30px;
            }
          }
        }
      }

      .no-classes {
        h2 {
          font-size: 22px;
          line-height: 1.36;
          font-weight: normal;
        }

        p {
          line-height: 1.33;
        }
      }
    }

    .modal#verify-user {
      .modal-header {
        min-height: 116px;

        .dots-modal {
          opacity: 0.4;
        }

        .decorator {
          filter: blur(84px);
          object-fit: contain;
          background-color: $primary-2;
          opacity: 0.6;

          &.first {
            width: 77px;
            height: 128px;
          }

          &.second {
            width: 60px;
            height: 128px;
          }
        }
      }

      .modal-body {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;

        h2 {
          font-weight: normal;
          line-height: 1.07;
        }

        .line-decorator-gray {
          width: 100%;
          height: 1px;
        }
      }
    }

    .modal#free-online-class-form-modal {
      .modal-content {
        max-width: 521px;
        margin: 0 auto;
        padding: 0 0 10px;

        .modal-header {
          height: 115px;
          margin: 0;
          padding: 0;
          background-color: #0F2F44;
          position: relative;

          .modal-title {
            margin: 0;
            width: 100%;
            text-align: center;
            color: $white;
            font-size: 40px;
            position: relative;
            margin-top: -20px;

            span {
              color: #DBB968;
              position: relative;
              right: 14px;
            }

            &::before {
              position: absolute;
              left: 0;
              right: 0;
              margin: 0 auto;
              bottom: -10px;
              content: "";
              background-color: #DBB968;
              width: 16px;
              height: 2px;
            }
          }

          .btn-close {
            position: absolute;
            top: 12px;
            right: 11px;
            font-size: 23px;
            background-image: none;
            color: $white;
            font-weight: 300;
          }

          .bottom-grid {
            position: absolute;
            top: -5px;
            right: 95px;
          }

          .top-grid {
            position: absolute;
            bottom: 15px;
            left: 2px;
          }

          .left-header-shadow {
            -webkit-filter: blur(84px);
            filter: blur(84px);
            background-color: $primary-2;
            width: 112px;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
          }

          .right-header-shadow {
            -webkit-filter: blur(84px);
            filter: blur(84px);
            background-color: $primary-2;
            width: 115px;
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
          }
        }

        .modal-body {
          .online-class-session-contact {
            text-align: center;
            margin: 0;
          }

          .modal-title {
            font-size: 28px;
            font-weight: normal;
            text-align: center;
            color: $dark-1;
            margin-bottom: 10px;
            width: 100%;
            margin-top: 14px;
          }

          .sub-title {
            font-size: 16px;
            font-weight: normal;
            text-align: center;
            color: $grey-2;
            width: 100%;
            margin-bottom: 18px;
          }

          .email-input {
            input {
              margin-bottom: 29px;
            }

            .btn {
              min-width: 307px;
              margin-bottom: 10px;

              @media (max-width: 414px) {
                min-width: 100%;
                margin-bottom: 0;
              }
            }
          }
        }

        @media (max-width: 991px) {
          .modal-header {
            .modal-title {
              font-size: 30px;
            }

            .bottom-head-bg {
              right: 50px;
            }
          }

          .modal-body {
            .online-class-session-contact {
              margin: 0;
            }
          }
        }

        @media (max-width: 767px) {
          .modal-header {
            .modal-title {
              font-size: 30px;
            }

            .bottom-head-bg {
              right: 50px;
            }
          }

          .modal-body {
            .online-class-session-contact {
              margin: 0;
            }

            .sub-title {
              font-size: 19px;
            }
          }
        }
      }
    }

    .modal.instructor-bio-modal {
      .modal-body {
        overflow: hidden;

        .instructor-rank {
          background-image: linear-gradient(97deg, #6865CB 0%, #6FA7DC 108%);
          padding: 0 10px 1px 11px;
          border-radius: 6px;
          color: $white;
          width: 175px;
          height: 27px;
          display: flex;
          align-items: center;
          margin: 10px 0 25px;
        }

        .top-dot-grid {
          position: absolute;
          right: 103px;
          width: auto;
          top: 8px;
        }

        .bottom-dot-grid {
          position: absolute;
          left: 34px;
          width: auto;
          bottom: 8px;
        }

        .close-icon {
          top: 20px;
          position: absolute;
          right: 15px;
          left: unset;
          text-align: right;

          .btn-close {
            font-size: 15px;
          }
        }

        .instructor-detail {
          padding: 29px 35px 35px 34px;
          font-size: 16px;
          position: relative;

          .instructor-name {
            font-size: 24px;
          }

          .instructor-score {
            padding: 2px 10px 1px;
            border-radius: 6px;
            background-image: linear-gradient(97deg, #6865CB 0%, #6FA7DC 108%);
            width: fit-content;
          }

          .instructor-description {
            max-height: 400px;
            overflow: auto;
            color: #545E64;
            line-height: 1.5;
            font-size: 16px;
          }
        }

        .instructor-image {
          @media (max-width: 767px) {
            display: none !important;
          }
        }
      }

      .modal-content {
        overflow: hidden !important;
      }

      .modal-dialog {
        @media (max-width: 991px) {
          max-width: 75%;
          margin-left: auto;
          margin-right: auto;
        }
      }
    }

    @media (min-width: 768px) {
      .hero {
        padding-left: 13px;
        padding-right: 13px;

        .emphasyzed-subtitle {
          padding: 13px 13px 13px 18px;

          .price {
            .words {
              font-size: 30px;
            }
          }
        }
      }

      .schedule-section {
        .classes-options {
          #accordion-online-classes {
            .accordion-item {
              .accordion-header {
                .class-option {
                  padding: 16px 12px 12px;

                  .separator-line {
                    border-top: 1px solid $primary-4 !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    @media (min-width: 768px) and (max-width: 991px) {
      .schedule-section {
        .classes-options {
          .class-option-wrapper {
            .gmat-tag {
              flex-direction: column !important;
              align-items: flex-start !important;
            }
          }
        }
      }
    }

    @media (min-width: 992px) {
      .class-schedules {
        .class-schedules-lnfo {
          padding: 0;
        }
      }

      .hero {
        padding-left: 20px;
        padding-right: 20px;
      }
    }

    @media (min-width: 1200px) and (max-width: 1340px) {
      .class-schedules {
        .class-schedules-lnfo {
          padding: 0 12px;
        }
      }
    }

    @media (min-width: 1200px) {
      .class-schedules {
        .class-schedules-lnfo {
          width: 380px;
        }

        .online-class-info {
          width: calc(100% - 380px);
        }
      }

      .hero {
        padding-left: 15px;
        padding-right: 15px;
        padding-top: 40px;

        h1 {
          font-size: 28px;
          line-height: 1.07;
          letter-spacing: 0.56px;
        }

        .top-dots img {
          opacity: 0.5;
        }

        .bottom-dots img {
          opacity: 0.5;
        }

        .image-container {
          width: 98px;
          height: 98px;
        }
      }

      .schedule-section {
        .classes-options {
          #accordion-online-classes {
            .accordion-item {
              .accordion-header {
                .class-option {
                  padding: 13px 10px 13px 23px;

                  .class-option-wrapper {
                    margin-bottom: 0;
                    display: flex;
                    gap: 5px;
                    align-items: center;

                    .period-hours {
                      white-space: nowrap;
                      font-size: 15px;
                    }

                    .instructor-detail {
                      display: flex;
                      gap: 15px;
                      align-items: center;
                    }

                    .online-class-date {
                      width: 35.33% !important;
                      padding-right: 0;

                      .period-days,
                      .period-hours {
                        color: #545E64 !important;
                      }
                    }
                  }

                  .trial-class {
                    .btn {
                      padding: 6px 14px;
                    }
                  }

                  .book-now-btn {
                    margin-bottom: 10px;
                  }

                  .instructor-detail {
                    .separator-line {
                      &:first-child {
                        position: relative;
                        left: -5px;
                      }
                    }
                  }

                  .separator-line {
                    border-top: none;
                    border-left: 1px solid $primary-4;
                    min-height: 100px;
                    width: 1px;
                    display: block;
                    content: "";
                    position: relative;
                    left: 15px;
                  }
                }
              }

              .accordion-body {
                .container {
                  padding-left: 20px;
                  padding-right: 20px;
                }

                .separator-line {
                  border-top: none;
                  border-left: 1px solid $primary-4;
                  min-height: auto;
                  width: 1px;
                  display: block;
                  content: "";
                }
              }
            }
          }
        }
      }
    }

    .class-option-wrapper {
      .period-hours {
        white-space: nowrap;
        font-size: 15px;
      }

      .time-zone {
        border-radius: 3px;
        border: solid 1px $grey-6;
        background-color: $white;
        padding: 4px 4px 3px 5.1px;
        gap: 5px;
        font-size: 13px;
        font-weight: 600;
        line-height: 1.85;
        color: $blue-1;
        height: 23px;
        text-transform: uppercase;
        margin-left: 5px;

        span {
          white-space: nowrap;
        }
      }

      @media (max-width: 1375px) {
        .period-hours-main {
          flex-wrap: wrap;

          .time-zone {
            margin-left: 0;
            margin-top: 0 !important;
          }
        }
      }

      @media (max-width: 991px) {
        .period-hours-main {
          flex-wrap: nowrap !important;

          .time-zone {
            margin-left: 5px;
            margin-top: 0;
            margin-bottom: 10px;
          }
        }
      }

      @media (max-width: 767px) {
        .period-hours-main {
          .time-zone {
            margin-left: 5px;
            margin-top: 0;
            margin-bottom: 10px;
          }
        }
      }

      @media (max-width: 414px) {
        .period-hours-main {
          flex-wrap: wrap !important;

          .time-zone {
            margin-left: 0;
          }
        }
      }
    }

    @media (min-width: 1200px) and (max-width: 1305px) {
      .schedule-section {
        .classes-options {
          .class-option-wrapper {
            .gmat-exam-tag-classes {
              margin-top: 10px;
              margin-left: 0;
            }

            .gmat-tag {
              flex-direction: column !important;
              align-items: flex-start !important;
            }
          }
        }
      }
    }

    @media (min-width: 1200px) and (max-width: 1800px) {
      .schedule-section {
        .classes-options {
          .class-option-wrapper {
            .gmat-exam-tag-classes {
              &.gmat-focus-tag-classes {
                padding: 2px 5px;
              }
            }
          }
        }
      }
    }

    @media (min-width: 1400px) {
      .hero {
        .emphasyzed-subtitle {
          .price {
            .words {
              font-size: 30px;
            }
          }
        }
      }

      .schedule-section {
        .title {
          height: 93px;
        }
      }
    }

    @media (min-width: 1801px) {
      .hero {
        .plus-icon {
          padding-right: 15px !important;
          text-align: right;
        }
      }

      .schedule-section {
        .classes-options {
          padding: 39px 28px 36px;
        }
      }
    }

    @media (max-width: 1199px) {
      .schedule-section {
        .classes-options {
          #accordion-online-classes {
            .accordion-item {
              .accordion-header {
                .class-option {
                  .class-option-wrapper {
                    margin-bottom: 10px;
                    align-items: center;
                  }

                  .btn-box-head {
                    gap: 12px;
                    margin-top: 16px;
                    width: 100%;
                    flex: 100%;
                    padding-left: 0;
                    padding-right: 0;
                    flex-wrap: wrap;
                    flex-direction: column;
                    font-size: 14px;

                    .hotspot-alert {
                      width: 100%;
                    }

                    .trial-class {
                      max-width: 100%;

                      .btn {
                        max-width: 100%;
                      }
                    }
                  }

                  .book-now-btn {
                    margin-bottom: 12px;
                  }
                }

                .separator-line {
                  border-top: none;
                  border-left: 1px solid $primary-4;
                  min-height: auto;
                  width: 1px;
                  display: block;
                  content: "";
                }
              }

              .accordion-collapse {
                .accordion-body {
                  .session-description {
                    flex-direction: column;

                    .separator-line {
                      border-bottom: 1px solid $gmat-4;
                      border-left: 0;
                    }
                  }
                }
              }

              .instructor-detail {
                display: flex;
                gap: 10px;
              }
            }
          }

          .class-option-wrapper {
            .gmat-exam-tag-classes {
              margin-top: 10px;
              margin-left: 0;
            }
          }
        }

        .content {
          align-items: center !important;
        }
      }
    }

    @media (max-width: 991px) {
      .class-schedules {
        .class-schedules-lnfo {
          padding: 0 6px;
        }
      }

      .hero {
        padding-left: 6px;
        padding-right: 6px;

        .subtitle {
          padding-left: 8px;
          padding-right: 8px;
          margin-bottom: 25px;
        }

        .liveteach-price-container {
          .inner-container {
            padding: 20px 8px;

            .discounted-price-badge {
              right: -5px;
              top: -12px;
            }
          }
        }

        .plus-icon {
          justify-content: flex-end;
          display: flex;
          padding-right: 11px !important;
          margin-left: 15px;
        }

        .award-winning {
          width: calc(83.3333% - 15px);
        }

        .live-teach-btn {
          width: auto;
        }
      }

      .schedule-section {
        .classes-options {
          #accordion-online-classes {
            .accordion-item {
              .accordion-header {
                .class-option {
                  padding: 20px 20px 0 !important;

                  .class-option-wrapper {
                    .online-class-date {
                      .period-hours {
                        margin-bottom: 10px !important;
                      }
                    }

                    .instructor-detail {
                      border-top: 1px solid $primary-4 !important;
                    }
                  }

                  .btn-box-head {
                    .trial-class {
                      display: flex;
                      flex-direction: column;

                      .btn {
                        padding-top: 8px;
                        padding-bottom: 8px;
                      }

                      .book-now-btn {
                        order: 2;
                        margin-bottom: 0;
                        margin-top: 10px;
                      }
                    }
                  }
                }
              }

              .accordion-collapse {
                .accordion-body {
                  padding: 10px 0 !important;
                }
              }
            }
          }
        }
      }
    }

    @media (max-width: 767px) {
      .hero {
        .liveteach-price-container {
          .inner-container {
            .discounted-price-badge {
              right: -10px;
              top: 3px;
            }

            .price {
              margin-top: 8px;
            }
          }
        }

        .view-full-details {
          padding-right: 1.5rem !important;
          padding-left: 1.5rem !important;
        }

        .plus-icon {
          font-size: 48px !important;
          justify-content: flex-end;
          display: flex;
          padding-right: 11px !important;
          margin-left: 0;
        }

        .subtitle {
          padding-left: 5px;
          padding-right: 5px;
        }

        .award-winning {
          width: calc(83.3333% - 0px);
        }

        .live-teach-btn {
          width: 180 !important;
        }

        h2 {
          width: 65% !important;
        }
      }

      .schedule-section {
        margin-top: 20px;

        .classes-options {
          #accordion-online-classes {
            .accordion-item {
              .accordion-body {
                padding: 10px 0 !important;

                .container {
                  padding-left: 12px;
                  padding-right: 12px;
                }
              }

              .session-description {
                gap: 10px !important;
              }
            }
          }

          .class-option-wrapper {
            .range-date {
              font-size: 11px;
            }

            .gmat-exam-tag-classes {
              margin-top: 0;
              margin-left: 15px;
            }
          }
        }
      }
    }

    @media (max-width: 576px) {
      .schedule-section {
        .classes-options {
          .class-option-wrapper {
            .gmat-exam-tag-classes {
              margin-left: 0;
              margin-top: 10px;
            }
          }
        }
      }
    }

    @media (max-width: 492px) {
      .hero {
        .subtitle {
          width: 80% !important;
        }
      }
    }
  }

  &[data-action="confirm"] {
    h1 {
      font-size: 28px;
    }

    h2 {
      font-size: 19.2px;
      font-stretch: normal;
      line-height: 1.6;
    }

    p {
      line-height: 30px;
      font-weight: normal;
    }

    dt {
      font-size: 15.9px;
      line-height: 1.44;
      letter-spacing: 0.16px;
      color: #DBB36F;
    }

    dd {
      font-size: 14.1px;
    }

    .ellipse-decorator-4 {
      width: 112px;
      height: 128px;
      opacity: 0.6;
      object-fit: contain;
      filter: blur(84px);
      background-color: $primary-2;
    }

    .ellipse-decorator-5 {
      width: 63px;
      height: 101px;
      opacity: 0.3;
      object-fit: contain;
      filter: blur(84px);
      background-color: $purple-verbal-3;
    }

    .dots-decorator {
      width: 22px;
      height: 15.4px;
    }

    .card {
      background-image: linear-gradient(158deg, $white 37%, $primary-5 134%);

      .instructor {
        padding: 35px 31px 29px 22px;
        background-image: linear-gradient(to bottom, $dark-1 0%, $blue-1 100%);

        p {
          color: $white;

          span {
            color: #DBB36F;
          }
        }
      }

      .congratulation {
        padding-left: 16px;
        padding-right: 22px;

        p {
          color: $grey-2;
        }
      }
    }

    @media (min-width: 768px) {
      .card {
        .instructor {
          min-height: 582px;
          padding: 35px 17px 82px 22px;
        }

        .congratulation {
          padding-top: 31px;
          padding-left: 38.4px;
          padding-right: 31px;
        }
      }
    }
  }

  &[data-action="checkout"] {
    .online-class {
      .payment-card {
        border-radius: 8px;

        .card {
          border-radius: 8px;

          .card-header {
            border-radius: 8px 8px 0 0;
          }
        }
      }

      .card-header {
        background-image: asset-url("layout/controllers/online_classes/index/online_class_schedule_img.png");
        background-size: cover;
        background-position: 100%;
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;

        h1 {
          font-size: 28px;
          color: $gmat-0;
          font-weight: 400;
        }

        .title:first-child {
          font-weight: 300;
        }
      }

      .card-body {
        .tab-credit-card {
          li.nav-item {
            .card-link-wrapper {
              border-bottom: solid 3px #DEE2E6;

              .nav-link.active {
                color: #5AA7D3;
                border: none;
                border-bottom: solid 3px #5AA7D3;
                margin-bottom: -3px;
              }
            }
          }
        }

        .indian-users-message {
          margin: 14px 0 34px 0;
          padding: 14px 16px 13px 17px;
          background: $blue-5;
          color: $gmat-1;

          .info-logo-indian-users {
            margin-right: 12px;

            img {
              width: 18px;
              height: 18px;
              max-width: none;
            }
          }

          .credit-card-issues {
            a {
              text-decoration: underline;
              text-decoration-color: rgba(27, 120, 207, 0.2);
            }
          }
        }

        .which-card {
          .nav {
            .nav-item {
              .nav-link {
                width: 230px;
              }
            }
          }

          .tab-content {
            .tab-pane {
              min-height: auto;

              .card-section {
                background-color: #ECF7FF;
                padding: 20px;
                border-radius: 6px;

                &::after {
                  background-image: asset-url("layout/controllers/dashboards/ondemand/vector_image.webp");
                  content: "";
                  width: 140px;
                  position: absolute;
                  right: 3px;
                  display: block;
                  bottom: 4px;
                  height: 100px;
                  background-size: cover;
                }

                .card-info-details {
                  color: #236EB3;
                }

                .card-box {
                  background-image: linear-gradient(to right, #68B5FA, #3769FF 239%);
                  padding: 18px;
                  border-radius: 8.3px;
                  width: 330px;

                  .card-number-field {
                    background-color: #235696;
                    padding: 6.5px;
                    border-radius: 8.3px;
                    width: max-content;
                    min-width: 60px;
                  }
                }
              }
            }
          }
        }
      }

      .payment-option {
        padding: 14px 14px 14px 48px;

        p {
          display: none;
        }

        &.loaded {
          padding: 0;

          p {
            display: block;
          }
        }

        label {
          padding: 14px 14px 14px 48px;

          i {
            top: 50%;
            transform: translateY(-50%);
          }
        }

        &:hover,
        label:hover {
          border-color: $primary-3;
          cursor: pointer !important;
        }

        &.active {
          border-color: $primary-3;
          background-color: $light-2;
        }
      }

      input#coupon-code {
        min-height: 44.2px;
      }

      button#apply-discount {
        margin-bottom: 1px;
      }
    }

    .summary-class {
      border-radius: 8px !important;
      overflow: hidden;

      .schedule-btn {
        position: relative;
        color: $gmat-1;

        &::after {
          width: 100%;
          height: 2px;
          content: "";
          display: block;
          position: absolute;
          bottom: 1px;
          background-image: linear-gradient(100deg, #56A5D5 2%, #97D8FF 88%);
          opacity: 0.4;
          left: 0;
          right: 0;
        }
      }

      .checktop-divider {
        width: 336px;
        height: 1px;
        display: block;
        content: "" !important;
        background-image: linear-gradient(102deg, transparent 0%, $grey-6 51%, transparent 100%);
        margin: auto;
      }

      .class-description {
        background-image: asset-url("layout/controllers/online_classes/confirm/online_class_description_img.svg");
        background-size: cover;
        background-position: 100%;
        z-index: 0;
        padding: 23px 16px 34px 19px;

        dt {
          line-height: 1.44;
        }

        .custom-separator {
          width: 1px;
          height: 150px;
          background-image: linear-gradient(102deg, transparent 0%, #FFF 51%, transparent 100%);
        }

        a.change-schedule {
          font-size: 14px;
          text-decoration: none;
          color: $white;
          margin: 13px 30px 0 0;
          padding: 3px 7.1px;
          border-radius: 2px;
          border: solid 1px $white;
          width: 160px;
          cursor: pointer;
          text-align: center !important;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
          }
        }

        .checkout-exam-tag {
          margin-left: 0;
          margin-bottom: 7px;
        }

        .checkout-tag-box {
          margin-bottom: 7px;
        }
      }

      .payment-total {
        padding: 39px 0 37px;

        .current-price .amount,
        .before-price .amount.filled {
          &::before {
            content: "$";
          }
        }

        .before-price {
          padding-top: 3px;
        }

        .your-plan {
          span {
            padding-top: 2px;
          }
        }
      }

      .list-unstyled {
        li {
          span:first-child {
            margin-top: -4px;
          }
        }
      }

      .checkout-rang-date {
        font-size: 14px !important;
        background-color: $white !important;
        color: $dark-2 !important;
        background-image: none !important;
        padding: 4px 10px 2px;
        border-radius: 6px;
        width: fit-content;
        text-align: center;
        text-transform: uppercase;
      }

      .terms-and-conditions,
      .emails-subscription {
        width: calc(100% + 20px);
        margin-left: -9px;

        .form-check {
          margin-bottom: 26px;

          .form-check-input {
            box-shadow: none;

            &:checked {
              border-radius: 4px;
              margin-top: 2px;
              background-color: $primary-2;
              border-color: $primary-2;
            }
          }
        }

        label {
          color: $grey-4;
          font-size: 14px;
        }
      }
    }

    .purchase-includes {
      li {
        margin-bottom: 30px;

        i {
          margin-top: 3px;

          &.fal {
            font-size: 10px;
          }

          &::before {
            color: $light-5;
            padding: 2px;
            border-radius: 100%;
            background-color: #829ABF;
            font-weight: 500;
            background-image: linear-gradient(110deg, #829ABF 7%, #6380AC 90%);
          }

          &::after {
            color: transparent;
          }
        }

        .item-color {
          color: $gmat-1;
        }
      }
    }

    #billing-information {
      input,
      select {
        margin-bottom: 24px !important;
      }

      .indian-users-help-text {
        margin-top: -10px;
        margin-bottom: 21px;
      }
    }

    #cart-email-confirmation {
      .modal-dialog {
        max-width: 600px;
      }

      .email-input-style {
        width: 143px;
        font-size: 30px;
        letter-spacing: 4px;

        &:focus {
          outline: none;
        }
      }

      .show-overlay {
        color: $primary-1;
      }
    }

    #review-order {
      display: none !important;
    }

    @media (min-width: 1400px) {
      .payment-card {
        width: 65%;
        height: max-content;
      }

      .purchase-summary {
        width: 35%;
      }
    }

    #payment-information {
      min-height: 400px !important;
    }

    #payment-method ul li > a.active {
      border-bottom: 3px solid $primary-2 !important;
      color: $primary-2 !important;
    }

    @media (min-width: 768px) and (max-width: 991px) {
      .summary-class {
        .class-description {
          .gmat-exam-tag-classes {
            margin-top: 10px;
          }
        }
      }
    }

    @media (min-width: 992px) and (max-width: 1800px) {
      .summary-class {
        .checkout-rang-date {
          padding: 4px 7px 2px;
        }
      }
    }

    @media (min-width: 1200px) and (max-width: 1800px) {
      .summary-class {
        .class-description {
          .gmat-exam-tag-classes {
            &.gmat-focus-tag-classes {
              padding: 2px 5px;
            }
          }
        }
      }
    }

    @media (min-width: 1400px) {
      .payment-card {
        width: 65%;
        height: max-content;
      }

      .purchase-summary {
        width: 35%;
      }
    }

    @media (max-width: 991px) {
      .online-class {
        .card-body {
          .which-card {
            .tab-content {
              width: 100%;

              .tab-pane {
                .card-section {
                  width: 100%;

                  &::after {
                    display: none;
                  }

                  .card-box {
                    width: 100%;
                  }
                }
              }
            }
          }
        }
      }

      .summary-class {
        .schedule-btn {
          font-size: 13px !important;
        }

        .class-description {
          padding: 26px 16px !important;

          .instructor-row {
            margin: 0 !important;

            .custom-separator {
              border: 0 !important;
            }

            .schedule-time {
              align-items: baseline;
              gap: 5px;
              display: flex;
            }

            .checkout-tag-box {
              flex-direction: column;
              display: flex;
            }
          }
        }

        .checktop-divider {
          width: 252px !important;
        }
      }
    }

    @media (max-width: 767px) {
      #checkout {
        .container {
          &.billing {
            padding: 0 0.75rem !important;

            #payment-method {
              .change-payment-method {
                padding-left: 4px;
                padding-right: 4px;
              }
            }
          }
        }
      }

      .online-class {
        .payment-card {
          .card {
            .card-body {
              #accordion-payment + hr {
                margin-top: 0;
              }
            }
          }
        }

        .card-body {
          .which-card {
            .tab-content {
              .tab-pane {
                .container {
                  padding-right: 0 !important;
                  padding-left: 0 !important;
                  padding-bottom: 1rem !important;
                  padding-top: 1rem !important;
                }
              }
            }
          }
        }
      }

      .summary-class {
        .class-description {
          .instructor-row {
            a.change-schedule {
              display: block;
              margin: 10px 0 0 0 !important;
              width: 100% !important;
            }

            .schedule-time {
              display: inline !important;
            }
          }
        }
      }
    }
  }
}
