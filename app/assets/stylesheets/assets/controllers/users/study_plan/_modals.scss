body[data-controller="users"][data-action="study_plan"] {
  #skipping-study-plan-modal {
    .modal-dialog {
      max-width: 670px;

      .modal-header {
        padding: 20px 30px;
      }

      .modal-body {
        padding: 0 35px 0 35px;

        p {
          font-weight: 400;
          color: $grey-2;
          margin-bottom: 20px;
        }
      }

      .modal-footer {
        padding: 30px 0 45px 0;

        .btn {
          min-width: calc(50% - 40px);
        }
      }
    }

    @media only screen and (max-width: 540px) {
      .modal-dialog {
        .modal-footer {
          display: flex;
          flex-flow: column-reverse;
          padding-top: 20px;

          .btn {
            width: calc(100% - 70px);

            &:last-child {
              margin-bottom: 15px;
            }
          }
        }
      }
    }

    @media only screen and (max-width: 400px) {
      .modal-dialog {
        .modal-footer {
          .btn {
            span {
              display: none;
            }
          }
        }
      }
    }
  }

  .review-test-chapters-modal {
    .modal-dialog {
      max-width: 680px;

      .modal-header {
        padding: 20px 30px;
      }

      .modal-body {
        padding-top: 24px;

        ul {
          list-style: none;
          padding: 0;
          margin-bottom: 0;

          li {
            display: flex;
            padding: 3px 10px 4px 15px;
            color: $grey-2;

            &::before {
              content: "\2022";
              color: $primary-2;
              display: inline-block;
              width: 17px;
              font-size: 20px;
              line-height: 20px;
            }
          }
        }
      }
    }
  }

  .strategy-reading-modal {
    .modal-dialog {
      max-width: 680px;

      .modal-header {
        padding: 20px 30px;
      }

      .modal-body {
        padding-top: 24px;

        h4 {
          max-width: 400px;
          margin: 33px auto 0 auto;
        }

        p {
          max-width: 525px;
          margin: 0 auto;
        }
      }
    }
  }

  .exam-score-modal {
    .modal-dialog {
      max-width: 680px;

      .modal-header {
        padding: 20px 30px;
      }

      .modal-body {
        padding: 30px 30px 10px 30px;

        .exam-scores-form {
          fieldset {
            flex: 2 1 auto;

            &.date {
              flex: 3 0.7 auto;
            }
          }
        }

        .test-analyzer {
          margin-top: 30px;
          padding: 25px 40px;

          .title {
            background: unset !important;
          }
        }
      }

      .modal-footer {
        padding: 0 30px 40px 30px;
      }
    }

    @media only screen and (max-width: 575px) {
      .modal-dialog {
        .modal-body {
          .test-analyzer {
            padding: 25px 20px;
          }
        }

        .modal-footer {
          display: flex;
          flex-wrap: wrap;
          flex-direction: row-reverse;

          button {
            width: 100% !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
          }
        }
      }
    }
  }

  #new-chapter-evaluation-modal,
  #new-chapter-modal,
  #no-questions-for-weakest-topics-test {
    .modal-dialog {
      .modal-body {
        padding-top: 0;

        img {
          width: 96px;
        }

        p {
          color: $grey-2;
        }
      }
    }
  }

  #no-questions-for-weakest-topics-test {
    .modal-dialog {
      max-width: 430px;

      .modal-body {
        p {
          max-width: 320px;
          margin: 0 auto;
        }
      }
    }
  }

  #subscription-exiring-soon-modal {
    .modal-header {
      display: flex;
      justify-content: flex-end;
    }

    .modal-dialog {
      max-width: 625px;

      .modal-body {
        padding: 10px 30px;

        img {
          width: 104px;
        }

        p {
          color: $grey-2;
        }
      }

      .modal-footer {
        padding: 12px 30px 24px 30px;
        display: flex;
        justify-content: center;

        > * {
          flex: 0 0 calc(50% - 15px);
          max-width: 180px;
        }

        .btn {
          padding-left: 0;
          padding-right: 0;
        }

        form {
          margin-right: 15px;

          .btn {
            width: 100%;
          }
        }
      }
    }
  }

  #accuracy-not-met-easy-modal,
  #accuracy-not-met-medium-a-modal,
  #accuracy-not-met-medium-b-modal,
  #accuracy-not-met-hard-modal {
    .modal-dialog {
      max-width: 790px;

      .modal-body {
        padding: 10px 30px;

        img {
          width: 90px;
        }

        > p {
          color: $grey-2;
          max-width: 500px;
          margin: 20px auto;
        }

        .carousel-inner {
          margin-top: 24px;
          padding: 40px 32px;
          background-color: $grey-9;
          border-radius: 4px;
          height: 275px;
          overflow: scroll;
          position: relative;
          display: block;

          h5 {
            margin-bottom: 16px;
          }
        }
      }

      .actions {
        display: flex;
        justify-content: space-between;
        padding: 16px 0 40px 0;

        .indicators {
          flex: 0 0 140px;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          .carousel-indicators {
            list-style: none;
            padding: 0;
            margin: 0;
            text-align: center;
            position: relative;
            display: block;

            li {
              width: 10px;
              height: 10px;
              background-color: $grey-6;
              border-radius: 5px;
              display: inline-block;
              margin: 0 3px;
              background-clip: initial;
              border-width: 0;

              &.active {
                background-color: $primary-2;
              }
            }
          }
        }

        .previous {
          flex: 0 0 calc(50% - 70px);
          text-align: left;
          margin: 0;
        }

        .next {
          flex: 0 0 calc(50% - 70px);
          text-align: right;
          margin: 0;
        }

        .previous,
        .next {
          a {
            width: 100%;
            max-width: 180px;
          }
        }
      }
    }

    @media only screen and (max-width: 575px) {
      .modal-dialog {
        .actions {
          .indicators {
            display: none !important;
          }

          .previous,
          .next {
            flex: 0 0 calc(50% - 5px);
          }
        }
      }
    }
  }

  .modal#calendar-setup-modal {
    .modal-dialog {
      .modal-content {
        section#view-preferences,
        section#date-time-preferences,
        section#availability {
          .modal-body {
            padding: 0 20px;
          }
        }

        section#view-preferences {
          .modal-body {
            .title-wrapper {
              max-width: 807px;
              padding: 10px;
            }
          }
        }

        section#date-time-preferences {
          .modal-header {
            padding: 20px 24px;

            .step-number {
              height: 32px;
              line-height: 1.33;
              padding: 3px;
              width: 32px;
              border: 2px solid $blue-4;
              color: $blue-2;
              box-shadow: 0 5px 15px 0 rgba(183, 195, 209, 0.6);
              font-size: 18px;
            }
          }

          .modal-body {
            padding-top: 40px;
            padding-bottom: 40px;
          }
        }

        section#availability {
          .modal-header {
            padding: 20px 24px;

            .step-number {
              height: 32px;
              line-height: 1.33;
              padding: 3px;
              width: 32px;
              border: 2px solid $blue-4;
              color: $blue-2;
              box-shadow: 0 5px 15px 0 rgba(183, 195, 209, 0.6);
              font-size: 18px;
            }
          }

          .modal-body {
            padding-top: 28px;
            padding-bottom: 28px;
          }
        }

        @media only screen and (min-width: 414px) {
          section#view-preferences,
          section#date-time-preferences,
          section#availability {
            .modal-body {
              padding-left: 24px;
              padding-right: 24px;
            }
          }
        }

        @media only screen and (min-width: 576px) {
          section#date-time-preferences {
            .modal-header {
              padding: 20px 24px 20px 40px;
            }
          }

          section#availability {
            .modal-header {
              padding: 20px 24px 20px 40px;
            }
          }
        }

        @media only screen and (min-width: 576px) and (max-width: 767px) {
          section#view-preferences,
          section#date-time-preferences,
          section#availability {
            .modal-body {
              padding-left: 40px;
              padding-right: 40px;
            }
          }
        }

        @media only screen and (min-width: 768px) and (max-width: 1199px) {
          section#view-preferences,
          section#date-time-preferences,
          section#availability {
            .modal-body {
              padding-left: 70px;
              padding-right: 70px;
            }
          }
        }

        @media only screen and (min-width: 1200px) {
          section#view-preferences,
          section#date-time-preferences,
          section#availability {
            .modal-body {
              padding-left: 40px;
              padding-right: 40px;
            }
          }
        }
      }

      .modal-footer {
        padding-left: 24px;
        padding-right: 24px;

        @media only screen and (max-width: 320px) {
          padding-left: 20px;
          padding-right: 20px;
        }

        .btn {
          width: 203px;
          height: 44px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          margin: 0;

          &:last-child {
            margin-left: 24px;
          }

          @media only screen and (max-width: 575px) {
            width: 100%;

            &:last-child {
              margin-left: 0;
              margin-top: 10px;
            }
          }
        }
      }

      @media only screen and (max-width: 991px) {
        max-width: none;
        margin-left: 40px;
        margin-right: 40px;
      }

      @media only screen and (max-width: 413px) {
        margin-left: 20px;
        margin-right: 20px;
      }
    }
  }

  .guide-modal {
    padding: 0 12px !important;

    .modal-dialog {
      max-width: 840px;
      pointer-events: unset;

      .modal-body {
        background-image: url(asset_path("layout/controllers/users/study_plan/guide_background.webp"));
        background-repeat: no-repeat;
        background-size: cover;
        min-height: 685px;
        padding: 30px 32px 14px 32px;
        border-radius: 6px;
        position: relative;

        .btn-close {
          position: absolute;
          top: 9px;
          right: 8px;
          z-index: 9;
          cursor: pointer;
        }

        .guide-modal-header {
          margin-bottom: 21px;
          display: flex;
          gap: 20px;
          align-items: center;

          .guide-modal-content {
            width: calc(100% - 220px);
            font-size: 16px;
            font-weight: 300;
            line-height: 24px;
            letter-spacing: 0.32px;
            color: $grey-1;

            .guide-modal-title {
              font-size: 22px;
              line-height: 1.36;
              color: $blue-2;
              margin-bottom: 16px;
              font-weight: 600;
            }

            .guide-link {
              font-weight: 600;
              color: $blue-3;
            }

            .resource-link {
              text-decoration: underline;
              color: $blue-2;
              font-weight: 600;
            }
          }

          .guide-modal-video {
            width: 220px;
            justify-content: center;
            align-items: center;
            display: flex;
            padding: 30px 13px 13px 13px;
            background-image: asset-url("layout/controllers/users/study_plan/video_image.webp");
            height: 152px;
            border-radius: 4.2px;
            overflow: hidden;
            background-position: center;
            background-size: cover;

            .play-btn {
              top: 50%;
              left: 0;
              right: 0;
              margin: 0 auto;
              transform: translate(0, -50%);
            }
          }
        }

        .remember-block {
          border-radius: 6px;
          box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.4);
          background-color: $white;

          .remember-head {
            border-bottom: solid 1px $grey-6;
            width: 100%;
            padding: 18px 27px;

            .remember-title {
              font-size: 15px;
              font-weight: 600;
              line-height: 1.5;
              letter-spacing: 0.15px;
              color: $grey-4;
              gap: 10px;
              margin-bottom: 0;
              display: flex;
              align-items: center;
            }
          }

          .remember-head-body {
            padding: 26px 35px 6px 22px;

            ul {
              padding: 0 0 0 25px;

              li {
                &::marker {
                  color: $blue-3;
                }

                font-size: 16px;
                font-weight: 300;
                line-height: 24px;
                letter-spacing: 0.16px;
                color: $grey-1;
                margin-bottom: 20px;

                .guide-link {
                  color: $blue-3;
                  font-weight: 600;
                }
              }
            }
          }
        }

        .guide-modal-footer {
          border-top: 1px solid #DEE2E6;
          color: #6C757D;
          font-size: 14px;
          text-align: left;
        }
      }
    }

    @media only screen and (max-width: 991px) {
      .modal-dialog {
        .modal-body {
          padding: 30px 15px 14px 15px;

          .guide-modal-header {
            .guide-modal-content {
              width: 100%;
            }
          }
        }
      }
    }

    @media only screen and (max-width: 767px) {
      .modal-dialog {
        .modal-body {
          padding: 30px 15px 14px 15px;
          max-width: 100%;

          .guide-modal-header {
            display: block;

            .guide-modal-content {
              width: 100%;
            }

            .guide-modal-video {
              display: none;
            }
          }
        }
      }
    }
  }

  .guide-with-year-modal {
    padding: 0 12px !important;

    .modal-dialog {
      max-width: 525px;
      margin-top: 140px;
      pointer-events: unset;

      .modal-body {
        padding: 30px 34px;
        height: auto;
        background-image: url(asset_path("layout/controllers/users/study_plan/guide_years_modal_bg.webp"));
        background-repeat: no-repeat;
        background-size: cover;
        min-height: 433px;
        border-radius: 6px;
        position: relative;

        .modal-title {
          color: $blue-2;
        }

        .modal-subtitle {
          color: #545E64;
        }

        .guide-version-label {
          color: $grey-2;
        }

        .dropdown {
          .dropdown-toggle::after {
            display: none;
          }

          .guide-select {
            padding: 10px;
            border-radius: 2px;
            border: solid 1px #D8E0E6;
            cursor: pointer;

            &:hover {
              border-color: #B7C3D1;
            }

            &.show {
              border-color: #1B78CF;
              color: #0573B4;
              background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 11l6-6 6 6'/%3e%3c/svg%3e") !important;
            }
          }

          li {
            border-bottom: 1px solid #D8E0E6;

            &:last-child {
              border-width: 0;
            }

            &.guide-item {
              color: #4D4D4D;
              border-radius: 4px;
              cursor: pointer;

              &:hover {
                background-color: #EEF6FB;
              }

              &.selected {
                background-color: #DEEBF3;

                .guide-label {
                  font-weight: 600;
                }
              }

              .guide-label {
                color: #4D4D4D;
                font-weight: normal;
                cursor: pointer;
              }
            }
          }
        }

        .footer-text {
          color: #2B343D;
          border-top: 1px solid $grey-6;
          padding: 20px 0 0 0;

          a.show-overlay {
            color: $blue-3 !important;

            &:hover {
              color: #8BC8E1 !important;
            }
          }
        }

        .save-btn {
          margin-bottom: 26px;
          width: 188px;
          margin-top: 35px;
          background-color: $blue-2;
          border-radius: 2px;
          border: 0;

          &:hover {
            background-color: $blue-1;
          }
        }

        .btn-close {
          position: absolute;
          top: 9px;
          right: 8px;
          z-index: 9;
          cursor: pointer;
        }
      }
    }

    @media only screen and (max-width: 991px) {
      .modal-dialog {
        .modal-body {
          padding: 30px 15px 14px 15px;
        }
      }
    }

    @media only screen and (max-width: 767px) {
      .modal-dialog {
        .modal-body {
          padding: 30px 15px 14px 15px;
          max-width: 100%;

          .save-btn {
            margin-bottom: 37px;
            width: 100%;
            margin-top: 15px;
          }
        }
      }
    }
  }

  .modal#calendar-unavailable-study-day,
  .modal#calendar-reshuffle-all-tasks {
    .modal-dialog {
      max-width: 625px;
      margin: 0.5rem auto;
    }

    .modal-body {
      padding: 0;

      img {
        display: block;
        margin: 0 auto;
      }

      h3 {
        line-height: 32px;
        margin: 32px 0 10px;
        padding: 0 82px;
      }

      p {
        color: $grey-2;
        font-size: 18px;
        line-height: 26px;
        font-weight: normal;
        padding: 0 82px;
      }
    }

    .modal-footer {
      padding: 0 0 44px;

      a {
        height: 44px;
      }
    }

    @media only screen and (max-width: 767px) {
      .modal-dialog {
        max-width: 355px;
      }

      .modal-body {
        p {
          padding: 0 35px;
        }
      }
    }

    @media only screen and (max-width: 413px) {
      .modal-dialog {
        margin-right: 24px;
        margin-left: 24px;
      }

      .modal-body {
        h3 {
          padding: 0 36px;
        }

        p {
          padding: 0 28px;
        }
      }
    }
  }

  .modal#calendar-reshuffle-skip-modal,
  .modal#want-more-tasks-for-day-modal {
    z-index: 1090;

    .modal-dialog {
      margin: 1.75rem auto;

      .modal-header {
        background-image: asset-url("layout/controllers/users/study_plan/calendar_view/tasks_modal_header_bg.svg");
        background-repeat: no-repeat;
        background-size: cover;
        padding: 0 42px 0 32px;

        .dots {
          position: absolute;
          right: 52px;
          top: 2px;
        }
      }

      .modal-body {
        padding: 32px 32px 40px;

        p {
          font-weight: normal;
          line-height: 1.63;
          color: $grey-2;
        }

        .numeration {
          font-size: 16px;
          border-radius: 50%;
          border: 2px solid $blue-4;
          width: 24px;
          height: 24px;
          line-height: 20px;
          margin-right: 18px;
          box-shadow: 0 5px 15px 0 rgba(183, 195, 209, 0.6);
        }

        table {
          margin: 40px 0 0;
          box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);

          .icon {
            cursor: pointer;
            color: $grey-6;
            font: normal normal normal 14px/1 "Font Awesome 5 Pro";
            font-size: 16px;

            &::before {
              content: "\f111";
              line-height: 20px;
            }

            &.active {
              font-size: 16px;
              color: $blue-3 !important;

              &::before {
                content: "\f192";
              }
            }
          }

          span.action-name {
            margin-top: 3px;
          }

          i {
            cursor: pointer;
            font-size: 11px;
            color: $grey-4;
            margin-left: 8px;

            &.fa-tag {
              margin-left: unset;
              color: unset;
              font-size: unset;
            }
          }

          thead {
            tr {
              th {
                &.reshuffle-all {
                  min-width: 165px;
                }

                &.skip-all {
                  min-width: 120px;
                }
              }
            }
          }

          tbody {
            tr {
              height: 71px;

              td {
                padding: 20px;

                .task-name {
                  letter-spacing: 0.48px;
                }

                .guide-tag {
                  font-size: 13px;
                  font-weight: 600;
                  color: #7C5718;
                  padding: 1px 9px;
                  background: #FFF6DC;
                  border-radius: 4px;
                  margin-left: 10px;
                  text-decoration: none;
                  text-align-last: center;
                  display: inline-flex;
                  align-items: center;

                  .optional-task {
                    display: block;
                  }

                  .optional {
                    display: none;
                  }

                  @media (max-width: 1200px) {
                    margin-left: 0;
                  }

                  @media (max-width: 768px) {
                    span.optional-task {
                      display: none;
                    }

                    span.optional {
                      display: block;
                    }
                  }
                }

                &:first-of-type {
                  padding-right: 20px;
                  border-left: 20px solid transparent;
                  padding-left: 0;
                }

                &:last-of-type {
                  padding-left: 20px;
                  border-right: 20px solid transparent;
                  padding-right: 0;
                }
              }
            }
          }
        }
      }

      .modal-footer {
        padding: 0 0 40px 0;

        .btn {
          width: 240px;
        }
      }

      @media only screen and (max-width: 1199px) {
        max-width: 688px;
      }

      @media only screen and (max-width: 767px) {
        max-width: 400px;

        .modal-header {
          padding: 24px 55px 24px 24px;

          .dots {
            right: 21px;
          }
        }

        .modal-body {
          padding-left: 20px;
          padding-right: 20px;

          table {
            margin: 40px 0 0;
            box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);

            i {
              margin-left: 0;
            }

            thead {
              tr {
                th {
                  padding: 20px 16px;

                  &.reshuffle-all,
                  &.skip-all {
                    min-width: 100px;
                  }
                }
              }
            }

            tbody {
              tr {
                td {
                  padding: 20px 16px;

                  &:first-of-type {
                    padding-right: 16px;
                    border-left: 16px solid transparent;
                  }

                  &:last-of-type {
                    padding-left: 16px;
                    border-right: 16px solid transparent;
                  }
                }
              }
            }
          }
        }
      }

      @media only screen and (max-width: 450px) {
        margin: 1.75rem 20px;
      }

      @media only screen and (max-width: 413px) {
        max-width: 280px;
        margin: 1.75rem auto;

        .modal-body {
          table {
            thead {
              tr {
                th {
                  padding: 20px 10px;

                  &.reshuffle-all,
                  &.skip-all {
                    min-width: 0;
                  }
                }
              }
            }

            tbody {
              tr {
                td {
                  padding: 20px 10px;

                  &:first-of-type {
                    padding-right: 10px;
                    border-left: 10px solid transparent;
                  }

                  &:last-of-type {
                    padding-left: 10px;
                    border-right: 10px solid transparent;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .modal#want-more-tasks-for-day-modal {
    .modal-dialog {
      .modal-footer {
        .btn {
          width: 203px;
          height: 44px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          margin: 0;

          &:last-child {
            margin-left: 24px;
          }
        }
      }

      @media only screen and (max-width: 767px) {
        .modal-header {
          padding: 24px;
        }

        .modal-footer {
          padding-left: 20px;
          padding-right: 20px;

          .btn {
            width: 100%;

            &:last-child {
              margin-left: 0;
              margin-top: 10px;
            }
          }
        }
      }
    }
  }

  #update-study-plan-confirmation-modal {
    .modal-dialog {
      .modal-content {
        padding-bottom: 40px;

        .modal-body {
          span {
            font-family: "proxima-nova";
            font-size: 18px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.44;
            letter-spacing: normal;
            text-align: center;
            color: $grey-2;
          }

          div {
            padding: 0 75px;
          }
        }

        .modal-footer {
          button,
          input {
            width: 160px;
          }

          button {
            margin-right: 24px;
          }
        }

        @media only screen and (max-width: 767px) {
          padding-bottom: 40px;

          .modal-body {
            div {
              padding: 0;
            }
          }
        }

        @media only screen and (max-width: 413px) {
          padding-bottom: 40px;

          .modal-body {
            div {
              padding: 0;
            }
          }

          .modal-footer {
            button,
            input,
            .button_to {
              width: 100%;
              margin: 0;
            }

            button {
              margin-bottom: 14px;
            }
          }
        }
      }

      @media only screen and (min-width: 768px) {
        min-width: 625px;
      }
    }
  }
}
