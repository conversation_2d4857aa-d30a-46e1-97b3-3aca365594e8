body[data-controller="users"][data-action="adjust_study_plan"],
body[data-controller="users"][data-action="customize_study_plan"] {
  .adjust-plan-form {
    border-radius: 2px;
    box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
  }

  .study-plan {
    padding: 32px 48px 40px 48px;

    &[data-legacy-course-enabled="false"] {
      .for-legacy {
        display: none !important;
      }

      .for-default {
        display: block;
      }
    }

    &[data-legacy-course-enabled="true"] {
      .for-legacy {
        display: block;
      }

      .for-default {
        display: none !important;
      }
    }

    .change-note {
      background-color: $red-4;
      padding: 10px 20px;
      margin-bottom: 32px;
      align-items: center;
      display: flex;

      i {
        font-size: 23px;
        color: $red-5;
      }

      span {
        margin-left: 25px;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.44;
        color: $red-5;

        &.span-note {
          margin: 0;
        }
      }
    }

    .adjust-study-plan-settings {
      .view-preferences-wrapper,
      .date-time-preferences-and-availability-wrapper {
        margin-top: 40px;
        border-top: 1px solid $grey-6;
        padding-top: 40px;
      }

      .view-preferences-wrapper {
        section#view-preferences {
          .study-plan-view-card-wrapper {
            margin: 0 !important;
          }
        }
      }

      .date-time-preferences-and-availability-wrapper {
        section#availability {
          #calendar {
            #header {
              #month-nav {
                #prev-month,
                #next-month {
                  span {
                    span.hidden-mobile {
                      display: none;
                    }
                  }
                }
              }
            }

            #weeks-wrapper {
              .week {
                .date {
                  height: 97px;

                  @media only screen and (max-width: 1199px) {
                    height: 94px;
                  }

                  @media only screen and (max-width: 991px) {
                    height: 80px;
                  }

                  @media only screen and (max-width: 767px) {
                    height: 60px;
                  }
                }
              }
            }
          }
        }

        section#date-time-preferences {
          .hours-and-start-date-wrapper {
            .hours-wrapper {
              .choice-card {
                &.per-week {
                  margin-right: 0;
                }

                &.per-day {
                  margin: 50px 0 0 0;
                }
              }
            }
          }
        }
      }
    }

    .adjust-plan-mission-reminder,
    .adjust-accelerated-study-plan,
    .adjust-show-og-questions {
      margin-top: 40px;
      border-top: 1px solid $grey-6;
      padding-top: 40px;
    }

    .adjust-accelerated-study-plan {
      .recommendation-content {
        padding: 18px 24px;
        border-radius: 2px;

        @media only screen and (max-width: 767px) {
          padding-right: 20px;
          padding-left: 20px;
        }

        p {
          font-weight: normal;
          color: $grey-2;
          line-height: 1.5;
        }

        ul {
          list-style: none;
          margin: 0;
          padding-left: 26px;
          padding-right: 10px;

          li {
            position: relative;

            &:not(:last-child) {
              margin-bottom: 10px;
            }

            p {
              margin-bottom: 0;

              img {
                display: block;
                position: absolute;
                top: 4px;
                left: -27px;
              }
            }
          }
        }
      }

      .button-wrapper {
        display: flex;
        justify-content: flex-end;

        .update-form-field-value {
          border: solid 1px $grey-6;
          background-color: $white;
          color: $dark-1;
          font-size: 16px;
          font-weight: normal;
          opacity: 1 !important;
          border-radius: 4px;
          margin-left: 10px;

          &:hover,
          &.active {
            border-color: $blue-1;
            background-color: $primary-5;
            color: $blue-1;
            outline: none;
            box-shadow: none;
          }
        }
      }
    }
  }

  .title-box {
    a {
      color: $blue-quant-2;
    }

    h2 {
      color: $blue-quant-2;
    }
  }

  .update-form-field-value.active {
    .study-plan-sections {
      border-color: $primary-2;
      background-color: $primary-5;
    }
  }

  .study-plan-sections {
    width: 100%;
    height: 118px;
    flex-grow: 0;
    margin: 0;
    padding: 28px 0 29.2px;
    border-radius: 4px;
    border: solid 1px $grey-6;
    background-color: $white;
    text-align: center;
    cursor: pointer;

    &:hover {
      border-color: $primary-2;
      background-color: $primary-5;
    }

    .active {
      border-color: $primary-2;
      background-color: $primary-5;
    }

    .update-form-field-value {
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      color: $dark-1;
    }

    .update-form-field-value.active {
      background-color: transparent;
      color: $primary-1;
    }

    &:hover .update-form-field-value {
      color: $primary-1;
    }
  }

  .label-heading label {
    color: $grey-2;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
  }

  .label-heading {
    color: $grey-2;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
  }

  .mission-reminder-text,
  .show-og-questions-text {
    color: $grey-2;
    font-size: 16px;
  }

  .mission-reminder,
  .show-og-tasks {
    display: flex;
    justify-content: flex-end;

    .update-form-field-value {
      border: solid 1px $grey-6;
      background-color: $white;
      color: $dark-1;
      font-size: 16px;
      font-weight: normal;
      opacity: 1 !important;
      border-radius: 4px;
      margin-left: 10px;

      &:hover,
      &.active {
        border-color: $blue-1;
        background-color: $primary-5;
        color: $blue-1;
        outline: none;
        box-shadow: none;
      }
    }
  }

  .modal#calendar-regenerate-confirmation-modal {
    z-index: 99999;
    padding-right: 0 !important;

    .modal-dialog {
      max-width: 440px;

      .modal-body {
        h3 {
          margin-top: 30px;
        }

        p {
          margin-top: 12px;
          font-weight: normal;
          line-height: 1.44;
          font-size: 18px;
          color: $grey-2;
        }
      }

      .modal-footer {
        .btn {
          width: 137px;
          height: 44px;
        }
      }
    }
  }

  .course-card-wrapper {
    .course-selection {
      gap: 10px;

      .study-plan-course-card {
        border-radius: 4px;
        border: solid 1px $grey-6;
        padding: 10px 12px;
        background-color: $white;
        position: relative;
        width: 50%;
        min-height: 118px;
        cursor: pointer;

        i {
          position: absolute;
          right: 14px;
          top: 14px;
          font-size: 24px;

          &.fa-check-circle {
            color: $gmat-2;
          }

          &.fa-circle {
            color: $grey-6;
          }

          &.unchecked {
            display: block;
          }

          &.checked {
            display: none;
          }
        }

        .header-course {
          display: flex;
          align-items: center;

          img {
            margin-top: -10px;
            margin-left: -15px;
          }

          .gmat {
            background-color: $gmat-1;
            font-size: 14px;
            font-weight: 600;
            color: $white;
            padding: 5px 10px;
            box-shadow: 2px 2px 5px #B0E2FF;
          }
        }

        .body-course {
          p {
            font-family: "proxima-nova";
            font-size: 16px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.38;
            letter-spacing: normal;
            text-align: left;
            color: $gmat-0;
            margin-bottom: 0;
          }
        }

        &.active {
          border-color: $gmat-2;
          background-color: $gmat-5;

          i {
            &.unchecked {
              display: none;
            }

            &.checked {
              display: block;
              background: white;
              border-radius: 50%;
            }
          }
        }
      }
    }
  }

  .modal#exit-study-plan-modal {
    .modal-dialog {
      max-width: 430px;
      border-radius: 4px;

      .modal-body {
        padding: 20px 50px;

        img {
          width: 107.7px;
          margin-bottom: 10px;
        }
      }

      .modal-footer {
        .btn {
          width: 137px;
        }
      }
    }
  }

  @media (min-width: 992px) and (max-width: 1200px) {
    .mission-reminder-text,
    .show-og-questions-text {
      max-width: 98%;
    }

    .course-card-wrapper {
      .course-selection {
        .study-plan-course-card {
          .header-course {
            flex-direction: column;
            align-items: flex-start;
          }
        }
      }
    }
  }

  @media (min-width: 767px) and (max-width: 992px) {
    .study-plan {
      .adjust-plan-content {
        padding-right: 15px;
        border-right: solid 0 $grey-6;
      }

      .adjust-plan-test-date {
        padding-left: 15px;
      }
    }

    .adjust-plan {
      padding-left: 8px;
      padding-right: 8px;
      margin-bottom: 25px;

      .section-link {
        flex: 0 0 auto;
        width: 33.3333333333%;
        padding-left: 5px;
        padding-right: 5px;
      }
    }

    .score-bar-container {
      border: 1px solid $grey-6 !important;
      border-radius: 2px !important;
      margin-bottom: 20px;
    }

    #score-bar {
      display: flex !important;
      text-align: center !important;
      padding: 15px 0 !important;
      flex-direction: inherit !important;
    }

    #score-bar .score {
      flex-grow: 1 !important;
      color: $grey-2 !important;
      font-size: 16px !important;
      cursor: pointer !important;
      flex-direction: column !important;
    }

    #score-bar .score .score-range {
      padding: 0;
    }

    #score-bar a .icon {
      order: 2;
      margin-bottom: 5px;
    }

    #score-bar a p {
      order: 1;
    }

    .border-top.mt-5.pt-4 {
      margin-top: 0 !important;
    }
  }

  @media only screen and (max-width: 767px) {
    .study-plan {
      padding: 32px 24px;

      .adjust-plan-content {
        padding-right: 15px;
        border-right: solid 0 $grey-6;
      }

      .adjust-plan-test-date {
        padding-left: 15px;
      }

      .adjust-accelerated-study-plan {
        .button-wrapper {
          justify-content: space-between;

          .update-form-field-value {
            width: 100%;

            &:first-child {
              margin-left: 0;
            }
          }
        }
      }
    }

    .adjust-plan {
      padding-left: 8px;
      padding-right: 8px;
      margin-bottom: 25px;

      .study-plan-sections {
        font-size: 16px;
        justify-content: center;
        padding-left: 13px;
        padding-right: 13px;
        height: 69px;
        line-height: 18px;

        img {
          display: none;
        }
      }

      .section-link {
        flex: 0 0 auto;
        width: 33.3333333333%;
        padding-left: 5px;
        padding-right: 5px;
      }
    }

    .mobile-border {
      border-top: solid 1px $grey-6;
      padding-top: 20px;
      margin-top: 10px;
    }

    .border-top.mt-5.pt-4 {
      margin-top: 0 !important;
    }

    .mission-reminder,
    .show-og-tasks {
      justify-content: space-between;

      .update-form-field-value {
        width: 100%;
        margin-left: 10px;
        margin-top: 20px;

        &:first-child {
          margin-left: 0;
        }
      }
    }

    .course-card-wrapper {
      .course-selection {
        flex-direction: column;

        .study-plan-course-card {
          width: 100%;
        }
      }
    }
  }

  @media only screen and (max-width: 360px) {
    .adjust-plan {
      margin-bottom: 25px;

      .study-plan-sections {
        padding-left: 18px;
        padding-right: 18px;
        margin-bottom: 10px;

        img {
          display: none;
        }
      }

      .section-link {
        flex: 0 0 auto;
        width: 50%;
      }
    }

    .study-plan {
      padding: 32px 20px;
    }
  }
}
