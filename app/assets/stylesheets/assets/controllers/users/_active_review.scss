body[data-controller="users"][data-action="active_review"] {
  h2 {
    font-size: 18px;

    @media (min-width: 768px) {
      font-size: 22px;
      line-height: 35px;
      font-weight: normal;

      &.subsection-separator::after {
        content: "-";
        padding-left: 4px;
        padding-right: 4px;
        margin-left: 12px;
      }
    }
  }

  a {
    outline: none;
    text-align: center;

    &.go-back {
      color: $dark-1;
      width: 45px;
      height: 45px;
    }
  }

  .active-review-logo {
    margin: -7px 0 0 0;
  }

  .page-header-light {
    background-image: linear-gradient(to right, #DFE8FF 0%, $white 20%, $white 67%, #E5F5FF 100%);
    box-shadow: 0 2px 2px 0 rgba(183, 195, 209, 0.1);
    position: relative;

    .header-inner {
      .title-box {
        display: flex;
        align-items: flex-start;
        padding: 23px 10px 26px 0;

        .back-btn-chevron {
          width: 24px;
          height: 24px;
          margin-right: 13px;
          margin-top: 15px;

          &:hover {
            background-color: $light-5;
          }
        }

        .title-and-subtile-wrapper {
          .header-title {
            margin: 0;
          }

          .subtitle {
            margin-left: 8px;
          }

          .subtitle-accordion {
            .learn-more-link {
              font-size: 16px;
              cursor: pointer;
              text-decoration: none;

              &:hover {
                background-color: transparent;
              }

              .show-less-icon,
              .show-less-text {
                display: none;
              }

              .learn-more-text,
              .show-less-text {
                text-decoration: underline;
              }

              &[aria-expanded="true"] {
                .learn-more-text,
                .learn-more-icon {
                  display: none;
                }

                .show-less-text,
                .show-less-icon {
                  display: inline;
                }
              }

              &[aria-expanded="false"] {
                .learn-more-text,
                .learn-more-icon {
                  display: inline;
                }

                .show-less-text,
                .show-less-icon {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }

  .select-section-tab-group {
    .select-section-tab {
      display: flex;
      align-items: center;
      justify-content: center;
      width: unset !important;
      flex-grow: 1;
    }
  }

  .title {
    min-height: 84px;
    margin-bottom: 31px;

    .container {
      padding-top: 13px;
      padding-bottom: 0;
    }

    @media (min-width: 576px) {
      min-height: 120px;
    }

    @media (min-width: 768px) {
      margin-bottom: 35px;

      .container {
        padding-top: 28px;
        padding-bottom: 39px;
      }
    }
  }

  .section-content {
    min-width: 0;

    .content-quant,
    .content-verbal,
    .content-di,
    .content-ir,
    .content-awa,
    .content-bwa {
      max-height: 400px;
      overflow-y: scroll;
      margin-bottom: 61px;

      @media (min-width: 768px) {
        height: 55vh;
      }
    }

    .content-quant,
    .content-di,
    .content-verbal,
    .content-ir,
    .content-awa,
    .content-bwa,
    .content-custom {
      ol {
        overflow-y: auto;

        li {
          button.button-hover {
            min-width: 0;
            outline: none;
            border: 1px solid transparent;

            > div {
              flex-basis: 100%;
              overflow-x: hidden;
              max-height: 67px;
              padding-top: 17px;
              padding-bottom: 17px;
              border: 1px solid transparent;
              border-bottom-color: $grey-7;

              &:hover {
                border-bottom-color: transparent;
              }

              > span {
                &.numeration {
                  display: inline-block;
                  width: 28px;
                  height: 28px;
                  line-height: 28px;
                  font-size: 14px;
                  font-weight: 600;
                  text-align: center;
                }

                &:last-child {
                  overflow-x: hidden;
                }
              }

              > i {
                line-height: inherit;
              }
            }
          }

          &:last-child {
            button.button-hover > div {
              border-bottom-color: transparent !important;
            }
          }
        }
      }
    }
  }

  .download-all-button {
    outline: none;
    line-height: 33px;

    &:hover {
      text-decoration: underline;
    }

    i {
      font-size: 14px;
    }
  }

  .dropdown-container {
    button {
      span:first-child {
        max-width: 120px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    &.restricted-dropdown {
      button {
        @media (min-width: 1200px) {
          span {
            &.js-update-subsection-name {
              display: none !important;
            }

            &.js-update-subsection-acronim {
              display: inline !important;
            }
          }
        }

        @media (min-width: 1800px) {
          span {
            &:first-child {
              max-width: none;
            }

            &.js-update-subsection-name {
              display: inline !important;
            }

            &.js-update-subsection-acronim {
              display: none !important;
            }
          }
        }
      }
    }
  }

  .section-icon {
    width: 26px;
    height: 26px;
  }

  @media (max-width: 992px) {
    .page-header-light {
      .header-inner {
        .title-box {
          padding: 28px 10px 29px 0;
        }
      }
    }
  }

  @media (max-width: 575px) {
    .page-header-light {
      .header-inner {
        .title-box {
          padding: 15px 10px 12px 0;

          .back-btn-chevron {
            margin-right: 10px;

            i {
              font-size: 19px;
            }
          }
        }
      }

      .title-and-subtile-wrapper {
        width: calc(100% - 48px);
        gap: 12px !important;

        .subtitle-accordion {
          #active-review-subtitle-collapse {
            border-top: solid 1px #D8E0E6;
            border-bottom: solid 1px #D8E0E6;
            margin-left: -61px;
            width: calc(100% + 105px);
            padding: 24px 20px 23px 24px;
          }
        }
      }
    }
  }
}
