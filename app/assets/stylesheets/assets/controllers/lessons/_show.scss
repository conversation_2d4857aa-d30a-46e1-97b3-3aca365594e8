body[data-controller="lessons"],
body[data-controller="demo/lessons"],
body[data-controller="content_pages"] {
  &[data-action="show"],
  &[data-action="static_page"] {
    &.scroll-disabled {
      .edit-lesson-link {
        display: none !important;
      }
    }

    body.ai-assist-enabled {
      .intercom-launcher {
        opacity: 0;
      }
    }

    .edit-lesson-link {
      z-index: 1;
      background-color: #EB574B;
      color: white;
      text-transform: uppercase;
      padding: 0 15px;
      position: fixed;
      top: 69px;
      left: 7px;

      &:hover,
      &:focus {
        color: white;
        background-color: #39CA74;
      }
    }

    #lessons-wrapper {
      padding-top: 110px;
      background: #F7F7F7;

      &.ondemand-locked {
        filter: blur(5px);
        -webkit-filter: blur(5px);
      }

      &.clean {
        section#lesson-show {
          nav {
            &.lesson-nav {
              .see-all-topics {
                display: none !important;
              }
            }
          }
        }
      }

      &.no-navigation {
        .prev-topic,
        .next-topic,
        .icn-arrow-l,
        .icn-arrow-r {
          display: none !important;
        }
      }

      .icn-arrow-l,
      .icn-arrow-r {
        position: fixed;
        top: 50%;
        background-color: #FAFAFA;
        border: 1px solid #EBEBEB;
        padding: 10px 15px;
        color: #798291;
        height: 40px;
        width: auto;
        z-index: 3;
        line-height: 19px;
      }

      .icn-arrow-l {
        left: 0;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        padding-left: 10px;

        &::before {
          position: absolute;
          content: "\f104";
          font-size: 26px;
          color: #798291;
          font-family: "Font Awesome 5 Pro";
          line-height: 16px;
          transition: all 1s ease-in-out;
          -moz-transition: all 1s ease-in-out;
          -webkit-transition: all 1s ease-in-out;
        }

        span {
          font-weight: 700;
          text-transform: uppercase;
          font-size: 0;
          padding-left: 10px;
          opacity: 0;
          transition: all 1s ease-in-out;
          -moz-transition: all 1s ease-in-out;
          -webkit-transition: all 1s ease-in-out;
        }

        &:hover {
          width: auto;
          padding-left: 10px;

          &::before {
            color: $primary-2;
            padding-left: 20px;
            transition: all 0s ease-in-out;
            -moz-transition: all 0s ease-in-out;
            -webkit-transition: all 0s ease-in-out;
          }

          span {
            opacity: 1;
            transition: all 0s ease-in-out;
            -moz-transition: all 0s ease-in-out;
            -webkit-transition: all 0s ease-in-out;
            font-size: 1rem;
            padding-left: 40px;
          }
        }
      }

      .icn-arrow-r {
        right: 0;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;

        &::before {
          position: absolute;
          content: "\f105";
          font-size: 26px;
          color: #798291;
          font-family: "Font Awesome 5 Pro";
          line-height: 16px;
          right: 14px;
          top: 11px;
          transition: all 0.5s ease-in-out;
          -moz-transition: all 0.5s ease-in-out;
          -webkit-transition: all 0.5s ease-in-out;
        }

        span {
          font-weight: 700;
          text-transform: uppercase;
          font-size: 0;
          padding-right: 10px;
          opacity: 0;
          transition: all 1s ease-in-out;
          -moz-transition: all 1s ease-in-out;
          -webkit-transition: all 1s ease-in-out;
        }

        &:hover {
          width: auto;

          &::before {
            color: $primary-2;
            padding-right: 20px;
            transition: all 0s ease-in-out;
            -moz-transition: all 0s ease-in-out;
            -webkit-transition: all 0s ease-in-out;
          }

          span {
            opacity: 1;
            font-size: 1rem;
            padding-right: 40px;
            transition: all 0s ease-in-out;
            -moz-transition: all 0s ease-in-out;
            -webkit-transition: all 0s ease-in-out;
          }
        }
      }

      &.notes-disabled {
        span.notetaking-preselection {
          &:hover {
            background-color: transparent !important;
            cursor: default;

            img {
              border-color: transparent !important;
            }
          }
        }
      }

      &.bookmarks-disabled {
        .flags {
          > a {
            display: none;
          }
        }
      }
    }

    #locked-lesson {
      pointer-events: none;

      .locked-tasks {
        -webkit-backdrop-filter: blur(18px);
        backdrop-filter: blur(18px);
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        height: 100%;

        .center-img {
          width: 100%;

          img {
            width: 60px;
          }
        }

        .locked-task-text {
          line-height: 1.33;
          width: 130px;
          text-align: center;
          margin-top: 5px;
        }
      }

      .locked-content {
        backdrop-filter: blur(8px);
        background-image: asset-url("layout/controllers/users/ondemand/locked_chapter_background.webp");
        min-height: 251px;
        position: relative;
        background-position: top;
        background-size: cover;
        display: flex;
        justify-content: center;
        align-items: center;
        border: solid 1px $grey-6;
        padding: 64px;

        .btn-close {
          top: 0;
          position: absolute;
          right: 0;
          padding: 20px;
        }

        .supercharge-text {
          color: $grey-1;
          text-align: center;

          img {
            width: 125px;
          }

          span.text-gold {
            color: #BB9961;
          }
        }

        a {
          &.upgrade-now-btn {
            background-image: linear-gradient(98deg, #454168 -5%, #3D4166 46%, #566082 97%);
            width: 280px;
            border-radius: 2px;
            margin: 0 20px;

            &:hover {
              background-image: linear-gradient(98deg, #566082 100%, #566082 100%, #566082 100%);
              color: $white;
            }

            @media (max-width: 585px) {
              margin: 0;
              min-width: 100%;
            }
          }

          &.back-to-self-study-btn {
            width: 280px;
            border-radius: 2px;
            color: #404167;
            border: solid 1px #404167;
            margin: 0 20px;

            @media (max-width: 585px) {
              margin: 0;
              min-width: 100%;
            }

            &:hover {
              background-color: #EAECF5;
              color: #404167;
            }
          }
        }
      }
    }

    a {
      cursor: pointer;
    }

    .lesson-nav {
      &.scrolled {
        padding: 0;
        height: 45px;

        .lesson-progress-bar,
        #note-guide-text,
        #bookmark-text,
        #fullscreen-text {
          display: none;

          &.mobile {
            display: none;
          }
        }
      }

      .nav-header {
        .far.fa-list-ul {
          font-size: 20px;
        }

        .rightside-links {
          li {
            position: relative;
            padding-left: 16px;
            padding-right: 16px;

            a {
              color: $grey-6;
              font-weight: 300;

              &:hover {
                color: $primary-3;
              }

              span {
                margin-left: 3px;
              }
            }

            &:first-child {
              padding-left: 0;
            }

            &:last-child {
              padding-right: 0;
            }
          }

          li + li {
            &::after {
              position: absolute;
              content: "";
              width: 1px;
              height: 100%;
              background: $grey-4;
              top: 0;
              left: 0;
            }
          }

          a.flag {
            i:first-child {
              display: block;
            }

            i:last-child {
              display: none;
            }

            &.flagged {
              i:first-child {
                display: none;
              }

              i:last-child {
                display: block;
              }
            }
          }
        }

        .leftside-links {
          .see-all-topics {
            position: relative;

            i {
              color: $grey-6;
            }
          }

          .archived-lesson {
            margin-left: 30px;

            h4 {
              color: $grey-10;
              font-size: 18px;
            }

            i {
              color: $grey-10;
              font-size: 14px;
            }

            p {
              color: $primary-2 !important;
              font-size: 14px;
            }
          }
        }
      }
    }

    .lesson-body {
      .coordinate-geometry-lesson-bar {
        padding: 13px 12px 11px 35px;
        border: solid 1px $grey-6;
        background: linear-gradient(90deg, #EDE9F0 5%, #F5F5F5 12%, $blue-4 56%, #EDE5F3 100%);
        line-height: 1.31;
        letter-spacing: normal;
        margin: 0 auto;
        align-items: center;
        border-radius: 4px 4px 0 0;

        h6 {
          line-height: 1;
        }

        .info-image {
          background-image: image-url("layout/controllers/lessons/show/coordinate_geometry_info.png");
          width: 22px;
          height: 22px;
          background-position: center;
          background-size: 22px;
        }

        .btn-close {
          cursor: pointer;
          font-size: 11px;
          color: $blue-1;
        }

        + #lesson-content-wrapper {
          .lesson-content {
            border-radius: 0 0 4px 4px;
          }
        }
      }

      #lesson-content-wrapper {
        padding-bottom: 120px;

        &.ondemand-locked {
          filter: blur(6px);
          -webkit-filter: blur(6px);
        }

        .lesson-content {
          padding: 72px 110px 72px 105px;
          border-radius: 4px;
          border: 1px solid $grey-6;
          background: $white;

          &.bottom-corner {
            border-radius: 0 0 4px 4px;
          }

          .title-wrapper {
            margin-bottom: 24px;

            h1 {
              font-weight: 100;
              font-size: 44px;
            }
          }

          .subtitle-wrapper {
            margin-bottom: 24px;

            h2 {
              font-weight: 700;
              font-size: 24px;
            }
          }

          .lesson-description {
            p {
              font-size: 22px;

              a {
                color: $primary-1;
              }
            }

            &.content {
              > p {
                font-size: 22px;
              }
            }

            > p {
              line-height: 33px;
              color: #4D4D4D;
              margin-bottom: 25px;
            }

            svg {
              vertical-align: baseline;
            }

            .end-of-topic {
              margin-top: 90px;

              h3 {
                margin-top: 32.5px;
              }
            }
          }
        }

        .navigation {
          margin: 32px 0;

          .prev-topic,
          .next-topic {
            font-size: 14px;
            width: 260px;
            height: 44px;

            @media (max-width: 767px) {
              font-size: 14px !important;
            }
          }

          .next-topic-long {
            @media (max-width: 767px) {
              font-size: 11px !important;
            }
          }
        }

        &.pushed {
          width: calc(100% - 470px);
          margin-left: 470px;
          padding: 0 18px 40px 0;

          .lesson-content {
            padding: 72px 56px;
          }
        }
      }

      .lesson-sidebar {
        position: fixed;
        left: -400px;
        width: 400px;
        min-height: 100%;
        overflow: hidden;
        top: 69px;
        z-index: 99999;
        background: $grey-9;
        border: 1px solid $grey-6;
        transition: all 0.5s ease 0s;

        &.active {
          left: 0;
          width: 440px;
          max-width: 100%;
          position: fixed;
          background-color: #FAFAFA;
          z-index: 0;
          visibility: visible;

          @media (min-width: 1440px) {
            width: 440px;
          }
        }

        .title-wrapper {
          margin: 10px 14px 14px;
          padding: 18px 14px;

          .close-sidebar {
            cursor: pointer;
          }
        }

        ul.topics-list {
          width: 100%;
          height: 100%;
          overflow-x: hidden;
          overflow-y: auto;
          text-transform: capitalize;
          max-height: calc(100vh - 8rem);
          padding: 0 18px 14px 14px;

          li {
            .status {
              min-width: 12px;
              width: 12px;
              height: 12px;
              line-height: 12px;
              border-radius: 50%;
              text-align: center;
              font-size: 10px;
              z-index: 2;
              border: 3px solid $grey-5;
              background-color: $grey-10;
            }

            .bar {
              background-color: $grey-6;
            }

            .bookmark {
              color: $grey-5;
            }

            &.topic {
              position: relative;

              .bar {
                position: absolute;
                height: 100%;
                width: 2px;
                left: 15px;
                top: 20px;
                z-index: 1;
              }

              .topic-content-wrapper {
                padding: 10px 10px 15px 35px;

                .status {
                  position: absolute;
                  top: 16px;
                  left: 10px;
                }
              }

              h6 {
                line-height: 1.64;
              }

              &:last-child {
                margin-bottom: 50px;

                .bar {
                  display: none;
                }

                .sub-topic {
                  &:last-child {
                    .sub-topic-content-wrapper {
                      border-bottom: none !important;
                    }
                  }
                }
              }

              &.with-sub-chapters {
                .topic-content-wrapper {
                  padding-bottom: 10px;
                }
              }
            }

            &.sub-topic {
              .sub-topic-content-wrapper {
                padding: 10px 0;
                margin: 0 10px 0 35px;

                .status {
                  min-width: 6px;
                  width: 6px;
                  height: 6px;
                  line-height: 6px;
                  position: relative;
                  top: 3px;
                  left: -3px;
                }

                > a {
                  line-height: 1.64;
                  color: $grey-3;
                }
              }
            }

            &.examples-not-completed {
              > .topic-content-wrapper,
              > .sub-topic-content-wrapper {
                > .status {
                  background-color: $grey-10 !important;
                  border: none;
                  border-color: $red-1 !important;
                  color: $red-1 !important;
                  font-size: 14px;
                }
              }

              > .topic-content-wrapper {
                > .status {
                  left: 9px;
                }
              }

              > .sub-topic-content-wrapper {
                > .status {
                  left: -7px;
                  background-color: transparent !important;
                }
              }
            }
          }

          &.awa,
          &.bwa {
            li {
              &.topic {
                .bar {
                  &.filled {
                    background-color: $green-3;
                  }
                }

                &.completed {
                  .topic-content-wrapper {
                    .status {
                      background-color: $green-3;
                      border-color: $green-3;
                    }

                    &:hover {
                      .status {
                        border-color: $green-awa-1;
                        background-color: $green-awa-1;
                      }
                    }
                  }
                }

                .topic-content-wrapper {
                  &:hover {
                    h6 {
                      color: $green-awa-1 !important;
                    }

                    i {
                      color: $green-awa-1;
                    }

                    .status {
                      border-color: $green-awa-1;
                      background-color: $green-awa-4;
                    }
                  }
                }
              }

              &.sub-topic {
                &.completed {
                  .status {
                    background-color: $green-3;
                    border-color: $green-3;
                  }

                  a {
                    color: $green-awa-2;
                  }
                }

                &:hover {
                  .status {
                    border-color: $green-awa-1;
                  }

                  a,
                  i {
                    color: $green-awa-1;
                  }
                }
              }
            }
          }

          &.ir {
            li {
              &.topic {
                .bar {
                  &.filled {
                    background-color: $gold-ir-3;
                  }
                }

                &.completed {
                  .topic-content-wrapper {
                    .status {
                      background-color: $gold-ir-3;
                      border-color: $gold-ir-3;
                    }

                    &:hover {
                      .status {
                        border-color: $gold-ir-1;
                        background-color: $gold-ir-1;
                      }
                    }
                  }
                }

                .topic-content-wrapper {
                  &:hover {
                    h6 {
                      color: $gold-ir-1 !important;
                    }

                    i {
                      color: $gold-ir-1;
                    }

                    .status {
                      border-color: $gold-ir-1;
                      background-color: $gold-ir-4;
                    }
                  }
                }
              }

              &.sub-topic {
                &.completed {
                  .status {
                    background-color: $gold-ir-3;
                    border-color: $gold-ir-3;
                  }

                  a {
                    color: $gold-ir-2;
                  }
                }

                &:hover {
                  .status {
                    border-color: $gold-ir-1;
                  }

                  a,
                  i {
                    color: $gold-ir-1;
                  }
                }
              }
            }
          }

          &.di {
            li {
              &.topic {
                .bar {
                  &.filled {
                    background-color: $cyan-di-3;
                  }
                }

                &.completed {
                  .topic-content-wrapper {
                    .status {
                      background-color: $cyan-di-3;
                      border-color: $cyan-di-3;
                    }

                    &:hover {
                      .status {
                        border-color: $cyan-di-1;
                        background-color: $cyan-di-1;
                      }
                    }
                  }
                }

                .topic-content-wrapper {
                  &:hover {
                    h6 {
                      color: $cyan-di-1 !important;
                    }

                    i {
                      color: $cyan-di-1;
                    }

                    .status {
                      border-color: $cyan-di-1;
                      background-color: $cyan-di-4;
                    }
                  }
                }
              }

              &.sub-topic {
                &.completed {
                  .status {
                    background-color: $cyan-di-3;
                    border-color: $cyan-di-3;
                  }

                  a {
                    color: $cyan-di-2;
                  }
                }

                &:hover {
                  .status {
                    border-color: $cyan-di-1;
                  }

                  a,
                  i {
                    color: $cyan-di-1;
                  }
                }
              }
            }
          }

          &.quant {
            li {
              &.topic {
                .bar {
                  &.filled {
                    background-color: $blue-quant-3;
                  }
                }

                &.completed {
                  .topic-content-wrapper {
                    .status {
                      background-color: $blue-quant-3;
                      border-color: $blue-quant-3;
                    }

                    &:hover {
                      .status {
                        border-color: $blue-quant-1;
                        background-color: $blue-quant-1;
                      }
                    }
                  }
                }

                .topic-content-wrapper {
                  &:hover {
                    h6 {
                      color: $blue-quant-1 !important;
                    }

                    i {
                      color: $blue-quant-1;
                    }

                    .status {
                      border-color: $blue-quant-1;
                      background-color: $blue-quant-4;
                    }
                  }
                }
              }

              &.sub-topic {
                &.completed {
                  .status {
                    background-color: $blue-quant-3;
                    border-color: $blue-quant-3;
                  }

                  a {
                    color: $blue-quant-2;
                  }
                }

                &:hover {
                  .status {
                    border-color: $blue-quant-1;
                  }

                  a,
                  i {
                    color: $blue-quant-1;
                  }
                }
              }
            }
          }

          &.verbal {
            li {
              &.topic {
                .bar {
                  &.filled {
                    background-color: $purple-verbal-3;
                  }
                }

                &.completed {
                  .topic-content-wrapper {
                    .status {
                      background-color: $purple-verbal-3;
                      border-color: $purple-verbal-3;
                    }

                    &:hover {
                      .status {
                        border-color: $purple-verbal-1;
                        background-color: $purple-verbal-1;
                      }
                    }
                  }
                }

                .topic-content-wrapper {
                  &:hover {
                    h6 {
                      color: $purple-verbal-1 !important;
                    }

                    i {
                      color: $purple-verbal-1;
                    }

                    .status {
                      border-color: $purple-verbal-1;
                      background-color: $purple-verbal-4;
                    }
                  }
                }
              }

              &.sub-topic {
                &.completed {
                  .status {
                    background-color: $purple-verbal-3;
                    border-color: $purple-verbal-3;
                  }

                  a {
                    color: $purple-verbal-2;
                  }
                }

                &:hover {
                  .status {
                    border-color: $purple-verbal-1;
                  }

                  a,
                  i {
                    color: $purple-verbal-1;
                  }
                }
              }
            }
          }
        }
      }
    }

    #note-guide {
      .modal-dialog {
        max-width: 710px;

        .modal-header {
          border-bottom: 1px solid $grey-6;
          padding: 22px 32px;

          .close {
            background: transparent;
            border: none;
            font-size: 35px;
            position: absolute;
            right: 15px;
            color: $grey-4;
            font-weight: bold;
          }
        }

        .modal-body {
          padding: 32px;
          font-size: 16px;
          font-weight: 400;
          color: $grey-2;

          strong {
            color: $dark-1;
          }

          p:first-child {
            margin-bottom: 28px;
          }

          p.numbered-last {
            margin-bottom: 32px;
          }

          .numbered,
          .numbered-first,
          .numbered-last {
            padding-left: 30px;
            position: relative;
            margin-left: 15px;

            strong {
              left: 0;
              background: $primary-4;
              border-radius: 50%;
              padding: 3px 5px;
              color: $primary-2;
              position: absolute;
              width: 20px;
              height: 20px;
              top: 3px;
              text-align: center;
              line-height: 16.62px;
              font-size: 14px;
              font-weight: 700;
            }
          }
        }
      }
    }

    &.gre {
      span {
        &.marker {
          background-color: $grey-7;
        }
      }
    }

    &.gmat,
    &.ea {
      span {
        &.marker {
          background-color: yellow;
        }
      }
    }

    &.sat {
      .exercise {
        .tools {
          .reference-guide {
            width: 20px;
            margin-left: 10px;
            cursor: pointer;

            img {
              filter: invert(45%) sepia(7%) saturate(1219%) hue-rotate(175deg) brightness(95%) contrast(84%);
            }
          }
        }

        .line-container {
          display: inline-block !important;
        }
      }

      #reference-guide-modal {
        .modal-title {
          font-size: 20px;
        }

        .equations,
        .mobile-equations {
          img {
            width: 1000px;
          }
        }
      }
    }

    @media (min-width: 992px) and (max-width: 1280px) {
      .lesson-body {
        #lesson-content-wrapper {
          display: block !important;
          padding-bottom: 120px;

          .lesson-content {
            padding: 72px 56px;
          }
        }
      }
    }

    @media (min-width: 768px) and (max-width: 991px) {
      .lesson-body {
        #lesson-content-wrapper {
          padding-bottom: 120px;

          .lesson-content {
            padding: 72px 56px;
          }
        }

        .lesson-sidebar {
          &.active {
            width: 100%;

            .px-4 {
              padding: 16px 34px !important;
            }
          }
        }
      }
    }

    @media (min-width: 1201px) {
      .lesson-body {
        #lesson-content-wrapper {
          display: block !important;
        }
      }
    }

    @media (max-width: 1200px) {
      #note-guide-button span,
      .flags span,
      li.toggle-fullscreen {
        display: none !important;
      }

      .lesson-nav {
        .nav-header {
          .rightside-links {
            li:nth-child(2) {
              padding-right: 0;
              margin-right: -5px;
            }
          }
        }
      }
    }

    @media (max-width: 991px) {
      .lesson-nav {
        position: fixed;
        top: 0;
        width: 100%;

        .nav-header {
          .leftside-links {
            .archived-lesson {
              &::before {
                height: 47px;
              }
            }
          }
        }

        .progress-percentage {
          .hidden-mobile {
            display: none;
          }
        }
      }

      .lesson-body {
        #lesson-content-wrapper {
          padding: 0 8px 86px 8px;

          .lesson-content {
            .lesson-description {
              > p {
                margin-bottom: 12px;
              }
            }
          }
        }

        .coordinate-geometry-lesson-bar {
          align-items: center;

          .coordinate-geometry-wrapper {
            align-items: start !important;
            gap: 13px;

            h6 {
              display: flex;
              flex-direction: column;
              line-height: 1.31;

              span {
                margin-left: 0 !important;
                padding-left: 0 !important;
              }
            }
          }
        }
      }
    }

    @media (max-width: 767px) {
      .lesson-progress-bar {
        &.desktop {
          display: none !important;
        }

        &.mobile {
          display: block;
        }
      }

      .icn-arrow-r,
      .icn-arrow-l {
        display: none !important;
      }

      .lesson-nav {
        position: fixed;
        top: 0;
        width: 100%;

        .nav-header {
          .leftside-links {
            .nav-items-back {
              position: relative;
              z-index: 9;
            }

            li {
              .navigator-back {
                padding-right: 10px;
              }

              .see-all-topics {
                padding-left: 12px;
              }
            }
          }

          .rightside-links {
            margin-right: 5px;
          }
        }
      }

      #lessons-wrapper {
        padding-top: 130px;

        .lesson-body {
          padding: 0 8px;

          .coordinate-geometry-lesson-bar {
            padding: 13px 12px 11px 15px;

            .coordinate-geometry-wrapper {
              h6 {
                flex-direction: row !important;
                flex-wrap: wrap;
                display: block;
                width: calc(90% - 35px);

                span {
                  display: inline !important;
                }
              }
            }
          }

          #lesson-content-wrapper {
            padding: 0 0 86px 0;

            .lesson-content {
              padding: 0 20px 20px;

              .title-wrapper {
                margin: 38px 0 32px 0;

                h1 {
                  font-size: 30px;
                }
              }

              .subtitle-wrapper {
                margin: 32px 0 18px;

                h2 {
                  font-weight: bold;
                  font-size: 20px;
                }
              }

              .lesson-description {
                p {
                  font-size: 18px;
                }

                > p {
                  margin: 18px 0 20px;
                  line-height: 24px;
                }
              }
            }
          }

          .info-box {
            &.success {
              &.with-icon {
                padding-left: 28px;

                &::before {
                  display: none;
                }
              }
            }

            p {
              line-height: 25px;
            }

            strong {
              font-size: 16px;
            }
          }

          .lesson-sidebar {
            top: 62px;

            &.active {
              width: 100%;
            }
          }
        }
      }

      &.sat {
        .exercise {
          .header {
            border: none;
          }
        }
      }
    }
  }

  &.ondemand {
    .tooltip-wrapper {
      position: absolute;
      z-index: 9999;
      text-align: center;
      right: 75px;
      top: -90px;
      display: none;

      .tooltip-container {
        background-color: $dark-1;
        padding: 5px 10px;
        text-align: left;
        border-radius: 1px;
        font-weight: 400;
        font-size: 14px;

        .text {
          color: $light-5;
          max-width: 300px;
          font-size: 16px;
        }
      }

      i.down-arrow-icon {
        color: #000 !important;
        font-size: 20px;
        position: relative;
        top: -8px;
      }
    }

    .lesson-body {
      #lesson-content-wrapper {
        .lesson-content {
          .lesson-description {
            .read-text-wrapper {
              position: relative;
              display: flex;
              align-items: center;

              .read-lesson-img {
                margin-right: 15px;

                img {
                  min-width: 26px;
                }
              }

              .read-lesson-border {
                background-image: linear-gradient(to right, #BEC6DC, #A9BBEA);
                height: 1px;
                margin-right: 60px;
                width: 100%;
              }

              .dots-image {
                position: absolute;
                right: 0;
              }
            }

            .read-text-lesson-container {
              width: 100%;
              height: auto;
              flex-grow: 0;
              margin: 0 83px 35px 0;
              padding: 10px 7.3px 10px 20px;
              border-radius: 4px;
              border-style: solid;
              border-width: 1px;
              border-image-source: linear-gradient(94deg, #DFE0FF -5%, rgba(223, 224, 255, 0) 93%);
              border-image-slice: 1;
              background-image: linear-gradient(to right, #EDEFF8 0%, #FBFCFE 99%), linear-gradient(94deg, #DFE0FF -5%, rgba(223, 224, 255, 0) 93%);
              background-origin: border-box;

              span.read-the-text {
                color: #3734A9;
                white-space: nowrap;
                margin-right: 20px;
              }

              @media (max-width: 400px) {
                padding-left: 10px;

                span.read-the-text {
                  font-size: 16px;
                }
              }
            }

            @media (max-width: 767px) {
              .read-text-wrapper {
                .read-lesson-border {
                  display: none;
                }
              }
            }

            .ondemand-video-lesson {
              width: 100%;
              height: 100%;
              flex-grow: 0;
              margin: 0 33px 56px 0;
              border-radius: 4px;
              box-shadow: 0 4px 15px 0 rgba(188, 200, 214, 0.3);
              border: solid 1px $grey-6;
              background-image: linear-gradient(153deg, #E8EBF5 4%, $white 80%);
              justify-content: space-between;

              .lesson-heading {
                justify-content: space-between;
                padding: 12px 20px;
                align-items: center;

                .video-lesson-header {
                  align-items: center;

                  .play-video-icon {
                    margin-right: 15px;

                    img {
                      min-width: 26px;
                    }
                  }

                  .watch-ondemand-lesson {
                    white-space: nowrap;
                    padding-right: 20px;
                  }
                }

                span.watch-ondemand-lesson {
                  color: #3734A9;
                }

                .read-lesson-border {
                  background-image: linear-gradient(to right, #BEC6DC, #A9BBEA);
                  height: 1px;
                  margin-right: 20px;
                  width: 100%;
                }

                .timer-wrapper {
                  white-space: nowrap;
                  align-items: center;

                  .timer-icon {
                    img {
                      min-width: 29px;
                    }
                  }
                }
              }

              .video-wrapper {
                margin-top: 0;
                margin-bottom: 22px;

                &.ondemand-lesson {
                  margin-right: 20px;
                  margin-left: 20px;
                  border-radius: 4px;
                  overflow: hidden;
                }
              }

              .video-container-note {
                width: 100%;
                height: auto;
                flex-grow: 0;
                margin: 22px 0 0;
                padding: 8px 12px !important;
                background-color: #DDF4FD;
                position: relative;
                justify-content: center !important;

                .video-note-wrapper h6 {
                  color: #0D6295;
                }

                .btn-close {
                  position: absolute;
                  right: 13px;
                }

                .question-image img {
                  width: 28px;
                  height: 28px;
                }

                &:hover {
                  .tooltip-wrapper {
                    display: block;
                  }
                }
              }

              @media (max-width: 991px) {
                .lesson-heading {
                  .read-lesson-border {
                    display: none;
                  }
                }

                .video-container-note {
                  .video-note-wrapper {
                    align-items: flex-start !important;
                  }

                  .question-image img {
                    margin-top: -4px;
                  }

                  &:hover {
                    .tooltip-wrapper {
                      top: -90px;
                      display: block;
                    }
                  }
                }
              }

              @media (max-width: 767px) {
                .lesson-heading {
                  align-items: flex-start;

                  .video-lesson-header {
                    align-items: flex-start;

                    .watch-ondemand-lesson {
                      white-space: inherit;
                      font-size: 18px;
                      line-height: 1.44;
                    }
                  }
                }

                .video-container-note {
                  .video-note-wrapper {
                    width: calc(100% - 40px);

                    .question-image {
                      width: 30px;
                    }

                    h6 {
                      font-size: 16px;
                      width: calc(100% - 30px);
                    }

                    .question-image img {
                      margin-top: -8px;
                    }

                    .btn-close {
                      position: absolute;
                      right: 8px;
                      font-size: 13px !important;
                      margin-top: -7px;
                    }
                  }

                  &:hover {
                    .tooltip-wrapper {
                      left: 0;
                      right: 0 !important;
                      margin: 0 auto;
                      max-width: 300px;
                      top: -115px;
                      display: block;
                    }
                  }
                }
              }

              @media (max-width: 575px) {
                .video-container-note {
                  justify-content: space-between !important;
                  align-items: center !important;
                }
              }

              @media (max-width: 400px) {
                .lesson-heading {
                  .video-lesson-header {
                    .watch-ondemand-lesson {
                      font-size: 16px;
                      padding-right: 10px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

#concept-mastery-modal {
  .modal-dialog {
    max-width: 625px;

    .modal-body {
      padding: 10px 30px;

      p {
        color: $grey-2;
      }
    }

    .modal-footer {
      padding: 12px 30px 24px 30px;
    }
  }
}
