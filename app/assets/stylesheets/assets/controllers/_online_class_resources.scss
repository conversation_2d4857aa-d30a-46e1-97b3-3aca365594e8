body[data-controller="online_class_resources"] {
  &[data-action="library"] {
    background-color: $light-1;

    .liveteach-cohort-library {
      margin-bottom: auto;

      .header {
        width: 100%;
        box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.3);
        background-color: $light-5;
        position: relative;

        .header-container {
          padding-top: 17px;
          padding-bottom: 17px;
          padding-left: 0;
          margin: 0 auto;

          .back-link {
            color: #1B78CF;
            padding: 5px 15px;

            &:hover {
              color: $primary-2;
            }
          }

          .header-info {
            .description {
              font-size: 16px;
              margin-top: 10px;
              max-width: 935px;

              @media (min-width: 1800px) {
                max-width: unset;
              }
            }
          }
        }
      }

      .separator {
        height: 1px;
        margin: 24px 0 17px 0;
        background-color: $grey-6;
      }

      .search-result {
        margin-bottom: 22px;
      }

      .body {
        .body-container {
          padding-top: 15px;
          padding-bottom: 15px;
          margin: 0 auto;

          .input-group {
            position: relative;

            .search-icon {
              position: absolute;
              left: 10px;
              top: 13px;
              z-index: 9;
            }

            #cohort-search-form {
              width: 100%;
            }

            .search-input {
              font-size: 16px;
              padding: 12px 0 12px 42px;
              border-radius: 2px;
              border: solid 1px $grey-6;
              background-color: $white;
            }
          }
        }

        ul.list-instructors {
          &.invisible-container {
            visibility: hidden;
            height: 0;
            overflow: hidden;
          }

          .instructor-card {
            display: flex;
            background: linear-gradient(to left, #9975FF, #7BC8FF, #7BC4FF, #7D7BFF);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 26px;
            border: 1px solid transparent;
            position: relative;
            margin-top: 8px;
            cursor: pointer;

            &::before {
              background: linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 45%, rgba(244, 246, 248, 1) 70%);
              width: 100%;
              height: 100%;
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              z-index: 0;
            }

            &::after {
              width: 4px;
              height: 100%;
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              z-index: 5;
              background-image: linear-gradient(176deg, #9975FF 4%, rgba(66, 207, 251, 0.41) 75%);
            }

            &:hover {
              border: solid 1.3px #4243FB;
            }

            div {
              position: relative;
            }

            .instructor-details {
              width: 100%;

              .instructor-image-container {
                width: 90px;
                height: 99px;

                .instructor-image {
                  margin-top: -9px;
                }
              }

              .description {
                padding: 21px;
                width: 100%;

                .instructor-detail {
                  line-height: 1.7;
                  text-align: left;
                  color: $blue-1;
                  width: calc(100% - 235px);
                  padding-right: 15px;

                  .instructor-name {
                    line-height: 22px;
                  }

                  .instructor-title {
                    color: #545E64;
                    padding-right: 12px;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                  }

                  .instructor-bio-link {
                    color: $blue-3;
                    border-left: 1px solid $grey-6;
                    padding-left: 12px;
                    text-decoration: underline;
                    text-decoration-color: $blue-4;
                    white-space: nowrap;

                    a {
                      &:hover {
                        color: $primary-3;
                      }
                    }
                  }
                }

                .classes-available {
                  width: 235px;
                  justify-content: flex-end;

                  i {
                    color: $grey-4;
                  }
                }
              }
            }

            .chevron-right {
              border-left: solid 1px $grey-5;
              min-height: 77px;
              padding: 0 27px;
              display: flex;
              align-items: center;
            }
          }
        }

        .cohort-library-video-card-container {
          &.invisible-container {
            visibility: hidden;
            height: 0;
            overflow: hidden;
          }

          .online-class-session-recording {
            .modal-dialog {
              .modal-content {
                .modal-header {
                  .bookmark-icon-link {
                    .bookmark-icon-group {
                      padding: 3px 6px 0 6px;

                      &:hover {
                        background-color: $blue-5;
                      }
                    }

                    &.flagged i.far {
                      display: none;
                    }

                    &:not(.flagged) i.fas {
                      display: none;
                    }
                  }

                  padding-left: 34px;
                }
              }
            }
          }
        }

        .cohort-library-video-card {
          display: flex;
          background-image: linear-gradient(132deg, $white 44%, rgba(255, 255, 255, 0.6) 108%);
          border-radius: 8px;
          overflow: hidden;
          margin-bottom: 25px;

          .card-thumbnail {
            position: relative;
            width: 90px;
            height: 134px;

            .instructor-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .play-button {
              position: absolute;
              bottom: 0;
              left: 0;
              border: none;
              border-radius: 50%;
              padding: 5px;
              font-size: 12px;
              cursor: pointer;

              .play_icon {
                width: 30px;
              }
            }
          }

          .card-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 18px 17px 11px 15px;
            width: calc(100% - 90px);

            .card-title {
              font-size: 14px;
              font-weight: bold;
              line-height: 1.2;

              .topic-title {
                line-clamp: 3;
                overflow: hidden;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                display: -webkit-box;
                margin-bottom: 15px;

                p {
                  font-weight: 600;
                  margin-bottom: 0;
                }
              }
            }

            .instructor-name {
              font-size: 14px;
              color: #666;
              margin-bottom: 0;
            }
          }
        }
      }
    }

    @media (max-width: 991px) {
      .liveteach-cohort-library {
        .body {
          .body-container {
            ul.list-instructors {
              .instructor-card {
                .instructor-details {
                  .description {
                    padding: 21px 21px 26px 21px;
                  }
                }

                .chevron-right {
                  padding: 0 20px;
                }
              }
            }
          }

          .cohort-library-video-card-container {
            .online-class-session-recording {
              .modal-dialog {
                .modal-content {
                  .modal-header {
                    .bookmark-icon-link {
                      .bookmark-icon-group {
                        &:hover {
                          background-color: transparent;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    @media (max-width: 767px) {
      .liveteach-cohort-library {
        .header {
          .header-container {
            padding: 16px 20px 17px 0;

            .title-box {
              .title {
                font-size: 26px;
              }
            }
          }
        }

        .body {
          .body-container {
            padding: 0 20px;

            ul.list-instructors {
              .instructor-card {
                .chevron-right {
                  min-height: 130px;
                  width: 46px;
                }

                .instructor-details {
                  width: calc(100% - 46px);

                  .description {
                    flex-direction: column;
                    padding: 23px 5px 23px 21px;
                    width: calc(100% - 90px);

                    .instructor-detail {
                      width: calc(100% - 0px);

                      .instructor-bio-link {
                        border: none;
                        padding: 0;
                      }
                    }

                    .classes-available {
                      width: auto;
                      margin-top: 8px;

                      .class-available-title {
                        font-size: 16px;
                      }
                    }
                  }

                  .instructor-image-container {
                    width: 90px;
                    height: 171px;

                    .instructor-image {
                      height: 100% !important;
                      margin-top: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    @media (max-width: 410px) {
      .liveteach-cohort-library {
        .body {
          .body-container {
            ul.list-instructors {
              .instructor-card {
                .instructor-details {
                  .description {
                    padding-left: 10px;

                    .classes-available {
                      font-size: 14px;
                      gap: 4px !important;

                      .class-available-title {
                        font-size: 14px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  &[data-action="cohort_video_repository"] {
    background-color: $light-1;

    section.instructors-cohort-video-repository {
      margin-bottom: auto;

      .header {
        width: 100%;
        box-shadow: 0 2px 2px 0 rgba(183, 195, 209, 0.1);
        background-color: $light-5;
        padding: 16px 0 18px 0;

        .header-container {
          margin: 0 auto;
          padding-left: 0;
        }

        .cohort-card {
          .instructor-info {
            align-items: center;
            gap: 27px;
            padding-right: 12px;

            .back-arrow {
              .back-link {
                padding: 20px;
                color: #1B78CF;

                &:hover {
                  color: $primary-2;
                }
              }
            }

            .instructor-image {
              width: 49px;
              height: 49px !important;
              border: solid 1px #6967CC;
              border-radius: 50%;
              object-fit: cover;
            }

            .instructor-info-block {
              flex-direction: column;
              padding-left: 11px;

              .instructor-name {
                color: #154261;
                margin-bottom: 5px;
              }

              .instructor-title {
                color: #545E64;
                padding-right: 12px;
                font-size: 16px;
              }

              .instructor-bio-link {
                border-left: 1px solid $grey-6;
                padding-left: 12px;
                text-decoration: underline;
                text-decoration-color: $blue-4;

                a {
                  &:hover {
                    color: $primary-3;
                  }
                }
              }
            }
          }
        }
      }

      .body {
        background-color: $light-1;
        padding-top: 28px;

        .body-container {
          margin: 0 auto;
          padding-bottom: 50px;

          .input-group {
            position: relative;

            .search-icon {
              position: absolute;
              left: 10px;
              top: 9px;
              z-index: 9;
            }

            .search-topic-input {
              padding-left: 45px;
              font-size: 16px;
            }
          }

          .search-result-separator {
            height: 1px;
            margin: 36px 0 18px 0;
            background-color: $grey-6;
          }

          ul.recorded-video-list {
            .list-group {
              position: relative;
              padding-top: 26px;
              background-color: transparent;
              border: none;
              width: 100%;

              &:first-child {
                .bar {
                  top: 76px;
                }
              }

              &:last-child {
                .bar {
                  height: 98px;
                }
              }

              &:only-child {
                .bar {
                  height: 0;
                }
              }

              .bar {
                position: absolute;
                display: block;
                border-left: 2px solid $grey-5;
                top: 0;
                height: 100%;

                &.completed {
                  border-color: #57C89C;
                }
              }

              .indicator {
                position: absolute;
                display: block;
                border-radius: 50%;
                border: 5px solid transparent;
                width: 30px;
                height: 30px;
                left: -15px;
                top: 71px;
                line-height: 20px;
                text-align: center;
                color: $white;
                font-size: 15px;
                text-transform: uppercase;

                .status {
                  background: $light-1;
                  width: 22px;
                  height: 22px;
                  font-size: 12px;
                  border-radius: 50%;
                  color: rgba(255, 255, 255, 0);
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  border: 2px solid #B7C3D1;

                  &.completed {
                    background: linear-gradient(17deg, rgba(71, 172, 127, 1) 0%, rgba(118, 255, 214, 1) 88%);
                    color: rgba(255, 255, 255, 1);
                    border: none;
                  }
                }

                &.completed {
                  border: 5px solid $grey-8;

                  &::before {
                    content: " ";
                    display: block;
                    position: absolute;
                    width: 32px;
                    height: 32px;
                    border: 2px solid #57C89C;
                    border-radius: 50%;
                    left: -5px;
                    top: -5px;
                  }
                }
              }

              &.filtered-data {
                .bar {
                  height: 0;
                }
              }

              .list-item {
                padding-left: 31px;

                .card {
                  box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
                  border: 1px solid;
                  background-color: $white;
                  position: relative;
                  border-radius: 4px;
                  border-image-slice: 1;
                  background-origin: border-box;
                  background-clip: content-box, border-box;
                  border-image-source: linear-gradient(to left, $grey-6 71%, #C4CCF2 30%);
                  background-image: linear-gradient(to right, #F3F4FC -4%, #FFF 9%, #FFF 95%, #F3F4FC 100%), linear-gradient(to left, $grey-6 71%, #C4CCF2 30%);
                  border-left-width: 4px;
                  cursor: pointer;

                  .session-video {
                    position: relative;
                    width: 190px;

                    .video-link {
                      width: 100%;
                      height: 100%;
                      display: flex;
                    }

                    .video-thumbnail {
                      width: 100%;
                      height: 116px;
                    }

                    .play-button {
                      position: absolute;
                      margin: 0 auto;
                      left: 0;
                      right: 0;
                      text-align: center;
                      top: 0;
                      width: 100%;
                      height: 100%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      background-image: linear-gradient(171deg, rgba(255, 255, 255, 0.1) -13%, rgba(189, 200, 234, 0.8) 99%);
                    }
                  }

                  &.active {
                    border-image-source: linear-gradient(106deg, #886AFF 10%, #7BDFFF 31%, #7BC7FF 77%, #7D7BFF 100%);
                    background-image: linear-gradient(to right, #F3F4FC -4%, #FFF 9%, #FFF 95%, #F3F4FC 100%), linear-gradient(106deg, #886AFF 10%, #7BDFFF 31%, #7BC7FF 77%, #7D7BFF 100%);
                  }

                  .session-info-block {
                    padding-left: 24px;
                    display: -webkit-box;
                    -webkit-line-clamp: 10;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    .session-details {
                      padding-top: 18px;

                      .session-topic {
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        color: #545E64;
                        font-size: 16px;
                        line-height: 1.5;

                        p {
                          margin-bottom: 0;
                          font-weight: normal;
                        }
                      }

                      .session-title {
                        text-align: left;
                        color: #0573B4;
                        margin-bottom: 10px !important;
                        font-size: 18px;
                        font-weight: 600;
                        line-height: 1.33;
                      }
                    }

                    .actions-links-mobile {
                      line-height: 1;
                      margin-left: 20px;

                      .custom-dropdown {
                        line-height: 1.5;
                        padding: 0;
                        transform: translate3d(-131px, 0, 0) !important;
                        border: none;
                        box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.3);
                        width: 240px;
                        inset: auto !important;

                        .dropdown-item {
                          .bookmark-icon-link {
                            padding: 4px 6px 2px 6px;

                            .bookmark-text {
                              display: block;
                            }

                            .remove-bookmark-text {
                              display: none;
                            }

                            &.flagged {
                              .bookmark-text {
                                display: none;
                              }

                              .remove-bookmark-text {
                                display: block;
                              }
                            }

                            &.flagged i.far {
                              display: none;
                            }

                            &:not(.flagged) i.fas {
                              display: none;
                            }
                          }
                        }

                        .session-resources-icon-group {
                          background-color: transparent !important;
                          padding-top: 5px;
                          padding-bottom: 10px;
                          display: flex;
                          align-items: center;

                          &:first-child {
                            padding-top: 18px;
                          }

                          &:last-child {
                            padding-bottom: 18px;
                          }
                        }

                        .flags {
                          background-color: transparent !important;

                          .session-resources-icon-group {
                            padding: 5px 0 10px 0 !important;
                          }
                        }

                        .icon-container {
                          flex: 0 0 20px;
                          text-align: center;
                          line-height: 1;
                        }

                        .icon-text {
                          padding-left: 21px;
                          color: $grey-1;

                          &.disabled {
                            color: $grey-5;
                            cursor: not-allowed;
                          }
                        }
                      }
                    }
                  }

                  .action-btn-group {
                    margin-left: auto;
                    padding-right: 41px !important;
                    gap: 6px;
                    border-radius: 2px;

                    .action-btn-icon-group {
                      &:first-child {
                        padding: 4px 5px 3px 5px;
                        display: inline-block;
                      }

                      &.bookmark-icon-link {
                        padding: 4px 6px 2px 6px;

                        &.flagged i.far {
                          display: none;
                        }

                        &:not(.flagged) i.fas {
                          display: none;
                        }
                      }

                      &:nth-child(2) {
                        font-size: 18px;
                        padding-top: 1px;
                      }

                      .clapperboard-play-icon {
                        width: 20px;
                        height: 20px !important;
                        margin-bottom: 4px;
                      }

                      &:hover {
                        background-color: $blue-5;
                      }
                    }

                    a.action-btn-icon-group:first-child {
                      padding-top: 2px;
                    }
                  }

                  &:hover {
                    border-color: $blue-3;
                    border-image-source: none;

                    .session-video {
                      .play-button {
                        background-image: linear-gradient(171deg, rgba(255, 255, 255, 0.1) -13%, rgba(110, 128, 188, 0.8) 99%);
                      }
                    }
                  }
                }
              }

              .online-class-session-recording {
                .modal-dialog {
                  .modal-content {
                    .modal-header {
                      .bookmark-icon-link {
                        .bookmark-icon-group {
                          padding: 3px 6px 0 6px;

                          &:hover {
                            background-color: $blue-5;
                          }
                        }

                        &.flagged i.far {
                          display: none;
                        }

                        &:not(.flagged) i.fas {
                          display: none;
                        }
                      }

                      padding-left: 34px;
                    }
                  }
                }
              }
            }
          }
        }
      }

      @media (max-width: 1200px) {
        .body {
          .body-container {
            ul.recorded-video-list {
              .list-group {
                .list-item {
                  .card {
                    .session-info-block {
                      width: calc(100% - 280px);

                      .session-topic {
                        font-size: 15px;
                      }
                    }

                    .action-btn-group {
                      padding-right: 22px !important;
                    }
                  }
                }
              }
            }
          }
        }
      }

      @media (max-width: 991px) {
        .body {
          .body-container {
            ul.recorded-video-list {
              .list-group {
                .list-item {
                  .card {
                    .action-btn-group {
                      .action-btn-icon-group {
                        &:hover {
                          background-color: transparent;
                        }
                      }
                    }
                  }
                }

                .online-class-session-recording {
                  .modal-dialog {
                    .modal-content {
                      .modal-header {
                        .bookmark-icon-link {
                          .bookmark-icon-group {
                            &:hover {
                              background-color: transparent;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      @media (max-width: 767px) {
        .header {
          .header-container {
            padding-left: 0 !important;
          }

          .cohort-card {
            .instructor-info {
              .instructor-title-and-bio-group {
                flex-wrap: wrap;
              }

              .instructor-title {
                width: 100%;
              }

              .instructor-info-block {
                .instructor-bio-link {
                  white-space: nowrap;
                  border: none;
                  padding-left: 0;
                  border-left: none;
                }
              }
            }
          }
        }

        .body {
          .body-container {
            padding-bottom: 35px !important;

            ul.recorded-video-list {
              padding-left: 13px !important;

              .list-group {
                &:first-child {
                  .bar {
                    top: 155px;
                  }
                }

                &:last-child {
                  .bar {
                    height: 155px;
                  }
                }

                &:only-child {
                  .bar {
                    height: 0;
                  }
                }

                .indicator {
                  top: 150px;
                }

                .list-item {
                  .card {
                    max-width: 320px;
                    margin: 0 auto;

                    .session-video {
                      width: 100%;

                      .video-thumbnail {
                        height: calc(76vw * 0.5625);
                        max-height: 11rem;
                      }

                      .play-button {
                        .play-icon {
                          width: 54px;
                        }
                      }
                    }

                    .session-info-block {
                      width: calc(100% - 7px);
                      padding-bottom: 0;
                      overflow: visible;

                      .session-details {
                        padding-bottom: 10px;
                        width: 250px;

                        .session-topic {
                          font-size: 15px;
                          display: inline-block;
                        }
                      }
                    }

                    .action-btn-group {
                      display: none !important;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .modal.instructor-bio-modal {
    .modal-body {
      overflow: hidden;

      .instructor-rank {
        background-image: linear-gradient(97deg, #6865CB 0%, #6FA7DC 108%);
        padding: 0 10px 1px 11px;
        border-radius: 6px;
        color: $white;
        width: 175px;
        height: 27px;
        display: flex;
        align-items: center;
        margin: 10px 0 25px;
      }

      .top-dot-grid {
        position: absolute;
        right: 103px;
        width: auto;
        top: 8px;
      }

      .bottom-dot-grid {
        position: absolute;
        left: 34px;
        width: auto;
        bottom: 8px;
      }

      .close-icon {
        top: 20px;
        position: absolute;
        right: 15px;
        left: unset;
        text-align: right;

        .btn-close {
          font-size: 15px;
        }
      }

      .instructor-detail {
        padding: 29px 35px 35px 34px;
        font-size: 16px;
        position: relative;

        .instructor-name {
          font-size: 24px;
        }

        .instructor-score {
          padding: 2px 10px 1px;
          border-radius: 6px;
          background-image: linear-gradient(97deg, #6865CB 0%, #6FA7DC 108%);
          width: fit-content;
        }

        .instructor-description {
          max-height: 400px;
          overflow: auto;
          color: #545E64;
          line-height: 1.5;
          font-size: 16px;
        }
      }

      .instructor-image {
        @media (max-width: 767px) {
          display: none !important;
        }
      }
    }

    .modal-content {
      overflow: hidden !important;
    }

    .modal-dialog {
      @media (max-width: 991px) {
        max-width: 75%;
        margin-left: auto;
        margin-right: auto;
      }
    }
  }
}
