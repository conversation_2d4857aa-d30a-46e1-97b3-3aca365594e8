body[data-controller="problem_solving"] {
  &[data-action="end_intermediate_section"] {
    section#end-intermediate-section {
      nav.question-nav {
        span {
          font-size: 16px;
          line-height: 35px;
        }

        .pull-right a {
          line-height: 18px;
        }
      }

      .question {
        border: solid 1px $grey-6;
        overflow: hidden;
        background-color: white;

        .question-content {
          padding: 15px;

          .break-info {
            border: solid 1px $grey-6;
            border-radius: 4px;
            padding: 63px 48px 128px;
            min-height: 60vh;
            text-align: center;

            .title {
              font-size: 28px;
              margin: 36px 0 24px;
            }

            .description {
              font-size: 22px;
              max-width: 978px;
              text-align: left;
              margin: 0 auto;
            }
          }
        }
      }

      @media only screen and (max-width: 767px) {
        .question {
          .question-content {
            .break-info {
              padding: 63px 27px 65px 34px;

              .title {
                font-size: 26px;
              }

              .description {
                font-size: 19px;
              }
            }
          }
        }
      }
    }

    &.gre {
      section#end-intermediate-section {
        nav.question-nav {
          font-size: 18px;
          color: #2b343d;

          .pull-right .navigation-buttons {
            display: initial;
          }
        }

        .question-actions {
          .btn.gray {
            background-color: #b7c3d1;
          }
        }

        @media only screen and (max-width: 767px) {
          nav.question-nav {
            margin-top: 66px;
          }
        }
      }
    }
  }
}
