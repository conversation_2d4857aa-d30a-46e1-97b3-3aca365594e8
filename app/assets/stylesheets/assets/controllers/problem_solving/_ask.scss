body[data-controller="problem_solving"] {
  &[data-action="ask"] {
    .action-following {
      .question-actions {
        margin: 0 auto;
        position: fixed;
        bottom: 0;
        background-color: white;
      }

      .question-content {
        margin-bottom: 116px;
      }
    }

    section#problem-solving {
      .question {
        border: solid 1px $grey-6;
        overflow: hidden;
        background-color: white;

        .question-content {
          padding: 15px;

          form {
            border: solid 1px $grey-6;
            border-radius: 4px;
            padding: 20px;
          }

          .answers {
            .answer {
              width: 100%;
              margin: 8px 15px 8px 0;

              label {
                .styledRadio,
                .styledCheckbox {
                  border: 2px solid #CCC;
                }

                .styledRadio {
                  &.selected {
                    border-color: transparent;
                  }
                }

                .styledCheckbox {
                  &.selected {
                    border-color: $dark-1;
                  }
                }
              }
            }
          }
        }

        &.text_completion {
          .answers {
            display: block;

            .group-row {
              .answer-list {
                h3,
                button.answer p {
                  font-size: 22px !important;
                }
              }
            }
          }
        }

        &.graphics_interpretation {
          .answers-select {
            &.solved {
              &.correct {
                border: 2px solid $green-2;
                background-color: #F5FCF8;
              }

              &.incorrect {
                border: 2px solid $red-2;
                background-color: $red-4;
              }
            }
          }

          .info-box .solution > span {
            border-radius: 4px;
          }
        }

        &.two_part_analysis {
          .table-responsive {
            max-width: 800px;
          }

          .info-box .solution > span {
            border-radius: 4px;
          }
        }

        &.multi_source_reasoning {
          &[data-reasoning-question-type="multiple_answer"] {
            .info-box .solution > span {
              border-radius: 4px;
            }
          }
        }

        &.table_analysis {
          .info-box .solution > span {
            border-radius: 4px;
          }
        }

        &.font-switch-wrapper {
          &.large-font {
            p,
            span:not(.math-tex-or-mml, .math-tex-or-mml span, .sort-by) {
              font-size: 22px;
            }

            .answer {
              label {
                margin-top: 11px;
              }
            }

            table {
              thead {
                th {
                  font-size: 22px;
                }
              }

              tbody {
                tr {
                  td {
                    font-size: 22px;

                    p,
                    strong {
                      font-size: 22px;
                    }
                  }
                }
              }
            }
          }

          &.small-font {
            .answer {
              label {
                margin-top: 7px !important;
              }
            }
          }
        }

        @media only screen and (min-width: 320px) and (max-width: 1399px) {
          margin-top: -1px;
        }
      }

      &.gmat,
      &.ea {
        .question {
          .question-content {
            .problem {
              min-height: calc(100vh - 325px);
            }

            .interrogation_part {
              padding-left: 15px;
              padding-top: 5px;
            }

            .answers {
              padding-top: 15px;

              label {
                margin-top: 5px;

                span {
                  &.option {
                    font-size: 21px;

                    p {
                      font-size: 21px;
                    }
                  }
                }
              }
            }
          }
        }

        .font-switch-buttons {
          .small-font,
          .large-font {
            border-color: white;

            &.active {
              background-color: white;
              color: $primary-1;
            }
          }
        }

        &.reading_comprehension {
          .question {
            .question-content {
              padding: 0;

              form {
                border: none;
              }
            }
          }
        }

        &.multi_source_reasoning,
        &.table_analysis {
          .question {
            .question-content {
              padding: 0;

              form {
                border: none;

                .problem.exercise {
                  .multi-source-reasoning.section-wrapper,
                  .table-analysis.section-wrapper {
                    .answer {
                      label {
                        margin-top: 6px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      &.gmat {
        .question {
          &.font-switch-wrapper {
            &.large-font,
            &.small-font {
              span:not(.math-tex-or-mml, .math-tex-or-mml span, .sort-by) {
                &.review-span-btn {
                  font-size: 13px !important;
                  font-weight: 600 !important;
                  white-space: nowrap;
                  line-height: 0;
                  margin: 0;
                  padding: 0;
                }
              }
            }
          }
        }
      }

      &.gre {
        .question {
          .question-content {
            form {
              line-height: 60vh;

              .exercise {
                display: inline-block;
                width: 100%;
                line-height: initial;
                vertical-align: middle;
                text-align: center;

                .answers {
                  visibility: hidden;
                }

                &.mathjax-loaded {
                  .answers {
                    display: inline-block;
                    visibility: visible;

                    .answer {
                      label {
                        margin-top: 7px;
                      }

                      .option {
                        text-align: left;
                      }
                    }
                  }
                }

                &.sentence_equivalence,
                &.text_completion {
                  display: block;
                  margin-bottom: 50px;
                }
              }

              .notes,
              .note-form {
                text-align: left;
                line-height: 1.5em;
              }
            }
          }
        }

        .font-switch-buttons {
          .small-font,
          .large-font {
            border-color: #162C40;
            color: #162C40;

            &.active {
              background-color: #162C40;
              color: $white;
            }
          }
        }

        .question {
          .question-content {
            form {
              .problem.exercise {
                .section-wrapper {
                  .answer {
                    label {
                      margin-top: 3px;
                    }
                  }
                }
              }
            }
          }
        }

        &.multi_source_reasoning,
        &.table_analysis {
          .question {
            .question-content {
              form {
                .problem.exercise {
                  .multi-source-reasoning.section-wrapper,
                  .table-analysis.section-wrapper {
                    .answer {
                      label {
                        margin-top: 3px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      /* inherits styling from /components/exercises/reading_comprehension.scss */
      &.reading_comprehension {
        .container {
          max-width: none;
        }

        p {
          text-align: left !important;
        }

        .question {
          .question-content {
            form {
              padding: 0;

              .problem.exercise {
                height: 100%;
                vertical-align: top;
                text-align: left;

                &[data-rc-question-type="select_the_sentence"] {
                  .sentence {
                    &:hover {
                      cursor: pointer;
                    }

                    &.correct,
                    &.incorrect {
                      background-color: transparent;
                      color: $grey-2;
                    }
                  }
                }

                .section-navigation {
                  display: none;
                }
              }
            }
          }
        }
      }

      &.multi_source_reasoning,
      &.table_analysis {
        .container {
          max-width: none;
        }

        p {
          text-align: left !important;
        }

        .question {
          .question-content {
            form {
              padding: 0;

              .problem.exercise {
                height: 100%;
                vertical-align: top;
                text-align: left;
              }
            }
          }
        }
      }

      /* RC styling for chapter test */
    }

    @media screen and (min-width: 768px) {
      section#problem-solving {
        &.reading_comprehension {
          .left-section {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;

            .content-box {
              height: calc(100vh - 305px);
            }
          }

          .right-section {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;

            .content-box {
              height: calc(100vh - 280px);
            }
          }
        }

        &.multi_source_reasoning,
        &.table_analysis {
          .left-section {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;

            .content-box {
              height: calc(100vh - 305px);
            }
          }

          .right-section {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;

            .content-box {
              height: calc(100vh - 280px);
            }
          }
        }
      }
    }

    @media screen and (min-width: 1400px) {
      section#problem-solving {
        &.reading_comprehension {
          .left-section {
            .content-box {
              height: calc(100vh - 273px);
            }
          }

          .right-section {
            .content-box {
              height: calc(100vh - 258px);
            }
          }
        }

        &.multi_source_reasoning,
        &.table_analysis {
          .left-section {
            .content-box {
              height: calc(100vh - 273px);
            }
          }

          .right-section {
            .content-box {
              height: calc(100vh - 258px);
            }
          }
        }
      }
    }

    @media only screen and (min-width: 768px) and (max-width: 1399px) {
      section#problem-solving {
        &.sat {
          &.reading_comprehension {
            .reading-comprehension.section-wrapper {
              margin-top: 30px;
            }
          }
        }
      }
    }

    @media only screen and (min-width: 992px) {
      section#problem-solving {
        &.gre {
          .question {
            .question-content {
              form {
                .exercise {
                  &.mathjax-loaded {
                    .answers {
                      .answer {
                        label {
                          margin-top: 7px;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    @media screen and (max-width: 991px) {
      section#problem-solving {
        &.sat {
          .reading_comprehension {
            .reading-comprehension.section-wrapper {
              .left-section {
                .content-box {
                  padding-right: 10px;
                  padding-left: 10px;
                }
              }

              .right-section {
                .content-box {
                  .reading-comprehension-exercise {
                    padding-right: 10px;
                    padding-left: 10px;
                  }
                }
              }
            }
          }
        }
      }
    }

    @media only screen and (max-width: 767px) {
      .action-following {
        .question-actions {
          padding: 40px 0 0;
          position: relative;
        }

        .question-content {
          margin-bottom: 40px;
        }
      }

      section#problem-solving {
        .question {
          &.text_completion {
            .answers {
              .group-row {
                .answer-list {
                  h3,
                  button.answer p {
                    font-size: 18px !important;
                  }
                }
              }
            }
          }
        }
      }

      &.sat {
        section#problem-solving {
          .question {
            margin-top: 10px;
          }

          &.sat {
            .reading_comprehension {
              .reading-comprehension.section-wrapper {
                margin-top: 9px;
              }
            }
          }
        }
      }
    }
  }
}
