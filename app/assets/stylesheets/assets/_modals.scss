#select-time-per-question,
#select-time-per-question-diagnostic {
  .modal-dialog {
    .modal-body {
      width: 200px;
      padding: 0 0 0 0 !important;
      margin: 0 auto;

      h5 {
        margin-top: 25px;
      }

      select {
        margin-top: 13px;
        color: $grey-3;
      }
    }

    .modal-footer {
      padding: 30px 0 35px 0;
      width: 200px;
      margin: 0 auto;

      a {
        width: 200px;
        margin: 0;
      }
    }

    &.subsection-warning {
      .default {
        display: none;
      }

      .hard-chapter-test {
        display: inherit !important;
      }

      .modal-body {
        width: 100%;

        h3 {
          margin: 0 24px;
        }

        .description {
          border-radius: 2px;
          margin: 20px 32px 24px;
          padding: 20px 24px;

          @media only screen and (max-width: 767px) {
            padding: 24px;
            margin: 24px;
          }

          @media only screen and (min-width: 768px) {
            font-size: 18px;
          }
        }

        .select-time-row {
          padding: 0 20px;
          margin: 0 32px;
          align-items: baseline;

          h5 {
            margin-top: 0;
          }

          select {
            margin-top: 0;
          }

          @media only screen and (max-width: 767px) {
            margin: 0;
          }
        }
      }

      .modal-footer {
        a {
          width: 200px;
        }
      }
    }
  }
}

#select-time-per-question-diagnostic {
  .modal-dialog {
    .modal-body {
      width: 240px;

      h5 {
        margin-bottom: 25px;
      }
    }
  }

  @media only screen and (min-width: 768px) {
    .modal-dialog {
      max-width: 430px;
    }
  }
}

#video-player-modal,
.online-class-session-recording {
  .modal-dialog {
    max-width: 960px;

    .modal-body {
      padding: 32px;

      .video-wrapper {
        margin: 0;
      }
    }
  }
}

#ondemand-office-hour-modal {
  .modal-dialog {
    max-width: 960px;

    .modal-header {
      padding: 16px 10px 16px 16px;
      background-image: linear-gradient(69deg, #D9BA88 -47%, rgba(217, 186, 136, 0) 21%);

      .dots-bg {
        width: 277px;
        height: 161px;
        position: absolute;
        right: 0;
        background-image: linear-gradient(291deg, #D9BA88 -147%, rgba(217, 186, 136, 0) 74%);
      }

      .dots-3x4 {
        background-image: asset-url("home/dots_3x4_grey.svg");
        position: absolute;
        right: 0;
        width: 29px;
        height: 42px;
        top: 15px;
        background-size: cover;
        z-index: inherit;
      }

      .ondemand-office-hour-modal-header {
        align-items: center;

        h1 {
          margin-bottom: 0;
          margin-left: 10px;
          font-size: 18px;
          font-weight: 600;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: 0.18px;
          color: #94825A;
        }
      }

      .schedule {
        border-radius: 4px;
        background-color: #F6E7D1;
        margin-left: auto;
        margin-right: 50px;

        .date {
          font-size: 16px;
          font-weight: 600;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: normal;
          color: $grey-2;
          border-right: solid 1px $white;
          align-items: center;
          padding: 5px 16px 5px 16px;
          gap: 10px;

          i {
            color: #94825A;
          }
        }

        .time {
          font-size: 14px;
          font-weight: 400;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: normal;
          color: $grey-2;
          align-items: center;
          padding: 5px 16px 5px 16px;
          gap: 10px;

          i {
            color: #94825A;
          }
        }
      }

      .btn-close {
        margin-left: inherit;
        z-index: 1;
      }
    }

    .modal-body {
      padding: 25px 31px 25px 31px;
      background-image: asset-url("home/office_hour_content_bg.png");
      background-position: center top;
      background-size: contain;
      background-repeat: no-repeat;

      .ondemand-office-hour-container {
        .content {
          p {
            font-size: 16px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: normal;
            text-align: left;
            color: $grey-2;

            a {
              color: $blue-3;
              text-decoration: underline;
            }
          }

          .zoom-link {
            h5 {
              font-size: 14px;
              font-weight: 600;
              font-stretch: normal;
              font-style: normal;
              line-height: 1.57;
              letter-spacing: normal;
              color: #313131;
              border-bottom: solid 1px $grey-6;
              margin-bottom: 18px;
              padding-bottom: 6px;
            }

            .zoom-link-body {
              background-color: #F0F3FE;
              border-radius: 4px;
              overflow: hidden;
              margin-bottom: 40px;

              &:has(.empty-link) {
                .copy-button {
                  &:hover {
                    background-color: #FCE5BF;
                    cursor: default;
                  }
                }
              }

              .copy-button {
                background-color: #FCE5BF;
                font-size: 14px;
                font-weight: 600;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.33;
                letter-spacing: normal;
                text-align: center;
                color: #3C2B10;
                padding: 10px 16px 8px 16px;
                height: 48px;
                display: flex;
                align-items: center;
                cursor: pointer;
                transition: all 0.4s ease;

                i {
                  margin-right: 5px;
                  transition: all 0.4s ease;
                }

                &:hover {
                  background-color: #E6C690;
                }
              }

              .empty-link {
                color: #858CA7;
              }

              a {
                padding: 8px 16px;
                font-size: 16px;
                font-weight: 600;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.5;
                letter-spacing: normal;
                text-align: left;
                color: $blue-3;
              }
            }
          }
        }

        .instructors {
          h5 {
            font-size: 14px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.57;
            letter-spacing: normal;
            color: #313131;
            border-bottom: solid 1px $grey-6;
            margin-bottom: 17px;
            padding-bottom: 6px;
            text-transform: uppercase;
          }

          .section-tab-box {
            display: flex;
            align-items: center;
            overflow: auto;

            .scroll-full-box {
              gap: 16px;
              display: flex;
              align-items: center;
              flex-direction: row;
              white-space: nowrap;
              flex-wrap: nowrap;
            }

            scrollbar-width: none;
            scrollbar-color: transparent;

            &::-webkit-scrollbar {
              width: 0;
              height: 0;
            }

            &::-webkit-scrollbar-track {
              background-color: transparent;
            }

            &::-webkit-scrollbar-thumb {
              background-color: transparent;
            }

            .section-tab-group {
              align-items: center;
              padding: 8px 8px 0 8px;

              .section-tab {
                .instructor {
                  font-size: 16px;
                  font-weight: 600;
                  font-stretch: normal;
                  font-style: normal;
                  line-height: 1.5;
                  letter-spacing: 0.16px;
                  text-align: left;
                  color: $grey-4;
                  padding-bottom: 6px;
                  display: flex;
                  align-items: center;

                  .first-name {
                    display: none;
                  }

                  .full-name {
                    display: inline;
                  }

                  img {
                    min-width: 34px;
                    height: 34px !important;
                    border: solid 0.8px #FFF6E6;
                    background-color: $white;
                    margin-right: 6px;
                    object-fit: cover;
                  }
                }

                .border-line {
                  width: 100%;
                  height: 4px;
                  background-image: linear-gradient(to right, transparent, transparent);
                }
              }

              &:hover {
                background-color: #FBF5EB;
              }

              &.active-tab {
                &:hover {
                  background-color: transparent;
                }

                .section-tab {
                  .instructor {
                    color: #7F693C;

                    img {
                      box-shadow: 0 1.9px 3.7px 0 rgba(187, 153, 97, 0.6);
                      border-color: #FDE6C0;
                      background-image: linear-gradient(to bottom, #DAE4EB 100%, $white 0%);
                    }
                  }

                  .border-line {
                    background-image: linear-gradient(to right, #DDC192, #A78958);
                  }
                }
              }
            }
          }

          .instructor-details {
            border: solid 1px #E3E7F5;
            background-image: linear-gradient(to bottom, #F1F3FA -2%, $white 57%);
            padding: 20px 30px;

            h6 {
              font-size: 16px;
              font-weight: 600;
              font-stretch: normal;
              font-style: normal;
              line-height: 1.5;
              letter-spacing: normal;
              color: #94825A;
            }

            p {
              font-size: 16px;
              font-weight: normal;
              font-stretch: normal;
              font-style: normal;
              line-height: 1.5;
              letter-spacing: normal;
              text-align: left;
              color: $grey-1;
            }

            .scroll-bar {
              max-height: 125px;
              overflow: auto;
              padding-right: 15px;

              &::-webkit-scrollbar {
                width: 7px;
              }

              &::-webkit-scrollbar-track {
                border-radius: 12px;
              }

              &::-webkit-scrollbar-thumb {
                background-color: #B7C3D1;
                border-radius: 25px;
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 992px) {
    .modal-dialog {
      padding: 0 15px;

      .modal-header {
        .schedule {
          .date {
            padding: 5px 8px;
          }

          .time {
            padding: 5px 8px;
          }
        }
      }

      .modal-body {
        .ondemand-office-hour-container {
          .content {
            .zoom-link {
              .zoom-link-body {
                .copy-button {
                  white-space: nowrap;
                }

                a {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }

          .instructors {
            .section-tab-box {
              .section-tab-group {
                .section-tab {
                  .instructor {
                    .first-name {
                      display: inline !important;
                    }

                    .full-name {
                      display: none !important;
                    }
                  }
                }

                &.active-tab {
                  .section-tab {
                    .instructor {
                      .first-name {
                        display: none !important;
                      }

                      .full-name {
                        display: inline-block !important;
                      }
                    }
                  }
                }
              }
            }

            .instructor-details {
              .scroll-bar {
                max-height: 200px;
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 767px) {
    .modal-dialog {
      .modal-header {
        flex-wrap: wrap;

        .ondemand-office-hour-modal-header {
          width: 60%;
        }

        .schedule {
          margin-top: 15px;
          margin-right: auto;

          .date {
            padding: 5px 8px;
          }

          .time {
            padding: 5px 8px;
          }
        }

        .btn-close {
          position: absolute;
          top: 10px;
          right: 10px;
        }
      }

      .modal-body {
        .ondemand-office-hour-container {
          .content {
            .zoom-link {
              .zoom-link-body {
                .copy-button {
                  white-space: nowrap;
                }

                a {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }

          .instructors {
            .section-tab-box {
              .scroll-full-box {
                gap: 8px;
              }

              .section-tab-group {
                padding: 8px 6px 0 6px;

                .section-tab {
                  .instructor {
                    span {
                      display: none;
                    }

                    .first-name {
                      display: none !important;
                    }
                  }
                }

                &.active-tab {
                  .section-tab {
                    .instructor {
                      span {
                        display: inline-block;
                      }
                    }
                  }
                }
              }
            }

            .instructor-details {
              .scroll-bar {
                max-height: 200px;
              }
            }
          }
        }
      }
    }
  }
}

#marketing-modal {
  .modal-dialog {
    max-width: 695px;

    .modal-content {
      .modal-header {
        height: 167px;
        background-image: image-url("layout/controllers/users/home/<USER>/background.png");
        background-position: left;
        background-repeat: no-repeat;
        background-size: cover;
        padding-left: 44px;

        @media only screen and (max-width: 767px) {
          padding-left: 23px;
        }

        .modal-title {
          font-size: 36px;
          line-height: 47.9px;
          color: $white;

          .highlighted {
            color: $primary-3;
          }
        }

        .modal-subtitle {
          font-size: 22.9px;
          line-height: 30.4px;
          color: $white;
        }

        .btn-close {
          filter: brightness(0) invert(1);
          opacity: 1;
          position: absolute;
          right: 16px;
          top: 16px;
          font-size: 12px;
        }
      }

      .modal-body {
        padding: 25px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .content {
          background-color: $grey-9;
          padding: 20px 15px;

          p {
            font-size: 16px;
            color: $grey-1;
            line-height: 27px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .sale-price {
            font-size: 18px;
            color: #2A89C0;
            line-height: 27px;
            font-weight: 600;
          }
        }

        .btn {
          width: 250px;
          margin: 0 auto;
          margin-top: 20px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  &.image-only {
    .modal-dialog {
      max-width: 1216px;
      padding: 0 20px;
    }

    .modal-content {
      background-color: transparent;

      .modal-header {
        background-image: none;
      }

      .modal-body {
        padding: 0;
        border-radius: 4px;

        .btn-close {
          position: absolute;
          right: 15px;
          top: 15px;
          opacity: 1;
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23FFF'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e");
        }

        img {
          border-radius: 4px;
        }

        .desktop-image {
          img {
            width: 100%;
          }
        }

        .mobile-image {
          display: none;
        }

        &.disable-call-action {
          .btn-close {
            padding-top: 0 !important;
          }

          a {
            cursor: default;
            width: 100%;
          }
        }
      }
    }

    @media (max-width: 767px) {
      .modal-dialog {
        max-width: fit-content;
        margin: 0.5rem auto;
        align-items: flex-start;
      }

      .modal-body {
        .desktop-image {
          display: none;
        }

        .mobile-image {
          display: block !important;

          img {
            width: 100%;
            height: auto;
          }
        }
      }
    }
  }
}

.online-class-session-files-modal {
  .modal-dialog {
    max-width: 644px;

    .modal-content {
      .modal-header {
        background: asset-url("layout/controllers/online_classes/session_header_bg.webp");
        background-size: cover;
        background-position: right;
        padding: 17px 38px;
        background-repeat: no-repeat;

        .header-info {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          line-height: 1.5;

          .title {
            color: $blue-5;
          }

          .last-updated {
            color: $blue-5;
          }
        }

        .btn-close {
          color: #FDFEFF !important;
          position: absolute;
          top: 8px;
          right: 8px;
          filter: invert(1);
        }
      }

      .modal-body {
        padding: 13px 38px 0 38px;
        background-color: #F4F6F8;

        .uploaded-files {
          .section-title {
            margin: 0 0 17px 0;
            line-height: 1.33;
            letter-spacing: 0.14px;
            text-align: left;
            color: $grey-4;
          }

          .file-list-item {
            padding: 20px 17px;
            border-radius: 4px;
            box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
            background-color: $white;
            margin-bottom: 23px;

            .file-item {
              padding: 17px 0;
              position: relative;

              &::after {
                width: 100%;
                height: 1px;
                display: block;
                content: "";
                position: absolute;
                bottom: 0;
                background: linear-gradient(to right, #E7E9EF, #CDD2DF);
              }

              &:last-child {
                &::after {
                  display: none;
                }
              }

              .file-icon {
                padding: 7px 9.7px 7.7px 9px;
                background-color: #EDF6FB;
                width: 35px;
                height: 35px;
                text-align: center;
                border-radius: 100%;
                color: $blue-2;
              }
            }

            .file-name {
              color: $grey-1;
            }

            .upload-date {
              color: $grey-4;
            }

            .schedule-download-btn {
              color: $blue-3;
              padding: 2px 5px 4px 5px;
              border-radius: 2px;

              &:hover {
                background-color: $blue-5;
              }
            }
          }
        }
      }
    }

    @media (max-width: 767px) {
      .modal-dialog {
        max-width: 90%;
        margin: 0 auto;
      }

      .modal-content {
        .modal-header {
          padding: 20px 20px;
        }

        .modal-body {
          padding: 13px 19px 0 19px;

          .uploaded-files {
            .file-list-item {
              .file-item {
                .file-icon-col,
                .file-actions-col {
                  width: 41px;

                  .file-icon {
                    width: 31px;
                    height: 31px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  }
                }

                .file-details-col {
                  width: calc(100% - 41px - 41px);
                }

                .file-actions {
                  .schedule-download-btn {
                    min-width: 19px;

                    .download-icon {
                      max-width: 19px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    @media (max-width: 475px) {
      .modal-content {
        .modal-header {
          background: asset-url("layout/controllers/online_classes/session_header_bg_mobile.webp");
          background-size: cover;
          background-position: bottom;
          background-repeat: no-repeat;
        }
      }
    }
  }
}
