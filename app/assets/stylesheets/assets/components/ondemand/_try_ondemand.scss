#try-ondemand {
  .modal-dialog {
    max-width: 750px;
    margin: 0 auto;

    .modal-body {
      padding: 15px;
      width: 90%;
      margin: 0 auto;

      .text-gold {
        color: #EED5A8;
      }

      .trial-separator {
        width: 30px;
        height: 4px;
        background-color: $white;
        content: "";
        display: block;
        margin: 0 auto;
        margin-top: 15px;
        margin-bottom: 40px;
        border-radius: 1.8px;
      }
    }

    .modal-content {
      background-image: url(asset-path("content-pages/ondemand/try_ondemand_desktop.webp"));
      background-size: cover;
      background-repeat: no-repeat;
      background-position: top left;
      background-color: transparent;
      border-radius: 9px !important;
      padding: 55px 15px;
    }

    .modal-header {
      text-align: center;
      justify-content: center;
      border-bottom: none;
      padding: 15px;

      .modal-title {
        max-width: 60%;

        h2 {
          font-size: 33.4px;
          font-weight: 600;
          line-height: 1.5;

          .header-ondemand {
            color: #27154E;
            padding: 1.5px 10px;
            border-radius: 5.6px;
            box-shadow: 0 1.7px 17px 0 rgba(235, 229, 208, 50%);
            border: 1px solid #FFEED3;
            background-image: linear-gradient(92deg, #C4A268 -55%, #F2DBAF 21%, #FFEED0 44%, #C2A065 103%), linear-gradient(94deg, #FEEED0 36%, #A98B53 68%);
          }
        }
      }
    }

    .close-button {
      position: absolute;
      top: 15px;
      right: 15px;
      cursor: pointer;

      .fa {
        font-weight: 300;
        color: $grey-1;
        font-size: x-large;
      }
    }

    .th {
      text-align: center;
      padding: 40px;
      margin: 0;
    }

    .td {
      text-align: center;
    }

    .custom-modal-dialog {
      width: 100%;
      max-width: 1138px;
      max-height: fit-content;
    }

    .table-container {
      border-radius: 9px;
      border-style: solid;
      border-width: 1px;
      border-color: #96816D;
      background-image: linear-gradient(160deg, #0D0F2B 23%, rgba(28, 30, 64, 0) 96%);
      background-origin: border-box;
      background-clip: content-box, border-box;

      .table-inner-box {
        padding: 0 31px 0 29px;

        table {
          margin-bottom: 17px;

          thead {
            tr {
              th {
                font-size: 16px;
                font-weight: bold;
                padding-left: 29px;
                padding-top: 22px;

                &:first-child {
                  padding-left: 0;
                  vertical-align: middle;
                }

                &:nth-child(2) {
                  width: 129px;
                  padding-left: 18px;
                  -webkit-background-clip: text;
                  background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-image: linear-gradient(to bottom, $white 33%, #D6D6D6 70%);
                }

                &:nth-child(3) {
                  width: 170px;
                  position: relative;
                  color: #FEE9CB;

                  &::before {
                    border-top: 10px #FEE9CB solid;
                    content: "";
                    position: absolute;
                    left: 0;
                    right: 0;
                    margin: 0 auto;
                    width: 23.4px;
                    top: 0;
                    border-left: 12px solid transparent;
                    border-right: 12px solid transparent;
                  }
                }

                &:nth-child(4) {
                  width: 165px;
                  -webkit-background-clip: text;
                  background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-image: linear-gradient(to bottom, #FDE6C0 37%, #BD9B62 70%);
                }

                &:nth-child(2),
                &:nth-child(3),
                &:nth-child(4) {
                  text-align: center;
                  vertical-align: middle;
                }
              }
            }
          }

          tbody {
            tr {
              td {
                padding-top: 12px;
                padding-bottom: 12px;
                padding-left: 17px;

                .star-features {
                  i {
                    color: #E1C48D;
                    font-size: 12px;
                    text-shadow: 0 -2px 10px #D9A920E0;
                  }
                }

                &:first-child {
                  padding-left: 0;
                  vertical-align: middle;
                }

                &:nth-child(2) {
                  padding-left: 18px;
                }

                &:nth-child(3) {
                  i {
                    color: #FEE9CB;
                  }
                }

                &:nth-child(4) {
                  -webkit-background-clip: text;
                  background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-image: linear-gradient(to bottom, #FDE6C0 37%, #BD9B62 70%);
                }

                &:nth-child(2),
                &:nth-child(3),
                &:nth-child(4) {
                  text-align: center;
                  vertical-align: middle;
                }

                ul {
                  margin-bottom: 0;
                  padding-left: 20px;
                }

                .trial-separator-new {
                  width: 15px;
                  height: 1px;
                  background-color: $white;
                  display: inline-block;
                }
              }
            }
          }
        }
      }
    }

    .modal-footer {
      justify-content: center;
      padding-top: 5px;
      padding-bottom: 15px;

      a {
        display: flex;
      }

      .cancel-btn-foot {
        padding: 12.4px 20.7px;
        border-radius: 1.4px;
        font-size: 16.6px;
        font-weight: 600;
        width: 308px;
        text-align: center;
        border-color: #F2DBAF;
        margin-right: 15px;
        color: #EED5A8;
        text-transform: none;
        background-color: transparent;
        box-shadow: 0 1.7px 17px 0 rgba(235, 229, 208, 0.5);

        &:hover {
          background-image: linear-gradient(to bottom, #3A3C4A, #3A3C4A), linear-gradient(97deg, #FEEED0 36%, #A98B53 68%);
          color: #EED5A8;
        }
      }

      .try-button {
        background: linear-gradient(92deg, #C4A268 -55%, #F2DBAF 21%, #FFEED0 44%, #C2A065 103%);
        padding: 12.4px 20.7px;
        border-radius: 1.4px;
        box-shadow: 0 1.7px 17px 0 rgba(235, 229, 208, 50%);
        font-size: 16.6px;
        color: $grey-1;
        font-weight: 600;
        width: 308px;
        text-align: center;

        &:hover {
          background-image: linear-gradient(to bottom, #E1BE84, #E1BE84), linear-gradient(97deg, #FEEED0 36%, #A98B53 68%);
          color: $grey-1;
        }

        .footer-ondemand-box {
          padding: 0 10px;
          border-radius: 3px;
          border: 1px solid #28164F;
          background-image: linear-gradient(101deg, #27154E -4%, #4B2C8D 12%, #27154E 32%, #462984 64%, #37206B 98%), linear-gradient(139deg, #28164F 36%, #7F5BCF 60%);
          display: inline;

          .footer-ondemand {
            background: #FEEED0;
            background: linear-gradient(to bottom, #FEEED0 0%, #A98B53 100%);
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }

      .learn-more {
        color: #EED5A8;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .text-style {
      margin-left: 35px;
    }

    @media (max-width: 991px) {
      .modal-content {
        background-image: url(asset_path("content-pages/ondemand/try_ondemand_tablet.webp"));
        max-width: 90%;
        margin: 0 auto;

        .modal-header {
          padding: 40px 20px 20px !important;

          .modal-title {
            min-width: 100%;
          }
        }

        .modal-body {
          padding: 0 20px 20px;
          width: 100%;

          .table-container {
            .table-inner-box {
              padding: 0 25px 25px;

              table {
                thead {
                  tr {
                    th {
                      padding-left: 10px !important;

                      &:nth-child(2n) {
                        width: 240px;
                        padding-left: 10px !important;
                      }
                    }
                  }
                }

                tbody {
                  tr {
                    td {
                      &:first-child {
                        width: 450px !important;
                      }

                      padding-left: 15px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    @media (min-width: 768px) {
      .modal-dialog {
        margin: 0;

        .close-button {
          filter: invert(1);

          .fa {
            filter: invert(1);
          }
        }

        .modal-content {
          background-image: url(asset_path("content-pages/ondemand/try_ondemand_mobile.webp"));
          max-width: 90%;
          margin: 0 auto;

          .modal-header {
            padding: 56px 10px 15px !important;

            .modal-title {
              h2 {
                font-size: 28px;

                br {
                  display: none;
                }
              }
            }
          }

          .modal-body {
            padding: 0 10px 10px;

            .trial-separator {
              display: none;
            }

            .table-container {
              .table-inner-box {
                padding: 0 15px 15px 10px;

                table {
                  thead {
                    tr {
                      th {
                        padding-left: 0;
                        padding-right: 10px;
                        font-size: 12px;

                        &:last-child {
                          display: none;
                        }
                      }
                    }
                  }

                  tbody {
                    tr {
                      td {
                        padding-left: 0;
                        padding-right: 20px;

                        &:first-child {
                          min-width: 148px !important;
                        }

                        &:last-child {
                          display: none;
                        }

                        ul {
                          padding-left: 10px;
                          list-style: none;
                        }
                      }
                    }
                  }
                }
              }
            }
          }

          .modal-footer {
            .try-button {
              width: 265px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 767px) {
  #try-ondemand {
    .modal-dialog {
      max-width: auto;
      width: 85%;

      .modal-content {
        width: 100%;
        max-width: 100%;

        .modal-body {
          p {
            min-width: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: 575px) {
  #try-ondemand {
    .modal-dialog {
      .modal-content {
        .modal-footer {
          .cancel-btn-foot,
          .show-overlay-ondemand {
            min-width: 300px;
            margin: 10px 0 !important;
          }
        }
      }
    }
  }
}
