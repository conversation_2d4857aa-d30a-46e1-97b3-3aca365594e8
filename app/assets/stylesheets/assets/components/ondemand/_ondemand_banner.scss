section {
  .text-gold {
    color: #94825A;
  }

  &.course-banner {
    background: #FDFDFD;
    background-position: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    height: auto;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-bottom: 68px;
    padding-top: 67px;
    position: relative;

    .course-banner-video-text {
      position: relative;
      z-index: 1;
    }

    .course-banner-video {
      object-fit: cover;
      width: 55%;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 0;
    }

    .course-banner-title {
      color: #27154E;
      margin-bottom: 40px;
      width: 710px;
      align-items: center;

      .logo-img {
        width: 240px;
        height: 55px !important;
      }

      .gmat-focus-tag {
        font-size: 17px;
        padding: 1px 9px;
        position: relative;
        top: -5px;
      }
    }

    .course-banner-detail {
      color: #27154E;
      width: 620px;
      line-height: 1.6;

      .text-gold {
        color: #94825A;
      }
    }

    .try-free-btn {
      border-radius: 2px;
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
      background-image: linear-gradient(102deg, #BE9C63 5%, #FDE6C0 84%);
      padding: 10px 15px;
      border: 0 !important;
      height: 44px;
      color: #2F1B04;
      width: 265px;

      &:hover {
        background-color: #BE9C63;
        background-image: unset;
        cursor: pointer;
      }
    }

    .purchase-btn {
      border-radius: 2px;
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
      background-image: linear-gradient(102deg, #BE9C63 5%, #FDE6C0 84%);
      padding: 10px 15px;
      border: 0 !important;
      width: 250px;
      height: 44px;
      color: #2F1B04;

      &:hover {
        background-color: #BE9C63;
        background-image: unset;
        cursor: pointer;
      }
    }

    .upgrade-btn {
      padding: 13px 15px;
      border-radius: 2px;
      border: solid 1px #27154E;
      width: 300px;
      height: 44px;
      background-color: transparent;
      color: #27154E;
      line-height: 1;

      &:hover {
        background-color: #D7DCEB;
        cursor: pointer;
      }

      &.free-account-user {
        margin-left: 0 !important;
      }
    }

    .prices {
      @media (max-width: 575px) {
        width: 100%;
      }

      .price-wrapper {
        .price {
          color: $grey-5;

          &::before {
            content: "$";
          }
        }

        .sale-price {
          color: $dark-1;

          &::before {
            content: "$";
          }
        }
      }

      .monthly-wrapper {
        border-top: 1px solid $grey-6;
      }
    }

    .one-month-price {
      color: $grey-1;
    }

    .yearly-price {
      color: #BE870F;
    }

    .access-price {
      color: $grey-4;
    }

    .monthly-price {
      @media (max-width: 575px) {
        width: 100%;
      }
    }
  }

  &.founder-detail {
    padding: 90px 0;

    .founder-detail-title {
      width: 85%;
      margin: 0 auto;
      text-align: center;
      line-height: 1.5;
    }

    .founder-wrapper {
      padding-top: 90px;

      .founder-wrapper-card {
        max-width: 19.5%;
        padding: 0 15px;
        width: 100%;
        color: $grey-1;
      }

      .icon {
        width: 43px;
        height: 43px;
        background-image: linear-gradient(to bottom, #FFE9C6, #E4C085, #B08F57);
        border-radius: 100%;
        text-align: center;
        font-size: 24px;
        color: $grey-2;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .icon-outer {
        padding: 5px;
        border-style: solid;
        border-width: 1px;
        border-color: #D7BD93;
        border-image-slice: 1;
        width: 55px;
        height: 55px;
        border-radius: 100%;
        margin: 0 auto;
      }
    }
  }

  &.plan-details {
    padding: 90px 0;
    background-image: linear-gradient(to top, #E7EBFB -10%, $white 40%, #EFF2FF);
    position: relative;

    h2.plan-header {
      font-size: 35px;

      .title {
        color: #5E5CB0;
      }
    }

    &::before {
      position: absolute;
      top: 100px;
      right: 5px;
      content: "";
      display: block;
      width: 98px;
      height: 84px;
      background-image: asset-url("layout/controllers/dashboards/ondemand/dots.webp");
      background-size: cover;
    }

    &::after {
      position: absolute;
      bottom: 240px;
      left: 0;
      content: "";
      display: block;
      width: 45px;
      height: 84px;
      background-image: asset-url("layout/controllers/dashboards/ondemand/dots_vertical.webp");
      background-size: cover;
    }

    .plan-header {
      color: #5E5CB0;
      width: 670px;

      .line-decorator-title {
        background-color: #5E5CB0;
      }
    }

    .plan-table.header {
      padding: 8px 11px;
      box-shadow: 0 -2px 10px 0 rgba(188, 200, 214, 0.4);
      background-color: $white;

      .header-text {
        line-height: 1;
      }

      .self-study-plans-head {
        background-image: linear-gradient(180deg, #A1A3BD 15%, #666579 5%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      span {
        width: 200px;
        display: block;
        text-align: center;
        position: relative;
        background-image: linear-gradient(182deg, #A1A3BD 91%, #666579 34%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;

        &:first-child {
          &::after {
            width: 1px;
            height: 27px;
            background-color: $grey-6;
            content: "";
            position: absolute;
            right: -8px;
            top: 8px;
          }
        }

        img {
          width: 121px;
          height: 23px;
        }
      }
    }

    .plan-divider {
      height: 2px;
      display: block;
      content: "";
      background-image: linear-gradient(to right, #C09D65, #FCE5BF 48%, #C09D65);
    }

    .plan-table {
      tbody {
        border-color: #EFF2FF !important;

        td {
          width: 210px;
          text-align: center;
          padding: 15px 0;
          color: $grey-1;

          span {
            color: #2B343D !important;
          }

          .count {
            color: #94825A;
          }

          .streaming-video {
            background-image: linear-gradient(131deg, #DBC590 3%, #675536 73%);
            letter-spacing: normal;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .score-imp {
            background-image: linear-gradient(to bottom, #CCB98C, #675536);
            letter-spacing: normal;
            text-align: center;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          i {
            color: #6C6B80;
          }

          &:first-child {
            text-align: left;
          }

          &.golden-check {
            background-image: linear-gradient(to bottom, #F2E6A8, #BAA37E, #8B7858);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          &:nth-child(2n) {
            .far {
              background-image: linear-gradient(to bottom, #A1A3BD, #6C6B80);
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          .dot-color {
            background-image: linear-gradient(to bottom, #FFE9C6 0%, #E4C085 46%, #B08F57);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }

    .try-ttp-ondemand-for-free {
      margin-top: 75px;

      hr {
        &.left,
        &.right {
          background-color: $grey-6;
          width: 100%;
        }
      }

      .profile-btn {
        display: inline-block;
        border-style: solid;
        width: 350px;
        border-image-source: linear-gradient(to bottom, $white 0%, rgba(251, 251, 251, 0) 100%);
        border-image-slice: 1;
        padding: 12px 25px;
        border-radius: 2px;
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
        background-image: linear-gradient(98deg, #BE9C63 4%, #FDE6C0 83%);
        text-align: center;
        color: #2F1B04;
        text-transform: uppercase;
        border-width: 0;

        &:hover {
          background-color: #BE9C63;
          background-image: unset;
          cursor: pointer;
        }
      }
    }
  }

  &.instructor {
    .instructor-profile {
      background-image: asset-url("layout/controllers/dashboards/ondemand/teacher_frame.webp");
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      height: 475px;
      padding: 100px 0;

      .instructor-details-title {
        color: $grey-1;
        width: 677px;
      }

      .instructor-details-feature {
        margin-top: 67px;
        color: $grey-2;
      }

      .line-decorator-title {
        background-color: #94825A;
      }

      .ttp-logo {
        width: 191px;
        height: 33px !important;
        object-fit: contain;
      }

      .gmat-logo {
        width: 70px;
      }

      .users-icon {
        width: 41px;
      }

      .gmatplus {
        width: 66px;
      }

      .logo-instructor {
        height: 50px;
        display: flex;
        align-items: center;
        image-rendering: crisp-edges;
        image-rendering: -webkit-optimize-contrast;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        -webkit-font-smoothing: subpixel-antialiased;
        shape-rendering: crispEdges;
        justify-content: center;
        margin-bottom: 1px;
      }

      .instructor-details-feature-title {
        background-image: linear-gradient(4deg, #A0804D 27%, #BD995D 77%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 26px;
        margin-bottom: 16px !important;
        line-height: 20px;
      }
    }

    .instructor-description {
      position: relative;

      .instructor-description-wrapper {
        padding: 33px 0 100px 0;
        color: $grey-1;
        max-width: 900px;
        line-height: 1.78;

        .text-gold {
          color: #896E43;
          font-weight: 600;
        }
      }

      &::before {
        position: absolute;
        top: 100px;
        right: 5px;
        content: "";
        display: block;
        width: 72px;
        height: 84px;
        background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
        background-size: cover;
      }

      &::after {
        position: absolute;
        bottom: 35px;
        left: 0;
        content: "";
        display: block;
        width: 90px;
        height: 63px;
        background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
        background-size: cover;
      }
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      .instructor-profile {
        .number-badge {
          font-size: 25px !important;
        }
      }
    }

    @media (min-width: 768px) and (max-width: 991px) {
      .instructor-profile {
        background-size: cover !important;

        .instructor-details-feature {
          .w-157 {
            width: 157px;
          }

          .w-150 {
            width: 150px;
          }
        }
      }
    }

    @media (max-width: 767px) {
      .instructor-profile {
        padding: 52px 0 !important;

        .instructor-details-feature {
          margin-top: 41px;

          .row {
            justify-content: space-around;
          }
        }
      }
    }

    @media (min-width: 600px) and (max-width: 767px) {
      .instructor-profile {
        height: 1280px !important;
      }
    }

    @media (max-width: 575px) {
      .instructor-profile {
        height: 1140px !important;

        &.ea {
          height: 920px !important;
        }

        .instructor-details-feature {
          margin-top: 20px !important;
        }
      }
    }
  }

  &.scott-woodbury {
    background-image: asset-url("layout/controllers/dashboards/ondemand/scott_frame.webp");
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 905px;
    display: flex;
    align-items: center;
    z-index: 4;

    .quotes-bg {
      background-image: asset-url("layout/controllers/dashboards/ondemand/quotes_bg_scott.webp");
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      height: 220px;
      padding: 20px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      object-fit: contain;
      box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.3);
      border-radius: 10px;

      strong {
        color: #5B4E33;
      }

      &::after {
        content: "";
        bottom: 0;
        right: 0;
        position: absolute;
        background-image: asset-url("layout/controllers/dashboards/ondemand/mask_group.webp");
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        width: 139px;
        height: 105px;
      }
    }

    .scott-detail-tab,
    .scott-woodbury-img-tab {
      display: none;
    }

    .text-gold {
      color: #896E43;
    }

    .scott-detail {
      margin-top: 73px;
      line-height: 34px;
    }

    .scott-student-title {
      color: $grey-4;
      padding: 20px 10px;
    }

    img.school-logo {
      object-fit: contain;
      flex-grow: 0;
      gap: 40px;

      &.harvard-img {
        width: 182px;
      }

      &.stanford-img {
        width: 158px;
      }

      &.mit-img {
        width: 196px;
      }

      &.kellog-img {
        width: 86px;
      }

      &.insead-img {
        width: 80px;
      }

      &.wharton-img {
        width: 151px;
      }
    }
  }

  &.why-ondemand {
    margin: 175px 0 115px 0;
    position: relative;
    z-index: 3;

    &.ea {
      background-color: $white;
      padding: 175px 0 115px 0;
      margin: 0;
    }

    &::after {
      background-image: asset-url("layout/controllers/dashboards/ondemand/why_ondemand_bg.webp");
      top: -300px;
      right: 0;
      background-size: cover;
      background-position: bottom;
      position: absolute;
      content: "";
      width: 492px;
      height: 868px;
      z-index: 1;
    }

    .why-ondemand-title {
      z-index: 4;
      position: relative;

      h3 {
        text-align: left;
        color: #5E5CB0;
      }

      .line-decorator-title {
        background-color: #5E5CB0;
      }
    }

    .why-ondemand-subtitle {
      color: #896E43;
      margin: 50px 0 43px 0;
    }

    .why-ondemand-head {
      color: $grey-1;

      .why-ondemand-head-highlight {
        color: #5E5CB0;
      }
    }

    .what-ondemand {
      margin-top: 80px;

      .what-ondemand-detail {
        color: $grey-1;
        line-height: 1.78;
      }

      .check-pointer {
        min-width: 16px;
        height: 16px;
        background-image: linear-gradient(to bottom, #FFE9C6 0%, #E4C085 46%, #B08F57 100%);
        content: "";
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
      }
    }

    .why-ondemand-right {
      z-index: 3;

      .play-icon {
        width: 50px;
        position: absolute;
        right: 0;
        left: 0;
        margin: 0 auto;
        top: 50%;
        transform: translate(0%, -50%);
      }

      img {
        pointer-events: fill !important;
        width: 512px;
        height: 266px;
        border: solid 3px transparent;

        &:not(.ea):hover {
          border-radius: 10px;
          box-shadow: 0 4.5px 29.1px 7.8px rgba(188, 200, 214, 0.3);
          border: solid 3px #5E5CB0;
          cursor: pointer;
        }
      }
    }
  }

  &.video-based-approach {
    height: 1061px;
    padding: 127px 0 112px 0;
    background-image: linear-gradient(to top, #E7EBFB -9%, $white 45%, #EFF2FF);
    position: relative;

    .video-based-ondemand-img-tab {
      display: none;
    }

    .slide-name {
      padding: 15px;
      color: $grey-1;
      position: absolute;
      bottom: 0;
      width: 100%;
      box-sizing: border-box;

      .quant-label {
        box-shadow: 0 0 6.8px 0 rgba(61, 170, 234, 0.6);
        background-image: linear-gradient(115deg, $blue-2 44%, #085785 127%);
        color: $white;
        width: 67px;
        height: 20px;
        padding: 0 5px;
        left: -15px;
        position: relative;
        line-height: 2;
      }

      .verbal-label {
        opacity: 0.9;
        box-shadow: 0 0 10px 0 rgba(183, 111, 255, 0.5);
        background-image: linear-gradient(110deg, $purple-verbal-2 46%, #5D2A72 121%);
        width: 90px;
        max-height: 23px;
      }

      .di-label {
        width: 108px;
        height: 20px;
        box-shadow: 0 0 6.8px 0 rgba(87, 194, 175, 0.6);
        background-image: linear-gradient(107deg, $cyan-di-2 -18%, #156A6A 106%);
      }

      img {
        width: 50px;
      }
    }

    &::before {
      position: absolute;
      top: 45%;
      right: 5px;
      content: "";
      display: block;
      width: 67px;
      height: 87.6px;
      background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
      background-size: cover;
    }

    .video-based-ondemand {
      .slide {
        border: solid 3px transparent;
        border-radius: 15px;

        &:hover {
          box-shadow: 0 4.5px 29.1px 7.8px rgba(188, 200, 214, 0.3);
          border: solid 3px #5E5CB0;
          cursor: pointer;
        }
      }

      .video-based-head-highlight {
        color: #5E5CB0;
      }

      .video-based-ondemand-detail {
        color: $grey-1;
        line-height: 1.78;
      }

      .check-pointer {
        min-width: 16px;
        height: 16px;
        background-image: linear-gradient(to bottom, #FFE9C6 0%, #E4C085 46%, #B08F57 100%);
        content: "";
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
      }
    }

    .video-based-ondemand-right {
      z-index: 3;
      padding-right: 50px;

      img {
        width: 490px;
      }
    }

    .video-based-experience {
      padding: 64px 0 96px 0;

      .video-based-experience-title {
        color: $grey-1;
      }
    }

    .video-based-experience-img {
      max-width: 100%;
    }

    .slide-active {
      transform: scale(1.2);
    }
  }

  &.score-improvement {
    position: relative;
    padding: 95px 0;

    &::after {
      background-image: asset-url("layout/controllers/dashboards/ondemand/ttp_expert_bg.webp");
      bottom: 0;
      right: 0;
      background-size: cover;
      background-position: bottom;
      position: absolute;
      content: "";
      width: 610px;
      height: 972px;
      z-index: -1;
    }

    &::before {
      position: absolute;
      top: 12.5%;
      left: 0;
      content: "";
      display: block;
      width: 67px;
      height: 87.6px;
      background-image: asset-url("layout/controllers/dashboards/ondemand/dots.webp");
      background-size: cover;
    }

    .score-improvement-ondemand {
      .score-improvement-head {
        color: $grey-1;

        .score-improvement-head-highlight {
          color: #5E5CB0;
        }
      }

      .score-improvement-detail {
        color: $grey-1;
        line-height: 1.78;
      }

      .check-pointer {
        min-width: 16px;
        height: 16px;
        background-image: linear-gradient(to bottom, #FFE9C6 0%, #E4C085 46%, #B08F57 100%);
        content: "";
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
      }

      .percentile-score {
        padding-bottom: 95px;
      }
    }

    .score-improvement-left {
      padding-top: 95px;
    }

    .score-improvement-right {
      z-index: 3;

      .first {
        width: 417px;
      }

      img {
        width: 563px;
      }

      .score-improvement-img-tab {
        display: none;
      }
    }
  }

  &.team-teachers {
    .team-teachers-right {
      padding-left: 35px;

      .score-team-teachers-ondemand-detail {
        line-height: 1.78 !important;
      }
    }

    .team-teachers-ondemand {
      .team-teachers-head {
        color: $grey-1;

        .team-teachers-head-highlight {
          color: #5E5CB0;
        }
      }

      .team-teachers-ondemand-detail {
        color: $grey-1;
        line-height: 1.78;
      }

      .check-pointer {
        min-width: 16px;
        height: 16px;
        background-image: linear-gradient(to bottom, #FFE9C6 0%, #E4C085 46%, #B08F57 100%);
        content: "";
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
      }
    }

    .team-teachers-ondemand-right {
      z-index: 3;

      img {
        width: 614px;
      }
    }

    .team-teachers-experience {
      padding: 64px 0 96px 0;

      .team-teachers-experience-title {
        color: $grey-1;
      }
    }

    .team-teachers-experience-img {
      max-width: 90%;

      &.active {
        transform: scale(1.2);
      }
    }
  }

  &.one-year-subscription {
    background-image: linear-gradient(to top, #EFECFF, $white 24%);

    .one-year-subscription-description {
      padding-top: 116px;

      .one-year-subscription-head-highlight {
        color: #5E5CB0;
      }

      .one-year-subscription-detail {
        color: $grey-1;
        line-height: 1.78;
      }

      .check-pointer {
        min-width: 16px;
        height: 16px;
        background-image: linear-gradient(to bottom, #FFE9C6 0%, #E4C085 46%, #B08F57 100%);
        content: "";
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
      }
    }

    .one-year-subscription-right {
      z-index: 3;

      img {
        width: 494px;
        height: 524px;
      }
    }

    .profile {
      margin-top: 80px !important;
    }
  }

  &.ondemand-faq {
    padding: 86px 0 98px 0;

    .faq-title {
      text-align: center;
      margin: 0 auto;
      margin-bottom: 20px;
      color: #5E5CB0;

      &::after {
        width: 38px;
        height: 5px;
        flex-grow: 0;
        margin: 0 auto;
        border-radius: 2px;
        background-color: #5E5CB0;
        content: "";
        display: block;
        margin-top: 20px;
      }
    }

    .accordions-col {
      .accordion {
        &:last-child {
          .accordion-item {
            border-bottom: 1px solid $grey-6 !important;
          }
        }

        .accordion-item {
          background-color: transparent;

          h3 {
            button.accordion-button {
              background-color: transparent;
              min-height: 115px;
              line-height: 1.3;
              padding-right: 2rem;
              border-bottom: 1px solid $grey-6;
              transition: border-color 0.15s ease-in-out;
              font-size: 20px;
              font-weight: 600;

              span {
                max-width: 75%;
              }

              @media (min-width: 768px) {
                font-size: 20px;
              }

              &::after {
                color: #5E5CB0;
                background-image: none;
                font-family: "Font Awesome 5 Pro";
                content: "\f067";

                /* plus */
                position: absolute;
                right: 0;
                transition: none;
                height: 1.25rem;
                width: 1.25rem;
              }

              &:not(.collapsed) {
                border-bottom-color: transparent;
                box-shadow: none;

                &::after {
                  transform: none;
                  content: "\f068";

                  /* minus */
                }
              }
            }
          }

          .accordion-collapse {
            p {
              padding: 48px 32px;
              background-color: $light-2;
              line-height: 1.94;
              font-weight: normal;
            }
          }
        }

        &:last-of-type {
          @media (min-width: 1200px) {
            .accordion-item {
              h3 {
                button.accordion-button {
                  border-bottom-color: transparent;
                }
              }
            }
          }
        }
      }

      &:last-of-type {
        .accordion {
          &:last-of-type {
            .accordion-item {
              h3 {
                button.accordion-button {
                  border-bottom-color: transparent;
                }
              }
            }
          }
        }
      }
    }
  }

  &.experience-transformative {
    .transformative-bg-img {
      background-image: asset-url("layout/controllers/dashboards/ondemand/experience_transformative_bg.webp");
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      height: 330px;
      padding: 54px 0 58px 61px;
      margin-bottom: 24px;

      .line-decorator-title {
        background-color: $white;
      }

      .purchase-btn-group {
        width: 250px;

        .purchase-now-btn {
          &:hover {
            color: $primary-3 !important;
          }

          text-decoration: underline;
          text-decoration-color: rgba(255, 255, 255, 0.42);
        }
      }

      .profile-btn {
        border-style: solid;
        width: 250px;
        border-image-source: linear-gradient(to bottom, $white 0%, rgba(251, 251, 251, 0) 100%);
        border-image-slice: 1;
        padding: 10px 25px;
        border-radius: 2px;
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
        background-image: linear-gradient(98deg, #BE9C63 4%, #FDE6C0 83%);
        text-align: center;
        color: #2F1B04;
        text-transform: uppercase;
        border-width: 0;
        margin-top: 57px;

        &:hover {
          background-color: #BE9C63;
          background-image: unset;
          cursor: pointer;
        }
      }
    }
  }

  &.scott-woodbury,
  &.one-year-subscription {
    .profile {
      background-image: asset-url("layout/controllers/dashboards/ondemand/unlock_button_bg.webp");
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      padding: 22px 49px 22px 40px;
      border-radius: 8px;
      align-items: center;
      margin: 120px 0 -45px 0;

      .description {
        width: 100%;
      }

      .purchase-now-btn {
        &:hover {
          color: $primary-3 !important;
        }

        text-decoration: underline;
        text-decoration-color: rgba(255, 255, 255, 0.42);
      }

      .profile-btn {
        border-style: solid;
        width: 240px;
        border-image-source: linear-gradient(to bottom, $white 0%, rgba(251, 251, 251, 0) 100%);
        border-image-slice: 1;
        padding: 12px 20px;
        border-radius: 2px;
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
        background-image: linear-gradient(98deg, #BE9C63 4%, #FDE6C0 83%);
        text-align: center;
        color: #2F1B04;
        text-transform: uppercase;
        border-width: 0;

        &:hover {
          background-color: #BE9C63;
          background-image: unset;
          cursor: pointer;
        }
      }
    }
  }

  @media (min-width: 1440px) {
    &.course-banner {
      .course-banner-video {
        width: 55%;
      }
    }
  }

  @media (min-width: 1024px) and (max-width: 1199px) {
    &.course-banner {
      .course-banner-video {
        width: 75%;
      }
    }
  }

  @media (min-width: 992px) and (max-width: 1199px) {
    &.course-banner {
      .course-banner-title {
        width: 650px;
      }

      .course-banner-detail {
        width: 550px;
      }
    }

    &.scott-woodbury {
      .profile {
        margin: 80px 0 -45px 0;
      }
    }

    &.why-ondemand {
      margin: 175px 0 115px 0;
    }
  }

  @media (max-width: 1300px) {
    &.course-banner {
      background-image: unset;

      .course-banner-video-text {
        max-width: 583px;
      }

      .course-banner-title {
        width: 100%;
        font-size: 40px;
      }
    }
  }

  @media (min-width: 1024px) {
    &.course-banner {
      background-image: unset;
    }
  }

  @media (max-width: 1023px) {
    &.course-banner {
      background-image: asset-url("layout/controllers/dashboards/ondemand/course_banner_bg_tab.webp");

      .course-banner-video {
        display: none;
      }

      .course-banner-title {
        width: 100%;
        font-size: 40px;
      }

      .course-banner-video-text {
        max-width: 100%;
      }

      .course-banner-detail {
        width: 90%;
      }
    }
  }

  @media (max-width: 991px) {
    &.founder-detail {
      .founder-detail-title {
        width: 100%;
      }

      .founder-wrapper {
        padding-top: 80px;

        .founder-wrapper-card {
          max-width: 31%;
        }
      }
    }

    &.plan-details {
      padding: 79px 0 70px 0;

      .row {
        padding: 0 15px;
      }

      &::before {
        background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
        background-size: contain;
        background-repeat: no-repeat;
        right: 10px;
        top: 80px;
        width: 42px;
      }

      &::after {
        background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
        background-size: contain;
        background-repeat: no-repeat;
        bottom: 20px;
        width: 45px;
      }

      .plan-table {
        tbody {
          td {
            .dot-color {
              display: none;
            }

            width: 130px;
          }
        }

        &.header {
          span {
            &:first-child {
              &::after {
                display: none;
              }
            }

            width: 120px;

            img {
              width: 100px;
            }
          }
        }
      }
    }

    &.instructor {
      .instructor-profile {
        background-image: asset-url("layout/controllers/dashboards/ondemand/teacher_frame_tab.webp");
        height: auto;
        padding: 64px 0 49px 0;
        background-size: 100%;

        .row {
          padding: 0 15px;
        }

        .instructor-details-title {
          width: 80%;
        }
      }

      .instructor-description {
        .instructor-description-wrapper {
          min-width: 90%;
          padding: 33px 0 50px 0;

          p {
            min-width: 100%;
          }
        }

        &::after {
          display: none;
        }

        &::before {
          background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
          background-size: contain;
          background-repeat: no-repeat;
          right: 10px;
          width: 42px;
          top: 60px;
        }
      }
    }

    &.scott-woodbury {
      background-image: asset-url("layout/controllers/dashboards/ondemand/scott_frame_tab.webp");
      height: auto;
      position: relative;
      background-position: bottom;

      .container {
        position: static;
        margin-left: 0;
        min-width: 100%;
        margin-right: 0;
        padding-left: 0;
        padding-right: 0;
      }

      .quotes-bg {
        width: 85%;
        margin: 0 auto;
        height: fit-content;

        &::after {
          display: none;
        }
      }

      .scott-detail,
      .scott-woodbury-img {
        display: none;
      }

      .scott-detail-tab,
      .scott-woodbury-img-tab {
        display: block;
      }

      .scott-detail-tab {
        max-width: 680px;
        margin: 0 auto;
        line-height: 1.78;
        font-weight: 300;
      }

      .container-box {
        max-width: 720px;
        margin: 0 auto;
      }

      .scott-accessible {
        max-width: 680px;
        margin: 0 auto;
      }

      .scott-woodbury-img-tab {
        position: relative;
        left: 0;
        min-width: 415px;
      }
    }

    &.why-ondemand {
      margin: 153px 0 98px 0;

      &.ea {
        padding: 153px 0 98px 0;
        margin: 0;
      }

      &::after {
        z-index: -1;
      }

      .row {
        padding: 0 15px;
      }

      .what-ondemand {
        margin-top: 57px;
      }
    }

    &.video-based-approach {
      height: auto;

      &::before {
        background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
        background-size: contain;
        right: 0;
        background-position: right;
        top: 30%;
        background-repeat: no-repeat;
      }

      .video_based_approach-detail {
        max-width: 44%;
      }

      .container {
        position: static;
        margin-left: 0;
        min-width: 100%;
        margin-right: 0;
        padding-left: 0;
      }

      .video-based-ondemand-img {
        display: none;
      }

      .video-based-ondemand-img-tab {
        display: block;
        position: relative;
        left: 0;
        max-width: 335px;
      }

      .video-based-experience {
        max-width: 720px;
        margin: 0 auto;
        padding: 64px 0 67px 0;
      }

      .video-based-ondemand {
        .leaning-row {
          max-width: 50%;
          margin: 0 auto;
          gap: 50px;
        }

        .video-based-ondemand-detail {
          width: 85%;
        }
      }
    }

    &.score-improvement {
      overflow: hidden;

      &::after {
        background-image: asset-url("layout/controllers/dashboards/ondemand/ttp_expert_bg_tab.webp");
        background-size: cover;
        background-position: bottom right -180px;
        background-repeat: no-repeat;
      }

      &::before {
        left: unset;
        right: 0;
        top: 6.5%;
        background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
        background-size: contain;
        background-position: right;
        background-repeat: no-repeat;
      }

      .score-improvement-ondemand {
        .percentile-score {
          padding-bottom: 0;
        }
      }

      .container {
        position: static;
        margin-left: 0;
        min-width: 100%;
        margin-right: 0;
        padding-left: 0;

        .row {
          max-width: 720px;
          margin: 0 auto;
          align-items: center;

          .score-improvement-right {
            .score-improvement-img {
              display: none;
            }

            .score-improvement-img-tab {
              display: block;
              position: absolute;
              right: 0;
              width: 450px;
              top: 55%;
            }
          }
        }
      }
    }

    &.team-teachers {
      .team-teachers-ondemand {
        flex-direction: column-reverse;
      }

      .team-teachers-right {
        padding: 0 85px;
      }
    }

    &.one-year-subscription {
      padding-bottom: 70px;

      .one-year-subscription-description {
        align-items: center;
      }
    }

    &.experience-transformative {
      .transformative-bg-img {
        background-image: asset-url("layout/controllers/dashboards/ondemand/experience_transformative_bg_tab.webp");
        padding: 30px 40px;
        background-size: 100%;
        background-position: center;
        border-radius: 10px;

        .profile-btn {
          margin-top: 25px;
        }
      }
    }

    &.ondemand-faq {
      padding: 50px 0 101px 0;
    }

    &.scott-woodbury,
    &.one-year-subscription {
      .profile {
        background-image: asset-url("layout/controllers/dashboards/ondemand/unlock_button_bg_tab.webp");
        align-items: flex-start;
        margin-top: 70px;
        margin-bottom: -85px;

        .profile-btn {
          width: 285px;
          margin-top: 20px;
        }
      }
    }
  }

  @media (min-width: 992px) and (max-width: 1200px) {
    &.experience-transformative {
      .transformative-bg-img {
        height: 350px;

        .profile-btn {
          margin-top: 40px;
        }
      }
    }
  }

  @media (max-width: 767px) {
    .container {
      padding: 0 24px !important;
    }

    &.course-banner {
      padding: 55px 0;
      height: auto;

      .row {
        padding: 0;
      }

      .try-free-btn,
      .upgrade-btn,
      .purchase-btn {
        width: 100%;
      }

      .course-banner-title {
        font-size: 30px;

        .logo-img {
          width: 151px;
        }
      }
    }

    &.founder-detail {
      padding: 58px 0 66px 0;

      .founder-detail-title {
        font-size: 25px;
      }

      .founder-wrapper {
        .founder-wrapper-card {
          min-width: 60%;
          margin: 0 auto;
        }
      }
    }

    &.plan-details {
      padding: 81px 0 100px 0;

      .plan-header {
        font-size: 30px;
      }

      .header {
        width: 180px;
        padding: 5px !important;

        span {
          width: 90px;
          font-size: 16px;

          img {
            width: 70px !important;
          }
        }
      }

      .plan-table {
        tbody {
          td:first-child {
            width: 150px;
          }

          td {
            width: 90px;
          }
        }

        &.header {
          span {
            &:first-child {
              &::after {
                display: none;
              }
            }

            width: 100px;
          }
        }
      }

      .try-ttp-ondemand-for-free {
        .profile-btn {
          width: 300px;
        }
      }

      &::before {
        top: 191px;
      }

      &::after {
        background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
      }
    }

    &.instructor {
      .instructor-profile {
        background-image: asset-url("layout/controllers/dashboards/ondemand/teacher_frame_mobile.webp");
        height: 1200px;
        background-size: cover;
        background-position: bottom;
        padding: 86px 0 49px 0;

        .row {
          padding: 0;
        }

        .instructor-details-title {
          width: 100%;
          font-size: 30px;
        }
      }

      .instructor-description {
        padding: 0 15px;

        &::before {
          right: 0;
          background-image: asset-url("layout/controllers/dashboards/ondemand/<EMAIL>");
        }
      }
    }

    &.scott-woodbury,
    &.one-year-subscription {
      .profile {
        background-image: asset-url("layout/controllers/dashboards/ondemand/unlock_button_bg_mobile.webp");
        margin-top: 95px;
        align-items: flex-start;
        background-size: cover;
        background-position: bottom right;
        padding: 30px;
        margin-bottom: 0;
        min-height: 314px;
        line-height: 1.4;

        .profile-btn {
          margin-bottom: 10px;
          padding: 10px;
        }
      }

      .one-year-subscription-description {
        padding-top: 30px;
      }
    }

    &.scott-woodbury {
      .scott-student-title {
        text-align: center;
        padding: 62px 0 38px 0;
      }

      .scott-woodbury-img-tab {
        display: none;
      }

      .container-box {
        max-width: 470px;
      }

      .scott-woodbury-img {
        display: block;
        margin: 0 auto !important;
      }

      .scott-accessible {
        img {
          margin-bottom: 50px;
        }
      }

      .profile {
        margin-top: 45px;
        margin-bottom: -110px;
      }
    }

    &.why-ondemand {
      margin: 180px 0 68px 0;

      &.ea {
        padding: 180px 0 68px 0;
        margin: 0;
      }

      .why-ondemand-title {
        h3 {
          font-size: 30px;
        }
      }

      .why-ondemand-head {
        h4 {
          min-width: 100%;
          font-size: 24px;
        }
      }

      .why-ondemand-subtitle {
        margin: 30px 0 30px 0;
      }

      .what-is-ondemand-video {
        margin-top: 30px;
      }
    }

    &.video-based-approach {
      padding: 58px 0 83px 0;

      &::before {
        display: none;
      }

      .container {
        padding: 0 30px;

        .video-based-ondemand {
          margin: 0 auto;
          width: 470px;

          h4 {
            width: 100% !important;
          }

          .video_based_approach-detail {
            max-width: 100%;
          }

          .video-based-ondemand-right {
            .video-based-ondemand-img-tab {
              display: none;
            }

            .video-based-ondemand-img {
              display: block;
            }
          }

          .video-based-ondemand-detail {
            width: 100%;
          }

          .leaning-row {
            max-width: 100%;
          }
        }

        .video-based-experience {
          .video-based-experience-title {
            font-size: 24px;

            img {
              width: 147px;
            }
          }
        }
      }
    }

    &.score-improvement {
      &::before {
        display: none;
      }

      .container {
        .row {
          width: 470px;

          .score-improvement-right {
            .score-improvement-img-tab {
              position: unset !important;
            }
          }
        }
      }

      &::after {
        display: none;
      }

      .score-improvement-left {
        padding-top: 0;
      }

      .score-improvement-ondemand {
        .percentile-score {
          padding-bottom: 95px;
        }
      }
    }

    &.team-teachers {
      .team-teachers-right {
        padding: 0;
      }
    }

    &.one-year-subscription {
      padding-bottom: 60px;

      h4 {
        min-width: 100%;
      }
    }

    &.experience-transformative {
      .transformative-bg-img {
        background-image: asset-url("layout/controllers/dashboards/ondemand/experience_transformative_bg_mobile.webp");
        background-size: cover;
        height: 560px;
        max-width: 85%;
        margin: 0 auto 20px auto;
        box-sizing: content-box;
        padding: 25px !important;

        .transformative-row {
          height: 100%;
          display: flex;
          width: 100%;
          flex-direction: column;

          h3,
          .line-decorator-title {
            margin: 0 10px;
          }

          .purchase-btn-group {
            margin: auto auto 0 auto;
          }

          .profile-btn {
            margin-top: auto;
          }
        }
      }
    }

    &.ondemand-faq {
      padding: 50px 0 80px 0;
    }
  }

  @media (max-width: 575px) {
    &.course-banner {
      background-image: asset-url("layout/controllers/dashboards/ondemand/course_banner_bg_mobile.webp");
    }

    &.score-improvement {
      .container {
        .row {
          width: 100%;
        }
      }
    }

    &.video-based-approach {
      .container {
        .video-based-ondemand {
          margin: 0 auto;
          width: 80%;
        }
      }
    }

    &.instructor {
      .instructor-profile {
        height: 1050px;
      }
    }

    &.scott-woodbury,
    &.one-year-subscription {
      .profile {
        .profile-btn {
          width: 100%;
        }
      }
    }
  }

  @media (max-width: 414px) {
    &.scott-woodbury,
    &.one-year-subscription {
      .profile {
        .profile-btn {
          width: 100%;
        }
      }
    }

    &.experience-transformative {
      .transformative-bg-img {
        .profile-btn {
          width: 100%;
          padding: 10px;
        }
      }
    }
  }
}

.line-decorator-title {
  width: 38px;
  border-radius: 10px;
  height: 5px;
}
