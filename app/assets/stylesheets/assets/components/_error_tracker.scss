.error-tracker {
  border: 1px solid $grey-6;
  display: block;
  margin-top: 58px;
  padding: 20px;

  .failure_reason {
    > .header {
      background: none;
      color: $dark-1;
      padding: 0;
    }

    > .body {
      form .btn {
        @media only screen and (max-width: 767px) {
          width: 100%;
        }

        &.disabled {
          border-color: $grey-5;
          pointer-events: none;
          color: $grey-5;
        }
      }

      #exercise_attempt_failure_reason_id,
      #problem_attempt_failure_reason_id {
        background: $white;
        border: 1px solid $grey-6;
        border-radius: 2px;
        color: $grey-4;
        display: inline-block;
        float: left;
        font-size: 16px;
        font-weight: 600;
        height: 44px;
        padding: 0 8px;
        position: relative;
        width: 55%;

        @media only screen and (max-width: 767px) {
          margin-bottom: 15px;
          width: 100%;
        }
      }
    }
  }
}
