.modal#calendar-setup-modal,
.adjust-study-plan-settings {
  section#view-preferences {
    .study-plan-view-card-wrapper {
      .study-plan-view-card {
        position: relative;
        padding: 40px 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid $grey-6;
        border-radius: 4px;
        height: 100%;
        cursor: pointer;

        .checked,
        .unchecked {
          background: white;
          border-radius: 50%;
          font-size: 24px;
          position: absolute;
          top: 12px;
          right: 12px;
        }

        .checked {
          color: $primary-2;
          display: none;
        }

        .unchecked {
          color: $grey-6;
          display: block;
        }

        .card-title {
          font-size: 20px;
          font-weight: normal;
          line-height: 1.4;
          color: $dark-1;
        }

        .card-description {
          color: $grey-2;

          .calendar-view-description {
            color: $blue-2;
          }
        }

        &.active {
          border: 1px solid $primary-2;
          background-color: $primary-5;

          .checked {
            display: block;
            background: white;
            border-radius: 50%;
          }

          .unchecked {
            display: none;
          }

          .card-title {
            font-weight: 600;
          }

          .card-title,
          .card-description {
            color: $blue-1;
          }
        }
      }
    }
  }

  section#date-time-preferences {
    .hours-and-start-date-wrapper {
      .form-control {
        border-color: $grey-6;

        &:focus {
          border-color: $blue-3;
          box-shadow: none;
        }

        &:disabled {
          color: $grey-5;
        }
      }

      .hours-wrapper {
        padding: 20px;

        .choice-card {
          padding: 0;

          .title {
            margin-bottom: 20px;
            cursor: pointer;

            > i {
              height: 20px;
              color: $grey-6;
              font: normal normal normal 14px/1 "Font Awesome 5 Pro";
              font-size: 20px;

              &::before {
                content: "\f111";
                line-height: 20px;
              }
            }
          }

          input {
            height: 44px;
            -moz-appearance: textfield;

            &[type=number]::-webkit-inner-spin-button,
            &[type=number]::-webkit-outer-spin-button {
              -webkit-appearance: none;
              -moz-appearance: textfield;
              appearance: none;
              margin: 0;
            }
          }

          &.per-week {
            margin-right: 22px;

            .week-hour-input-wrapper {
              padding: 5px;

              label {
                color: $blue-2;
              }
            }
          }

          &.per-day {
            margin-left: 22px;

            .days-wrapper {
              display: flex;
              flex-wrap: wrap;

              .day-hour-input-wrapper {
                flex-basis: 14.28%;
                padding: 5px;
                min-width: 60px;

                label {
                  color: $blue-2;
                }
              }
            }
          }

          &.active {
            .title {
              > i {
                font-size: 20px;
                color: $primary-1 !important;

                &::before {
                  content: "\f192";
                }
              }
            }
          }
        }
      }
    }
  }

  section#availability {
    #calendar {
      border-radius: 4px;
      box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);

      #header {
        padding: 8px 14px;

        #month-nav {
          #prev-month,
          #next-month {
            cursor: pointer;
            color: $blue-3;
            padding: 0 5px;

            &:hover {
              background-color: $blue-5;
            }

            &.disabled {
              pointer-events: none;
              color: $grey-5;
            }
          }
        }

        #date-today {
          width: 80px;
          height: 32px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          border-color: $grey-6;

          &:focus,
          &:active {
            border: none;
          }
        }
      }

      #days-name-wrapper {
        background-color: $light-2;
        display: flex;
        justify-content: space-between;
        border-top: 1px solid $grey-6;
        border-bottom: 1px solid $grey-6;
        box-shadow: 0 2px 10px 7px rgba(188, 200, 214, 0.25);

        .day {
          text-transform: uppercase;
          color: $grey-3;
          font-size: 13px;
          width: 14.28%;
          text-align: left;
          padding: 5px;
        }
      }

      #weeks-wrapper {
        .week {
          display: flex;
          border-bottom: 1px solid $grey-7;

          .date {
            position: relative;
            font-size: 10px;
            font-weight: 600;
            line-height: 1.33;
            width: 14.28%;
            height: 55px;
            text-align: left;
            padding: 3px;
            border: 1px solid transparent;
            border-right: 1px solid $grey-7;
            color: $grey-2;
            display: flex;
            align-items: center;
            justify-content: center;

            span.date-text {
              text-transform: uppercase;
              position: absolute;
              top: 2px;
              left: 2px;
            }

            &.present,
            &.future {
              cursor: pointer;
            }

            &.excluded {
              color: $blue-3;
              background: linear-gradient(to left top, transparent 48.75%, $grey-7 50%, $grey-7 50%, transparent 52.25%);
              background-color: $light-2;

              span.date-text {
                text-decoration: line-through;
                text-decoration-color: $blue-3;
              }
            }

            &.today {
              border-top: 4px solid $blue-3;

              span.date-text {
                width: fit-content;
                height: fit-content;
                background: $blue-3;
                color: $white;
                border-radius: 24px;
                padding: 2px 5px;
              }
            }

            &.exam {
              pointer-events: none;
              color: $blue-3;
              background-image: linear-gradient(to bottom, rgba(191, 224, 238, 0.17), $primary-5);
              position: relative;

              span.exam-name {
                text-transform: uppercase;
              }
            }

            &.past,
            &.future.inactive,
            &.present.inactive {
              color: $grey-5;
              pointer-events: none;

              &.excluded {
                span.date-text {
                  text-decoration-color: $grey-5;
                }
              }
            }

            &:hover {
              color: $blue-3;
              border: 1px solid $blue-3 !important;

              &.today {
                border-top-width: 4px !important;
              }
            }

            &:last-child {
              border-right: none;
            }
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }

  @media only screen and (min-width: 576px) {
    section#availability {
      #calendar {
        #weeks-wrapper {
          .week {
            .date {
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  @media only screen and (min-width: 768px) and (max-width: 1199px) {
    section#availability {
      .study-plan-view-card-wrapper {
        .study-plan-view-card {
          margin: 0 35px;
        }
      }
    }
  }

  @media only screen and (min-width: 992px) {
    section#date-time-preferences {
      .hours-and-start-date-wrapper {
        .hours-wrapper {
          .choice-card {
            &.per-day {
              .days-wrapper {
                .day-hour-input-wrapper {
                  min-width: 55px;
                }
              }
            }
          }
        }
      }
    }
  }

  @media only screen and (max-width: 767px) {
    section#view-preferences {
      .study-plan-view-card-wrapper {
        .study-plan-view-card {
          flex-direction: column;
          padding: 40px 24px;
          text-align: center;
        }
      }
    }

    section#date-time-preferences {
      .hours-and-start-date-wrapper {
        .hours-wrapper {
          .choice-card {
            &.per-week {
              margin-right: 0;
            }

            &.per-day {
              margin-left: 0;
              margin-top: 50px;
            }
          }
        }
      }
    }

    section#availability {
      #calendar {
        #days-name-wrapper {
          .day {
            visibility: hidden;

            &::first-letter {
              visibility: visible;
            }
          }
        }
      }
    }
  }

  @media only screen and (max-width: 575px) {
    section#availability {
      #calendar {
        #header {
          #month-nav {
            justify-content: space-between;
            width: 100%;
          }
        }
      }
    }
  }

  @media only screen and (max-width: 414px) {
    section#availability {
      #calendar {
        #header {
          #date-today {
            width: 50px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
