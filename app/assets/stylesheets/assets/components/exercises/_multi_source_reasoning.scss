.multi-source-reasoning.section-wrapper {
  .left-section,
  .right-section {
    .content-box {
      &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: $white;
      }

      &::-webkit-scrollbar-thumb {
        background: $grey-5;
        border-radius: 20px;
      }
    }
  }

  .left-section {
    padding: 0;
    width: 53%;

    .content-box {
      padding: 0 40px;
      margin: 25px 0;

      &::-webkit-scrollbar {
        width: 5px !important;
      }

      .tab-navigation {
        margin-bottom: 20px;

        .header-sub-navigation-inner {
          border-bottom: 1px solid $grey-6;

          a.tab-btn {
            align-items: center;
            display: flex;
            font-size: 15px;
            height: 38px;
            justify-content: center;
            line-height: 1.33;
            color: $grey-4;
            font-weight: normal;
            white-space: nowrap;

            &.active {
              color: $dark-1;
              font-weight: 600;
            }
          }
        }

        .navigation-dropdown {
          border-bottom: 1px solid $grey-6;

          .dropdown {
            &.navigation-tab-list {
              .dropdown-toggle {
                .fa-angle-up {
                  display: none;
                }

                .fa-angle-down {
                  display: inline-block;
                }

                &.show {
                  .fa-angle-up {
                    display: inline-block;
                  }

                  .fa-angle-down {
                    display: none;
                  }
                }

                &:focus {
                  box-shadow: none;
                }

                &::after {
                  display: none;
                }
              }

              .dropdown-item {
                &:active,
                &.active {
                  background-color: #E9ECEF !important;
                  color: black;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }

      p:last-child {
        margin-bottom: 0 !important;
      }
    }
  }

  .right-section {
    padding: 0;
    width: 47%;

    .content-box {
      .question-wrapper {
        padding: 25px 50px 0 50px;
        overflow-x: hidden;

        .video-wrapper {
          margin-top: 0;
        }

        .exercise-container {
          .instructors {
            flex-wrap: wrap;
            padding-left: 19px;

            .presented-by {
              width: 100%;
              color: $dark-2;
              font-weight: 600;
              font-size: 12px !important;
            }

            .instructor-info {
              flex-wrap: wrap;
              font-weight: 600;

              img {
                margin-left: 0;
              }

              .instructor-name {
                color: $blue-1;
              }

              .instructor-title {
                color: $blue-2;
                font-weight: 600;
              }
            }

            .blue-dots {
              position: absolute;
              right: 4px;
              top: 14px;

              @media screen and (max-width: 992px) {
                top: 8px;
              }

              @media screen and (max-width: 480px) {
                top: 36px;
              }
            }
          }
        }
      }

      .blue-dots {
        top: 17px;

        @media screen and (max-width: 480px) {
          top: 52px;
        }
      }

      .interrogation_part,
      .answers {
        padding: 0 !important;
      }

      .answers {
        .answer {
          margin-bottom: 20px;
        }

        &.multiple-answers {
          margin-top: -20px;

          table {
            thead {
              background: $white;

              th {
                border: none;
                padding: 5px 10px;
                font-weight: 600;
                color: $grey-2;

                &:first-child {
                  border: none;
                }
              }
            }

            tbody {
              tr {
                td {
                  border: none;
                  text-align: left;
                  padding: 5px 10px 10px 10px;

                  .answer {
                    justify-content: center;
                    margin: 0;

                    label {
                      flex: none;

                      .styledRadio {
                        margin: 0;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .error-tracker {
        .failure_reason {
          > .body {
            form {
              @media only screen and (max-width: 1200px) {
                .btn {
                  width: 100% !important;
                  margin-left: 0 !important;
                }

                #exercise_attempt_failure_reason_id,
                #problem_attempt_failure_reason_id {
                  margin-bottom: 15px;
                  width: 100%;
                }
              }
            }
          }
        }
      }

      .info-box {
        .info-box-content {
          .video-wrapper {
            padding-bottom: 46.75%;
          }
        }
      }
    }
  }

  @media screen and (min-width: 768px) {
    .left-section {
      border-right: 1px solid transparent;

      &.type-problem {
        border-right: 1px solid $grey-6;
      }

      .content-box {
        padding-right: 28px;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .right-section {
      .content-box {
        overflow-y: auto;
        overflow-x: hidden;
      }
    }
  }

  @media screen and (min-width: 1400px) {
    .left-section {
      .content-box {
        height: calc(100vh - 235px);
      }
    }

    .right-section {
      .content-box {
        height: calc(100vh - 222px);
      }
    }
  }

  @media screen and (max-width: 991px) {
    .left-section {
      .content-box {
        .tab-navigation {
          .navigation-dropdown {
            button {
              &.btn:not(.btn-outline-*) {
                color: #000;

                &:hover {
                  color: #000;
                }
              }
            }
          }
        }
      }
    }

    .right-section {
      .content-box {
        .question-wrapper {
          padding: 25px 25px 0;
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    .left-section {
      width: 100%;
      padding: 0;

      .content-box {
        padding: 0 20px;

        .tab-navigation {
          .navigation-dropdown {
            button {
              span {
                text-overflow: ellipsis;
                overflow: hidden;
                width: 170px;
                display: inline-block;
                white-space: nowrap;
                vertical-align: bottom;
              }
            }
          }
        }
      }
    }

    .right-section {
      width: 100%;
      border-top: 1px solid $grey-6;

      .content-box {
        .question-wrapper {
          padding: 15px 20px 10px 20px;
        }
      }
    }
  }
}

.exercise-attempt-container {
  @media screen and (min-width: 768px) {
    .multi-source-reasoning.section-wrapper {
      .left-section {
        .content-box {
          height: calc(100vh - 165px) !important;
        }
      }

      .right-section {
        .content-box {
          height: calc(100vh - 115px) !important;
        }
      }
    }
  }
}
