.table-analysis.section-wrapper {
  .left-section,
  .right-section {
    .content-box {
      &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: $white;
      }

      &::-webkit-scrollbar-thumb {
        background: $grey-5;
        border-radius: 20px;
      }
    }
  }

  .left-section {
    padding: 0;
    width: 53%;

    .content-box {
      padding: 0 40px;
      margin: 25px 0;

      &::-webkit-scrollbar {
        width: 5px !important;
      }

      p:last-child {
        margin-bottom: 0 !important;
      }

      .sort-by-wrapper {
        span.sort-by {
          color: $grey-2#4D4D4D !important;
          font-weight: 600 !important;
          margin-right: 12px;
          font-size: 14px !important;
          text-transform: uppercase;
        }

        > select {
          cursor: pointer;
          width: 185px;
          font-size: 18px;
          border-radius: 2px;
          border: solid 1px $grey-6;
          color: $grey-2;
          padding: 4px 7px;
          height: 33px;

          &:focus,
          :active {
            border-color: $primary-4;
          }
        }
      }

      table {
        color: unset;

        thead {
          th {
            font-weight: 600;
            color: $grey-2;
            text-transform: capitalize;
          }
        }

        tbody {
          tr {
            td {
              padding-top: 10px;
              padding-bottom: 10px;
              border-top: 0;

              &:not(:last-child) {
                border-right: 0;
              }

              &:first-child {
                text-align: left;
                padding-left: 15px;
              }
            }
          }
        }
      }
    }
  }

  .right-section {
    padding: 0;
    width: 47%;

    .content-box {
      .question-wrapper {
        padding: 25px 50px 0 50px;
        overflow-x: hidden;

        .video-wrapper {
          margin-top: 0;
        }
      }

      .interrogation_part,
      .answers {
        padding: 0 !important;
      }

      .answers {
        .answer {
          margin-bottom: 20px;
        }

        table {
          thead {
            background: $white;

            th {
              border: none;
              padding: 5px 10px;
              font-weight: 600;
              color: $grey-2;

              &:first-child {
                border: none;
              }
            }
          }

          tbody {
            tr {
              td {
                border: none;
                text-align: left;
                padding: 5px 10px 10px 10px;

                .answer {
                  justify-content: center;
                  margin: 0;

                  label {
                    flex: none;

                    .styledRadio {
                      margin: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .error-tracker {
        .failure_reason {
          > .body {
            form {
              @media only screen and (max-width: 1200px) {
                .btn {
                  width: 100% !important;
                  margin-left: 0 !important;
                }

                #exercise_attempt_failure_reason_id,
                #problem_attempt_failure_reason_id {
                  margin-bottom: 15px;
                  width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }

  @media screen and (min-width: 768px) {
    .left-section {
      border-right: 1px solid $grey-6;

      .content-box {
        height: calc(100vh - 270px);
        padding-right: 28px;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .right-section {
      .content-box {
        height: calc(100vh - 255px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }
  }

  @media screen and (min-width: 1400px) {
    .left-section {
      .content-box {
        height: calc(100vh - 235px);
      }
    }

    .right-section {
      .content-box {
        height: calc(100vh - 222px);
      }
    }
  }

  @media screen and (max-width: 991px) {
    .right-section {
      .content-box {
        .question-wrapper {
          padding: 25px 25px 0;
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    .left-section {
      width: 100%;
      padding: 0;

      .content-box {
        padding: 0 20px;
      }
    }

    .right-section {
      width: 100%;
      border-top: 1px solid $grey-6;

      .content-box {
        .question-wrapper {
          padding: 15px 20px 10px 20px;
        }
      }
    }
  }
}
