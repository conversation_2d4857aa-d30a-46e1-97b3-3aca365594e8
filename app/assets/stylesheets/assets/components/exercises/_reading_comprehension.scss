body {
  &.gre {
    .reading-comprehension.section-wrapper {
      .left-section {
        p {
          &:not(:last-child) {
            margin-bottom: 0;
          }

          text-indent: 20px;
        }
      }
    }
  }
}

.sentence-number {
  color: #4D4D4D;
  font-weight: 600 !important;
}

// RC styling used in /lessons/show/exercise.scss and /problem_solving/ask.scss
.reading-comprehension.section-wrapper {
  .left-section,
  .right-section {
    .content-box {
      &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: $white;
      }

      &::-webkit-scrollbar-thumb {
        background: $grey-5;
        border-radius: 20px;
      }
    }
  }

  .left-section {
    padding: 0;
    width: 53%;

    .rc-questions-range-heading {
      padding: 6px 20px;
      font-size: 14px;
    }

    .content-box {
      padding: 0 40px;
      margin: 25px 0;

      &::-webkit-scrollbar {
        width: 5px !important;
      }

      p:last-child {
        margin-bottom: 0 !important;
      }
    }
  }

  .right-section {
    padding: 0;
    width: 47%;

    .content-box {
      .section-navigation {
        .header-sub-navigation-inner {
          border-bottom: 1px solid $grey-6;

          a.tab-btn {
            align-items: center;
            display: flex;
            font-size: 12px;
            height: 60px;
            justify-content: center;
            padding: 0 10px;
            line-height: 1.33;
            max-width: 40%;
          }
        }

        .navigation-dropdown {
          border-bottom: 1px solid $grey-6;

          .dropdown {
            &.right-section-list {
              .dropdown-toggle {
                .fa-angle-up {
                  display: none;
                }

                .fa-angle-down {
                  display: inline-block;
                }

                &.show {
                  .fa-angle-up {
                    display: inline-block;
                  }

                  .fa-angle-down {
                    display: none;
                  }
                }

                &:focus {
                  box-shadow: none;
                }

                &::after {
                  display: none;
                }
              }

              .dropdown-item {
                &:active,
                &.active {
                  background-color: #E9ECEF !important;
                  color: black;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }

      .reading-comprehension-exercise {
        padding: 25px 50px 0 50px;
        overflow-x: hidden;

        .video-wrapper {
          margin-top: 0;
        }
      }

      .interrogation_part,
      .answers {
        padding: 0 !important;
      }

      .answers {
        .answer {
          margin-bottom: 20px;
        }
      }

      .error-tracker {
        .failure_reason {
          > .body {
            form {
              @media only screen and (max-width: 1200px) {
                .btn {
                  width: 100% !important;
                  margin-left: 0 !important;
                }

                #exercise_attempt_failure_reason_id,
                #problem_attempt_failure_reason_id {
                  margin-bottom: 15px;
                  width: 100%;
                }
              }
            }
          }
        }
      }

      .instructors {
        flex-wrap: wrap;
        padding-left: 19px;

        .presented-by {
          width: 100%;
          color: $dark-2;
          font-weight: 600;
          font-size: 12px !important;
        }

        .instructor-info {
          flex-wrap: wrap;
          font-weight: 600;

          img {
            margin-left: 0;
          }

          .instructor-name {
            color: $blue-1;
          }

          .instructor-title {
            color: $blue-2;
            font-weight: 600;
          }
        }

        .blue-dots {
          position: absolute;
          right: 4px;
          top: 8px;

          @media screen and (max-width: 480px) {
            top: 36px;
          }
        }
      }
    }
  }

  &.sat {
    .left-section {
      padding-right: 0;

      .passage-heading {
        background: $light-3;
        padding: 10px 27px;

        & > p {
          margin-bottom: 0;
          font-weight: normal !important;
          line-height: 1.63 !important;
          color: $grey-2 !important;
        }
      }

      .content-box {
        margin-top: 20px;
        overflow: auto;

        .passage-fit-for-sat {
          margin: 0 auto;
          width: fit-content;

          span.line-number {
            min-width: 36px;
            display: inline-block;
            color: #A253C2 !important;
            text-align: right;
            padding-right: 10px;
          }

          .line {
            margin: 0;
            font-weight: normal;
            line-height: 1.63;
            white-space: nowrap;
          }
        }
      }
    }
  }

  @media screen and (min-width: 768px) {
    .left-section {
      border-right: 1px solid transparent;

      &.type-problem {
        border-right: 1px solid $grey-6;
      }

      .content-box {
        height: calc(100vh - 270px);
        padding-right: 28px;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .right-section {
      .content-box {
        height: calc(100vh - 255px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }
  }

  @media screen and (min-width: 1400px) {
    .left-section {
      .content-box {
        height: calc(100vh - 235px);
      }
    }

    .right-section {
      .content-box {
        height: calc(100vh - 222px);
      }
    }
  }

  @media screen and (max-width: 991px) {
    .right-section {
      .content-box {
        .reading-comprehension-exercise {
          padding: 25px 25px 0;
        }

        .instructors {
          .instructor-info {
            .instructor-title {
              width: 90% !important;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    .left-section {
      width: 100%;
      padding: 0;

      .rc-questions-range-heading {
        margin-right: 0;
      }

      .content-box {
        padding: 0 20px;
      }
    }

    .right-section {
      width: 100%;

      .content-box {
        .section-navigation {
          .navigation-dropdown {
            border-top: 1px solid $grey-6;
          }
        }

        .reading-comprehension-exercise {
          padding: 15px 20px 10px 20px;
        }
      }
    }
  }

  &.multiple_answer {
    .right-section {
      .reading-comprehension-exercise {
        .multiple-answer-question-instructions {
          background: $light-1;
          padding: 7px 14px;
          line-height: 24px;
        }
      }
    }
  }

  &.select_the_sentence {
    .left-section {
      .sentence {
        &.selected {
          background-color: #BFE0EE;
        }
      }
    }
  }
}
