#summarize {
  .summarize-lesson-modal {
    background-color: $white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.3);
    text-align: center;
    bottom: 60px;
    width: 430px;
    transform: translate(-50%, 0);
    left: 50%;
    background-image: image-url("layout/components/ai_assist/summary/submenu1_bg.webp");
    background-size: cover;
    background-position: top;
    background-repeat: no-repeat;

    .generate-button {
      background-color: $blue-2;
      color: $white;
      padding: 4px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: #0056B3;
      }
    }

    h2.summarize-title {
      font-size: 18px;
      margin-bottom: 20px;
      color: $dark-2;
    }

    .options {
      .check-input {
        z-index: 9;
      }

      .card {
        padding: 12px 8px;
        border-radius: 6px;
        border: solid 1px $grey-6;
        background-color: $white;
        cursor: pointer;

        &.selected {
          border-color: #52A1D0;
          background-color: $primary-5;

          .card-title {
            color: $gmat-1;
          }
        }

        &:not(.disabled):not(.selected):hover {
          border-color: $primary-4;
          background-color: #F7FCFF;

          .card-title {
            color: $dark-1;
          }
        }

        &.disabled {
          border-color: #B0BBC5;

          .card-title {
            color: $grey-5;
          }

          &:hover {
            cursor: not-allowed;
          }
        }

        .card-title {
          color: $dark-1;
        }

        .icon-image {
          max-height: 44px;
          width: auto;
          box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
        }
      }
    }
  }
}

.summaries-navigation {
  .next-topic,
  .prev-topic {
    max-width: 170px;
  }
}

.summarize-btn-wrapper {
  border-style: solid;
  border-width: 1px;
  border-image-source: linear-gradient(to right, #B157D0, #796AE2 46%, #43B7FF);
  border-image-slice: 1;
  margin-right: 21px;

  &.disabled {
    border-image-source: linear-gradient(to right, #8E9BA7, #8E9BA7, #8E9BA7);
  }

  .summarize-btn {
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-image: linear-gradient(to right, #B157D0, #796AE2 45%, #43B7FF);
    padding: 10px 20px;
    height: 42px;
    display: flex;
    align-items: center;
    line-height: 1.5;

    &:hover {
      border: solid 1px #796AE2;
      background-color: #F9F6FF;
      color: #796AE2 !important;
      background-clip: unset;
      background-image: unset;
      -webkit-text-fill-color: unset;
    }

    &.disabled {
      background-image: linear-gradient(to right, #8E9BA7, #8E9BA7, #8E9BA7);

      &:hover {
        border: solid 1px #8E9BA7 !important;
        cursor: not-allowed;
        background-color: #F9F6FF;
        color: #8E9BA7 !important;
        background-clip: unset;
        background-image: unset;
        -webkit-text-fill-color: unset;
      }

      img {
        &.enabled {
          display: none;
        }

        &.disabled {
          display: inline;
        }
      }
    }

    &:focus {
      box-shadow: none;
    }

    img {
      max-width: 20px;

      &.enabled {
        display: inline;
      }

      &.disabled {
        display: none;
      }
    }
  }
}

.summarize-loader {
  background: linear-gradient(96deg, #AE59D1 11%, #5FBDFF 97%);
  padding: 1px;
  border-radius: 4px;
  margin-top: 35px;

  .summary-section {
    background: image-url("layout/components/ai_assist/summary/summary_loader_bg.webp"), $white;
    background-position: top;
    background-size: cover;
    padding: 22px 16px;
    background-repeat: no-repeat;
    border-radius: 4px;
    padding-bottom: 0;

    .summarize-loader-title {
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      background-image: linear-gradient(to right, #B157D0, #796AE2 45%, #6AB4E2 100%);
      width: max-content;
      margin-bottom: 0;
    }

    .nav-item {
      &::marker {
        content: none !important;
      }
    }

    .summary-box {
      height: calc(100% - 44px);

      .icon-container {
        width: 250px;
        height: auto;
      }

      .summarize-loader-title {
        margin-bottom: 129px;
        margin-top: -40px !important;
      }
    }

    .accordion-button {
      min-height: 45px;
    }
  }

  .bottom-space {
    height: 100%;
    padding-bottom: 0;
  }
}

.summarize-result-section {
  .summarize-loader {
    .summary-section {
      background-color: $white;
      background-image: unset !important;
      border-radius: 3px;
      overflow: hidden;

      .accordion-header {
        .nav-item {
          &::marker {
            display: none;
          }
        }

        .accordion-button {
          box-shadow: none !important;

          &:hover,
          &:focus {
            z-index: 0;
          }
        }
      }

      .nav-tabs {
        padding: 2px 4px;
        border-radius: 6px;
        background-color: $grey-8;
        margin-right: 40px;
        border: none;

        @media (max-width: 991px) {
          margin-right: 25px;
        }

        .nav-link {
          padding: 5px 16px;
          border: 1px solid $white;
          border-width: 0 1px 0 0;
          margin: 1px 0;
          border-radius: 0;
          color: $grey-4;

          &.active {
            border-radius: 5px;

            span {
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
              background-image: linear-gradient(to right, #B157D0, #796AE2 45%, #43B7FF);
            }

            .icon-gradient {
              background: linear-gradient(to right, #B157D0, #796AE2 45%, #43B7FF);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              color: transparent;
            }
          }

          &:last-child {
            border-right: 0;
          }
        }
      }

      .accordion-button {
        &::after {
          position: absolute;
          right: 20px;
          top: 10px;
        }
      }

      .accordion-body {
        padding: 32px 40px 15px;

        .tab-pane {
          font-size: 22px;
          line-height: 33px;
          color: $grey-2;
          font-weight: 300;
        }

        h2 {
          font-weight: 300;
          font-size: 24px;
          text-decoration: underline;
          margin-bottom: 25px;
        }

        p {
          margin-bottom: 25px;
        }

        ul {
          margin-bottom: 25px;
        }
      }
    }
  }
}

@media (max-width: 1300px) {
  #lesson-content-wrapper {
    padding-bottom: 200px !important;

    .summaries-navigation {
      padding-bottom: 60px !important;
    }
  }
}

@media (max-width: 992px) {
  #lesson-content-wrapper {
    padding-bottom: 200px !important;

    .container {
      max-width: 700px;
    }

    .summaries-navigation {
      width: calc(100% - 0px) !important;
      margin-left: -8px;
      padding-bottom: 60px !important;
      margin-top: 20px;

      .container {
        padding-left: 0;
        padding-right: 0;
      }

      .prev-topic {
        max-width: 150px;
        padding-left: 10px;
        padding-right: 10px;
        width: 100%;

        .hidden-mobile {
          display: none;
        }
      }

      .next-topic {
        max-width: 115px;
        width: 100%;

        .hidden-mobile {
          display: none;
        }
      }

      .popover-frame {
        width: 100%;

        .popover-wrapper {
          width: 100%;

          .summarize-wrap {
            justify-content: flex-end !important;
          }
        }
      }
    }
  }

  .summarize-btn-wrapper {
    max-width: 203px;
    width: 100%;

    &:nth-child(2) {
      max-width: 150px;
      width: 100%;
    }

    .summarize-btn {
      padding: 10px 8px;
      justify-content: center;
      width: 100%;
    }
  }
}

@media (max-width: 767px) {
  .summarize-btn-wrapper {
    margin-right: 0;

    &:nth-child(2) {
      margin-left: auto;
      margin-right: inherit;
    }

    .summarize-btn {
      padding: 10px 12px;
    }
  }

  .lesson-body {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  #lesson-content-wrapper {
    padding-bottom: 0 !important;

    .main-content.container {
      max-width: calc(100% - 16px);
    }

    .summaries-navigation {
      padding: 1px 8px !important;
      position: static !important;
      margin-top: 15px;
      margin-left: 0;
      padding-bottom: 60px !important;

      .container {
        padding-left: 0;
        padding-right: 0;
      }

      .prev-topic,
      .next-topic {
        display: none;
      }

      .mobile-navigation {
        .mobile-prev-next-topic {
          background-color: $blue-2 !important;
          border-color: $blue-2 !important;
        }

        .btn {
          min-width: 120px;
          margin-top: 20px;

          &:focus {
            box-shadow: none;
          }
        }
      }
    }
  }

  .summarize-result-section {
    .summarize-loader {
      .summary-section {
        .accordion-button::after {
          top: 10px;
        }

        .nav-tabs {
          padding-right: 4px;
          margin-right: 30px;

          .nav-link {
            padding: 5px 9px !important;

            .summary-text {
              display: none;
            }
          }
        }

        .accordion-body {
          padding: 32px 22px 9px;

          .tab-pane {
            font-size: 18px;
            line-height: 1.83;
          }

          h2 {
            font-size: 18px;
            margin-bottom: 18px;
          }

          p {
            margin-bottom: 18px;
          }

          ul {
            margin-bottom: 18px;
          }
        }
      }
    }
  }

  #summarize {
    .summarize-lesson-modal {
      width: 100%;
      margin: 0 auto;
      right: 0;
      transform: unset;
      left: 0;
      padding: 20px 5px;

      .options {
        .card {
          padding: 12px 4px;
        }

        .m-col {
          padding: 0 10px;
        }
      }
    }
  }

  .popover-wrapper,
  .popover-frame {
    min-width: 100%;
  }
}

@media (max-width: 400px) {
  #summarize {
    .summarize-lesson-modal {
      .options {
        .m-col {
          padding: 0 5px;

          .card {
            .card-title {
              font-size: 12px;
            }
          }

          &:first-child {
            padding-left: 5px;
          }

          &:last-child {
            padding-right: 5px;
          }
        }
      }
    }
  }

  .popover-wrapper {
    .summarize-wrap {
      .summarize-btn-wrapper {
        max-width: 183px;
        width: 100%;

        &:nth-child(2) {
          max-width: 120px;
          width: 100%;
        }
      }

      .summarize-btn {
        padding: 10px 5px !important;
        gap: 10px;

        img {
          margin-right: 0 !important;
          max-width: 15px;
        }
      }
    }
  }
}
