body[data-controller="admin/online_classes/sessions/problems"] {
  &[data-action="index"] {
    .top-header {
      padding: 19px 0 15px 0;
      margin-bottom: 25px;
      border-bottom: solid 1px $grey-6;
      display: flex;
      gap: 12px;
      align-items: center;

      .q-box {
        min-width: 28px;
        height: 28px;
        font-size: 18px;
        font-weight: 600;
        color: $white;
        background-color: $ttp-blue-color;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      h4 {
        color: $dark-1;
        font-size: 22px;
        margin: 0;

        p {
          margin: 0;
        }
      }
    }

    .class-heading {
      font-size: 20px;
      font-weight: 600;
      line-height: 1.2;
      letter-spacing: 0.2px;
      text-align: left;
      color: #007AB9;
      margin-top: 0;
      margin-bottom: 19px;

      p {
        margin: 0;
      }
    }

    .homework-details {
      padding: 16px 30px 16px 20px;
      border-radius: 2px;
      border: solid 1px $grey-6;
      background-color: $white;
      width: 100%;

      .main-homework {
        gap: 20px;
        justify-content: space-between;
        align-items: center;

        .homework {
          width: 70%;

          .name {
            color: $grey-1;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
          }

          p {
            color: $grey-2;
            font-size: 16px;
            margin-bottom: 11px;
          }

          .released-txt {
            color: #007AB9;
            font-weight: 600;
          }

          p.title {
            color: $red-1;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 0;
          }
        }

        .ready-btn {
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          h5 {
            font-size: 13px;
            font-weight: 600;
            letter-spacing: 0.13px;
            text-align: center;
            color: $grey-4;
            text-transform: uppercase;
            margin-bottom: 15px;
          }

          .icon-red {
            box-shadow: 0 1px 7px 0 rgba(0, 0, 0, 0.25);
            border: solid 2px $white;
            background-color: $red-1;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;

            &.green {
              background-color: $gre-2;
            }
          }
        }

        .show-homework-link {
          .btn {
            padding-left: 20px;
            padding-right: 20px;
          }
        }
      }
    }

    .download-schedule-btn {
      .add-another-problem-btn {
        width: 180px;
        height: 44px;
      }
    }

    .scrollable-table-container.homework-table {
      .empty-list {
        background-color: transparent;

        td {
          height: 500px;
          vertical-align: top;
          padding-top: 70px;

          img {
            width: 119.3px;
            height: 69.9px;
          }

          p {
            margin-top: 20px;
            font-size: 18px;
            line-height: 1.33;
            color: #4D4D4D;
          }
        }
      }
    }

    .ttp-table {
      th.hide-date {
        visibility: hidden;
      }

      th:last-child {
        width: 221px;
        min-width: 221px;
      }
    }

    tbody {
      td.actions {
        i.disabled {
          border: none;
          cursor: not-allowed;
          color: $grey-5;
        }
      }

      td.task-type {
        color: $grey-1;
      }

      td.text-start {
        max-width: 317px;

        .text-count {
          color: $grey-1;
        }
      }
    }

    .empty-task-list {
      tbody {
        td {
          border: 0 !important;
        }
      }
    }

    .problem-level {
      display: inline-block;
      padding: 2px 8px;
      font-size: 13px;
      font-weight: 500;
      line-height: 16px;
      border: 1px solid;
      border-radius: 11px;

      &.easy {
        color: $ttp-green-color;
        border-color: $ttp-green-color;
      }

      &.medium {
        color: $ttp-yellow-color;
        border-color: $ttp-yellow-color;
      }

      &.hard {
        color: $ttp-red-color;
        border-color: $ttp-red-color;
      }

      &.challenge {
        color: $red-1;
        border-color: $red-1;
      }
    }

    .schedule-time-detail {
      font-size: 28px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: normal;
      text-align: left;
      color: $blue-2;
    }

    .problems-include {
      font-weight: 600;
      font-size: 18px;
    }

    #add-homework-task-modal {
      .modal-header {
        padding: 20px 32px 16px;
        margin: 0;
        background-color: $ttp-gray-dark;

        .modal-title {
          color: $light-5;
        }

        .btn-close {
          color: $light-5;
          font-size: 25px;
          background: none;
        }
      }

      .modal-task-type {
        padding: 24px 32px;

        select {
          background: asset_url("admin/controllers/online_classes/chevron_down.svg") no-repeat 100% 50%;
          -moz-appearance: none;
          -webkit-appearance: none;
          appearance: none;
          background-size: 37px 27px;
        }

        h6.task {
          color: $grey-2;
        }

        .task-type-select-id {
          padding: 12px 15px 12px 15px;
          border-radius: 2px;
          border: solid 1px $grey-6;
          color: $grey-1;

          &.form-select {
            background-color: $white !important;
          }
        }
      }

      .modal-navigation {
        ul {
          &.nav-pills {
            border-bottom: 1px solid $grey-6;
            padding-left: 10px;

            .nav-item {
              flex-direction: row;
              justify-content: flex-end;
              display: flex;
              padding: 5px 0 0 0;
              background-color: $white;

              .nav-link {
                flex-grow: 0;
                margin: auto 0;
                line-height: 1.33;
                color: $grey-4;
                cursor: pointer;
              }

              :hover {
                color: $ttp-gray-dark;
                font-weight: 600;
              }

              .active {
                font-weight: 600;
                line-height: 1.33;
                color: $dark-1;
                text-align: center;
                background-color: $white;

                &::after {
                  position: relative;
                  top: 9px;
                  display: block;
                  border-bottom: 2px solid $blue-2;
                  content: "";
                }
              }
            }
          }
        }
      }

      .modal-body {
        padding: 24px 32px;

        .row {
          .lessons-select-field {
            width: 100%;
          }
        }

        .chapters-select-field {
          display: flex;

          select {
            background: asset_url("/assets/admin/controllers/online_classes/chevron_down.svg") no-repeat 100% 50%;
            -moz-appearance: none;
            -webkit-appearance: none;
            appearance: none;
            background-size: 28px 20px;
          }

          .input-group {
            margin-bottom: 0 !important;
            flex: 1 65%;
          }

          .form-select {
            margin-right: 10px;
            flex: 1 35%;
            padding-right: 40px !important;
            background-color: $white !important;
          }

          .form-select,
          .form-control,
          .button-search {
            padding: 11px 8px 12px 10px;
            border-radius: 2px;
            border: solid 1px $grey-6;
            color: $grey-3;
            height: 45px;

            &:focus {
              box-shadow: none !important;
            }
          }
        }

        .table-content {
          margin-top: 30px;
          border: solid 1px $grey-6;
          border-radius: 2px;

          .empty-results {
            flex-direction: column;
            flex-wrap: nowrap;
            align-content: center;
            justify-content: center;
            align-items: center;
            row-gap: 20px;
            display: flex;
            height: 300px;

            .empty-result-message {
              font-size: 16px;
              line-height: 1.33;
              color: $grey-3;
              text-align: center;
            }
          }

          .table-results {
            overflow-x: hidden;

            .diagnostic-problems-table {
              margin-bottom: 0;
            }

            table tr td {
              color: #6D6D6D;
              padding: 6px 10px 0 23px;

              .detail-icon {
                color: $blue-3;
              }

              &:last-child {
                vertical-align: middle;
              }

              &:hover {
                cursor: pointer;
              }

              &:not(:last-child) {
                min-width: 95px;
                width: calc(90%) !important;
              }

              &:first-child a {
                color: #6D6D6D;
                cursor: pointer;

                > p {
                  margin-bottom: 0;
                  font-size: 14px;
                }
              }

              img {
                max-width: 100%;
                height: auto !important;
              }

              .problem-content {
                overflow-x: auto;
                font-size: 14px;
                color: $grey-2;
                margin: 29px 17px 27px 0;
              }
            }

            .problem-checkbox-field {
              display: none;
            }

            .problem-checkbox-field + label {
              min-width: 20px;
            }

            .problem-checkbox-field:checked + label::before {
              color: $gre-2;
              font-size: 20px;
              content: "\f058";
              padding-top: 6px;
            }

            thead,
            tbody { display: block; }

            tbody {
              height: 300px;
              overflow-y: auto;
              overflow-x: hidden;

              .problem-description {
                &.checked {
                  background-color: $blue-5;
                }
              }
            }

            .problem-level {
              display: inline-block;
              padding: 2px 8px;
              font-size: 13px;
              font-weight: 500;
              line-height: 16px;
              border: 1px solid;
              border-radius: 11px;

              &.easy {
                color: $ttp-green-color;
                border-color: $ttp-green-color;
              }

              &.medium {
                color: $ttp-yellow-color;
                border-color: $ttp-yellow-color;
              }

              &.hard {
                color: $ttp-red-color;
                border-color: $ttp-red-color;
              }

              &.challenge {
                color: $red-1;
                border-color: $red-1;
              }
            }
          }
        }
      }

      .modal-footer {
        border-top: none;
        padding: 0 46px 20px 32px;

        .selected-count {
          flex: auto;
          font-size: 18px;
          color: $grey-2;
          text-align: left;

          .problems-found {
            font-weight: bold;
          }

          .selected-count-label {
            color: $grey-2;
          }

          .selected-count-number {
            color: $blue-2;
          }
        }

        .close-modal-button {
          font-size: 14px;
          width: 136px;
          height: 44px;
        }

        .btn-save-problem {
          font-size: 14px;
          width: 156px;
          height: 44px;
        }
      }
    }

    @media (max-width: 1024px) {
      .homework-details {
        .main-homework {
          .homework {
            width: 60%;
          }
        }
      }
    }

    @media (max-width: 991px) {
      tbody {
        td.actions {
          padding: 8px;
        }
      }

      #add-homework-task-modal {
        .modal-dialog {
          max-width: 85%;
          margin: 0 auto;

          .modal-body {
            .table-content {
              .table-results {
                table tr td {
                  .problem-content {
                    width: 220px;
                  }
                }
              }
            }
          }
        }
      }
    }

    @media (max-width: 767px) {
      .homework-details {
        .main-homework {
          flex-wrap: wrap;
          gap: 5px;

          .homework {
            width: 100%;
            margin-bottom: 18px;
          }

          .show-homework-link {
            margin-left: auto;

            .btn {
              padding-left: 10px;
              padding-right: 10px;
            }
          }
        }
      }

      #add-homework-task-modal {
        .modal-dialog {
          .modal-header,
          .modal-task-type {
            padding: 24px 15px;
          }

          .modal-body {
            padding: 24px 15px;

            .chapters-select-field {
              display: inline;

              .form-select,
              .form-control,
              .button-search {
                margin-bottom: 10px;
                background-color: $white;
                color: $blue-2;
              }
            }

            .table-content {
              margin-top: 15px;

              .table-results {
                table tr td {
                  &:first-child {
                    max-width: 50%;
                  }

                  &:not(:last-child) {
                    max-width: 100px !important;
                    min-width: unset !important;
                  }

                  .problem-content {
                    padding: 0;
                    width: auto;
                  }
                }
              }
            }
          }

          .modal-footer {
            padding: 24px 15px;
            flex-direction: column;

            .selected-count {
              width: 100%;
            }

            .btn {
              width: 100%;
              margin-left: 0;
            }

            .close-modal-button {
              order: 2;
            }

            .btn-save-problem {
              order: 1;
            }
          }
        }
      }
    }

    @media (max-width: 480px) {
      #add-homework-task-modal {
        .modal-dialog {
          .modal-body {
            .table-content {
              .table-results {
                table tr td {
                  .problem-content {
                    width: 150px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
