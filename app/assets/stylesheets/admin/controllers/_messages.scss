body[data-controller="admin/messages"] {
  .btn-custom {
    font-size: 14px;
    width: 100%;
    margin: 10px 0;
  }

  .width-custom {
    min-width: 185px;
  }

  .score {
    font-size: 16px;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.33;
    letter-spacing: normal;
    text-align: left;

    &.unhelpful {
      color: $ttp-red-color;
    }

    &.helpful {
      color: #57C292;
    }
  }

  .status {
    border: 1px solid;
    padding: 2px 8px;
    border-radius: 11px;
    display: inline-block;
    font-size: 13px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;

    &.unresolved {
      color: $ttp-red-color;
      border-color: $ttp-red-color;
    }

    &.resolved {
      color: $ttp-green-color;
      border-color: $ttp-green-color;
    }
  }

  .max-width-custom {
    width: 215px;
    min-width: 215px;
  }

  &[data-action="user_feedback"] {
    .user-full-name {
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.33;
      letter-spacing: normal;
      text-align: left;
      color: $ttp-blue-color;

      i {
        color: $red-1;
      }
    }

    .user-email {
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.33;
      letter-spacing: normal;
      text-align: left;
      color: $grey-3;
    }

    .subtitle {
      font-size: 13px;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.33;
      letter-spacing: normal;
      text-align: left;
      color: $grey-2;
    }

    .date {
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.33;
      letter-spacing: normal;
      text-align: left;
      color: $grey-1;
    }

    .feedback-date {
      margin: 8px 0;
    }

    .feedback-score {
      margin: 8px 0;
    }

    .chapter {
      font-size: 14px;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.33;
      letter-spacing: normal;
      text-align: left;
      color: $grey-1;
    }

    .lesson {
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.33;
      letter-spacing: normal;
      text-align: left;
      color: $grey-3;
    }

    .vertical-separator {
      height: 1px;
      flex-grow: 0;
      margin: 11.5px 0 19.5px;
      background-color: $grey-7;
    }

    .message {
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.5;
      letter-spacing: normal;
      text-align: left;
      color: $grey-2;
    }

    .resolved-by {
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.33;
      letter-spacing: normal;
      text-align: center;
      color: $grey-1;
    }

    .resolved-at {
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.33;
      letter-spacing: normal;
      text-align: center;
      color: $grey-3;
    }

    .no-content {
      padding: 100px;

      .icon {
        text-align: center;
        margin-bottom: 30px;
      }

      .title {
        font-size: 22px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.36;
        letter-spacing: normal;
        text-align: center;
        color: $ttp-gray-dark;
        margin-bottom: 10px;
      }

      .subtitle {
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.33;
        letter-spacing: normal;
        text-align: center;
        color: $grey-3;
      }
    }
  }

  &[data-action="reply"] {
    .white-box {
      background-color: #FFFFFF;
      border: solid 1px $grey-6;
      margin-top: 24px;

      .details-container {
        padding: 22px 28px;

        .user-fullname {
          font-size: 18px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.44;
          letter-spacing: 0.18px;
          text-align: left;
          color: $blue-2;
        }

        .user-email {
          font-size: 16px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: normal;
          text-align: left;
          color: $grey-3;
        }

        .field-label {
          font-size: 13px;
          font-weight: 600;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: normal;
          text-align: left;
          color: $grey-2;
          margin-bottom: 8px;
        }

        .date-value {
          font-size: 16px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: normal;
          text-align: left;
          color: $grey-3;
        }
      }

      .lesson-info-container {
        padding: 22px 28px;
        border-top: 1px solid $grey-6;

        .from {
          font-size: 13px;
          font-weight: 600;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: normal;
          text-align: left;
          color: $grey-2;
        }

        .chapter {
          margin-left: 14px;
          font-size: 16px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: normal;
          text-align: left;
          color: $grey-3;
        }

        .lesson {
          margin-left: 14px;
          font-size: 16px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: normal;
          text-align: left;
          color: $blue-3;
        }
      }
    }

    .chat-box {
      position: relative;
      min-height: 480px;
      padding: 10px 32px 100px 32px;
      background-color: $light-2;
      border: 1px solid $grey-6;
      border-top: none;

      .message-group {
        display: flex;
        flex-direction: row;
        margin-top: 20px;

        .img-container {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8px 10px;
          width: 42px;
          height: 42px;
          border-radius: 50%;
          background-color: $blue-5;
          border: solid 1px $blue-4;
          box-shadow: 0 6px 11px 0 rgba(188, 200, 214, 0.25);
          font-size: 18px;
          font-weight: 600;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.33;
          letter-spacing: 0.18px;
          text-align: left;
          color: $blue-2;
        }

        .message-container {
          margin-left: 16px;
          max-width: 75%;

          .message-content {
            background-color: #FFFFFF;
            border: solid 1px $grey-6;
            padding: 16px 20px;
            font-size: 16px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: normal;
            text-align: left;
            color: $grey-2;
            border-radius: 8px;
            word-wrap: break-word;
          }

          .message-sender {
            margin-top: 12px;
            font-size: 16px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: normal;
            text-align: right;
            color: $grey-2;
          }

          .message-date {
            margin-top: 2px;
            font-size: 16px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: normal;
            text-align: left;
            color: $grey-3;
          }

          .custom-margin-top {
            margin-top: 12px;
          }
        }

        &.admin-sender {
          flex-direction: row-reverse;

          .message-container {
            margin-right: 16px;

            .message-content {
              text-align: right;
              background-color: $grey-7;
            }

            .message-date {
              text-align: right;
            }
          }
        }
      }

      #conversation {
        overflow-y: scroll;
        height: 430px;
      }

      #send-message {
        position: absolute;
        bottom: 0;
        left: 0;
        margin: 0 32px 32px;
        width: calc(100% - 64px);
        display: flex;
        flex-direction: row;
        background-color: #FFFFFF;
        box-shadow: 0 4px 26px 7px rgba(188, 200, 214, 0.15);
        border: solid 1px $grey-6;
        padding: 10px;

        textarea {
          resize: none;
          width: 100%;
          border: none;
        }

        &.disabled {
          background-color: #E9ECEF;
        }

        .form-control {
          &:focus {
            border: none;
            box-shadow: none;
          }
        }

        input[type="submit"] {
          width: 220px;
        }
      }
    }

    @media only screen and (min-width: "768px") {
      .send-reply-button {
        min-width: 206px;
      }
    }

    @media only screen and (max-width: "768px") {
      .white-box {
        .details-container {
          .field-label {
            margin-top: 15px;
            margin-bottom: 0;
          }
        }
      }

      .chat-box {
        #send-message {
          input[type="text"] {
            &::placeholder {
              font-size: 12px;
            }
          }

          input[type="submit"] {
            width: 100%;
          }

          .send-reply-button {
            min-width: 130px;

            span {
              display: none;
            }

            &::after {
              content: 'SEND REPLY';
            }
          }
        }
      }
    }
  }
}
