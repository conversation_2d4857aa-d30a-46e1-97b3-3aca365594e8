/* global bootstrap */

function close_alerts () {
  $('#messages').addClass('active');

  setTimeout(function () {
    $('#messages').removeClass('active');
  }, 4000);

  $('#messages a.close').click(function (event) {
    event.preventDefault();
    $(this).closest('#messages').removeClass('active');
  });
}

function custom_message (type, message) { // eslint-disable-line no-unused-vars
  $('#messages').html(
    `<div class='alert alert-${type}'>` +
      "<a class='close' href='#' data-dismiss='alert'>×</a>" +
      `<ul class='alert-list'>${message}</ul>` +
    '</div>'
  );

  close_alerts();
}

function disable_submit_input (element) { // eslint-disable-line no-unused-vars
  if (element.hasClass('disabled') == false) {
    element.prop('disabled', true);
    element.attr('re-enable-with', element.val());
    element.val('Please wait...');
    element.addClass('disabled');
  }
}

function re_enable_submit_inputs () { // eslint-disable-line no-unused-vars
  $('[re-enable-with]').each(function () {
    $(this).removeAttr('data-disable-with');
    $(this).removeAttr('disabled');
    $(this).removeClass('disabled');
    $(this).val($(this).attr('re-enable-with'));
    $(this).removeAttr('re-enable-with');
  });
}

function initialize_datepickers () {
  var $future_datepickers = $('.datepicker:not(.past)');
  var $past_datepickers = $('.datepicker.past');

  if ($future_datepickers.length) {
    $future_datepickers.pickadate({
      format: 'mmmm dd, yyyy',
      formatSubmit: 'yyyy-mm-dd',
      hiddenName: true,
      min: new Date(`${$('body').data('user-current-date')}T00:00:00`)
    });
  }

  if ($past_datepickers.length) {
    $past_datepickers.pickadate({
      format: 'mm/dd/yyyy',
      formatSubmit: 'yyyy-mm-dd',
      hiddenName: true,
      max: new Date(`${$('body').data('user-current-date')}T00:00:00`)
    });
  }
}

function initialize_tooltips () {
  $('[data-bs-toggle="tooltip"]').each(function () {
    new bootstrap.Tooltip($(this)[0]);
  });

  $(document).on('click', function (event) {
    if (window.matchMedia('(max-width: 991px)').matches && (!$(event.target).closest('.gmat-focus-tag').length || !$(event.target).closest('.greeting-intro-tag').length)) {
      if (!$(event.target).closest('[data-bs-toggle="tooltip"]').length) {
        $('.tooltip').hide();
      }
    }
  });

  $('[data-bs-toggle="tooltip"]').on('click', function (event) {
    if (($(this).hasClass('gmat-focus-tag') || $(this).hasClass('greeting-intro-tag')) && window.matchMedia('(max-width: 991px)').matches) {
      $(this).tooltip('show');
      event.stopPropagation();
    } else {
      $(this).tooltip('hide');
    }
  });
}

// eslint-disable-next-line no-unused-vars
function desactivate_non_numeric_keys (event) {
  if (!((event.keyCode > 95 && event.keyCode < 106) ||
    (event.keyCode > 47 && event.keyCode < 58) ||
    event.keyCode == 8)) {
    return false;
  }
}

// eslint-disable-next-line no-unused-vars
function user_agent_is_mobile () {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// eslint-disable-next-line no-unused-vars
function is_blank (str) {
  return !str.replace(/^\s+/g, '').length;
}

// eslint-disable-next-line no-unused-vars
function is_integer_or_float (str) {
  var regex = /^\-?\d+(?:\.\d+)?$/;
  return regex.test(str);
}

// eslint-disable-next-line no-unused-vars
function is_integer (str) {
  var regex = /^\-?\d+$/;
  return regex.test(str);
}

function remove_readonly ($element) {
  $element.removeAttr('readonly');
}

// eslint-disable-next-line no-unused-vars
function replace_history_and_bind_back (url) {
  window.history.pushState({ id: Date.now() }, '', url);
}

function trim_attributes (node, allowed_attributes) {
  $.each(node.attributes, function () {
    var attribute_name = this.name;

    if ($.inArray(attribute_name, allowed_attributes) == -1) {
      $(node).removeAttr(attribute_name);
    }
  });
}

// eslint-disable-next-line no-unused-vars
function sanitize_html (html, whitelist) {
  whitelist = whitelist || { strong: [], b: [], i: [], br: [] };
  var output = $('<div>' + html + '</div>');

  output.find('*').each(function () {
    var allowed_attributes = whitelist[this.nodeName.toLowerCase()];

    if (!allowed_attributes) {
      $(this).remove();
    } else {
      trim_attributes(this, allowed_attributes);
    }
  });
  return output.html();
}

// eslint-disable-next-line no-unused-vars
function insert_content_on_radio_button_label_subscription ($content) {
  $content.each(function () {
    var content = $(this).find('div.content-wrapped');
    $(this).find('input').data('label-content-checked', content.html());
    $(this).find('input').data('label-content-unchecked', content.html());
    $(this).find('label').append(content);
    $(this).addClass('loaded');
  });
}

function filter_by_attribute ($button, selector, attribute) {
  var value = $button.data(attribute);

  $button.closest('.attribute-filters').find('a').removeClass('active');
  $button.addClass('active');

  $(selector).addClass('hidden');
  $(selector + '[data-' + attribute + '="' + value + '"]').removeClass('hidden');

  if ($button.data('filter-success') != undefined) {
    window[$button.data('filter-success')]();
  }
}

// eslint-disable-next-line no-unused-vars
function table_responsive ($selector, is_remove_pre_styling) {
  $selector.each(function () {
    if (!$(this).parent().hasClass('table-responsive')) {
      $(this).wrapAll('<div class="table-responsive"></div>');

      if (is_remove_pre_styling) {
        $(this).find('td, td strong, p, th, th > div').css('font-size', '');
        $(this).find('td, th, th > div').css('padding', '');
      }
    }
  });
}

function auto_scroll_to_element () {
  var $element = $('.auto-scroll-to-here').first();

  if ($element.length > 0) {
    $('html, body').animate({ scrollTop: $element.offset().top - 85 }, 600);
  } else if (window.location.hash.indexOf('#section-') == 0) {
    $element = $(window.location.hash.replace('#section-', '#'));
    $('html, body').animate({ scrollTop: $element.offset().top - 85 }, 600);
  }
}

function auto_trigger_modals () {
  var $element = $('.modal.auto-trigger').first();

  if ($element.length > 0) {
    $element.modal('show');
  } else if (window.location.hash.indexOf('#modal-') == 0) {
    var element_id = window.location.hash.replace('#modal-', '');
    $element = $('.modal[data-autotrigger-id="' + element_id + '"]').first();

    if ($element.length > 0) {
      $element.modal('show');
    }
  }
}

// eslint-disable-next-line no-unused-vars
function current_browser () {
  var test = function (regexp) { return regexp.test(window.navigator.userAgent); };
  switch (true) {
    case test(/edg/i): return 'Microsoft Edge';
    case test(/trident/i): return 'Microsoft Internet Explorer';
    case test(/firefox|fxios/i): return 'Mozilla Firefox';
    case test(/opr\//i): return 'Opera';
    case test(/ucbrowser/i): return 'UC Browser';
    case test(/samsungbrowser/i): return 'Samsung Browser';
    case test(/chrome|chromium|crios/i): return 'Google Chrome';
    case test(/safari/i): return 'Safari';
    default: return 'Other';
  }
}

// eslint-disable-next-line no-unused-vars
function submit_invisible_recaptcha_form (token) {
  $('#contact-form').trigger('submit');
}

// eslint-disable-next-line no-unused-vars
function scroll_to_element ($element, position = -80, duration = 300) {
  $('html, body').animate({ scrollTop: $element.offset().top + position }, duration);
}

function calculate_percentage (completed_count, total_count) {
  var progress = Math.round(completed_count / total_count * 100);
  return Number.isNaN(progress) ? 0 : progress;
}

function update_progress_bar ($progress_bar, progress) {
  $progress_bar.css('width', progress + '%');
}

$(document).on('hidden.bs.modal', '#video-player-modal', function () {
  $('#video-player-modal .video-wrapper').html('');
});

$(document).on('click', 'input.js-act-as-radio-button', function () {
  $('input.js-act-as-radio-button[data-radio-group="' + $(this).data('radio-group') + '"]:not(#' + $(this).attr('id') + ')').prop('checked', false).change();
  $(this).prop('checked', true).change();
});

$(document).on('click', '[data-bs-toggle="modal"][data-bs-target="#select-time-per-question"]', function () {
  var $dialog = $('#select-time-per-question .modal-dialog');

  if ($.inArray($(this).data('level'), ['hard', 'challenge']) != -1 && $.inArray($(this).data('subsection'), ['critical reasoning', 'sentence correction']) != -1) {
    $dialog.removeClass('modal-sm').addClass('modal-lg subsection-warning');
  } else {
    $dialog.removeClass('modal-lg subsection-warning').addClass('modal-sm');
  }

  if ($(this).data('form-id')) {
    $('a#time-per-question-modal-button').data('form-id', $(this).data('form-id'));
    $('a#time-per-question-modal-button').attr('href', '');
  } else {
    $('a#time-per-question-modal-button').data('form-id', '');
    $('a#time-per-question-modal-button').attr('href', $(this).attr('href'));
  }
});

$(document).on('click', '[data-bs-toggle="modal"][data-bs-target="#select-time-per-question-diagnostic"]', function () {
  $('a#time-per-question-diagnostic-modal-button').data('form-id', $(this).data('form-id'));
  $('a#time-per-question-diagnostic-modal-button').attr('href', '');
});

$(document).on('click', 'a#time-per-question-modal-button:not(.disabled)', function (event) {
  event.preventDefault();
  var time = $(this).closest('.modal').find('select:visible option:selected').val();

  if ($(this).data('form-id')) {
    var $form = $(`#${$(this).data('form-id')}`);

    $form.find('input[name="time_per_question"]').val(time);
    $form.submit();
  } else {
    var data_url = $(this).attr('href');
    var symbol = data_url.includes('?') ? '&' : '?';

    $(this).attr('href', data_url + symbol + 'time_per_question=' + time);
    window.location = $(this).attr('href');
  }

  $(this).closest('.modal').modal('hide');
  $('#loading-overlay').show();
});

$(document).on('click', 'a#time-per-question-diagnostic-modal-button:not(.disabled)', function (event) {
  event.preventDefault();
  $('#loading-overlay').show();

  var time = $(this).closest('.modal').find('select:visible option:selected').val();
  var test_order = $(this).closest('.modal').find('select#test_order').val();
  var $form = $('#' + $(this).data('form-id'));

  $form.find('input[name="time_per_question"]').val(time);

  if ($(this).closest('.modal').find('select#test_order').length) {
    $form.find('input[name="test_order"]').val(test_order);
  }

  $form.submit();

  if ($.fn.modal) {
    $(this).closest('.modal').modal('hide');
  }
});

$(document).on('click', '#score-bar button.score', function () {
  $('#score-bar button.score').removeClass('active');
  $(this).addClass('active');
  $('#user_track_id, #guest_request_track_id').val($(this).data('track-id'));
});

$(document).on('click', '.attribute-filters.general-filter a', function (event) {
  event.preventDefault();
  var target_set = $(this).closest('.general-filter').data('container') + ' .filterable';

  if ($(this).data('filter') == 'all') {
    $(target_set).removeClass('hidden');
  } else {
    filter_by_attribute($(this), target_set, 'filter');
  }
});

$(document).on('change', 'select.attribute-filters', function (event) {
  event.preventDefault();
  var target_set = $(this).data('target-selector');
  var $option = $(this).find('option:selected');
  var attribute = $option.data('attribute');
  var value = $option.data('value');

  if (attribute == 'all') {
    $(target_set).removeClass('hidden');
  } else {
    $(target_set).addClass('hidden');
    $(target_set + '[' + attribute + '="' + value + '"]').removeClass('hidden');
  }

  if ($(this).data('filter-success') != undefined) {
    window[$(this).data('filter-success')]();
  }
});

$(document).on('change', 'select.js-add-class-when-option-selected', function () {
  $(this).removeClass('option-is-selected');

  if ($(this).val() !== '') {
    $(this).addClass('option-is-selected');
  }
});

$(document).on('click, click.rails', 'a.show-overlay, button.show-overlay', function () {
  if (!$(this).attr('data-confirm')) {
    $('#loading-overlay').show();
  }
});

$(document).on('submit', 'form', function () {
  if ($(this).hasClass('show-overlay')) {
    $('#loading-overlay').show();
  }
});

$(document).on('click', 'a.submit-current-form', function (event) {
  event.preventDefault();
  $(this).closest('form').submit();
});

$(document).on('click', 'a.submit-form-by-id', function (event) {
  event.preventDefault();
  var form_selector = 'form#' + $(this).data('form-id');
  $(form_selector).submit();
});

$(document).on('click', '.update-form-field-value', function (event) {
  event.preventDefault();

  var form_id = $(this).data('form-id');
  var field_name = $(this).data('field-name');
  $('form#' + form_id + ' [name="' + field_name + '"]').val($(this).data('value'));
  $('.update-form-field-value[data-form-id="' + form_id + '"][data-field-name="' + field_name + '"]').removeClass('active');
  $(this).addClass('active');
});

$(document).on('click', '.banner-close', function (event) {
  event.preventDefault();
  $(this).closest('.top-banner').remove();
  $('.top-banner-padding').remove();
});

$(document).on('click', 'a.js-scroll-to-element', function (event) {
  var $element = $('body').find('#' + $(this).data('element-id'));

  if ($element.length > 0) {
    event.preventDefault();
    $('html, body').animate({ scrollTop: $(this).closest('body').find('#' + $(this).data('element-id')).offset().top + -150 }, 800);
    $element.closest('section').find('.animated-title').delay(600).animate({ zoom: '130%' }, 'slow').animate({ zoom: '100%' }, 'slow');
  }
});

$(window).on('scroll', function () {
  $('[data-bs-toggle="tooltip"]:focus').blur();
});

$('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (event) {
  history.pushState({}, '', event.target.hash.replace('#', '#tab-'));
});

$(document).ready(function () {
  var link_href = document.location.hash.replace('tab-', '');
  $('a[data-bs-toggle="tab"][href="' + link_href + '"]').tab('show');
});

$(document).ready(function () {
  var $element = $('.trigger-click-on-ready');

  if ($element.length && $element.is(':visible')) {
    $element.click();
  }
});

$(document).ready(function () {
  remove_readonly($('.remove-readonly-on-ready'));
});

$(document).ready(function () {
  close_alerts();
  initialize_tooltips();
  initialize_datepickers();
  auto_scroll_to_element();
  auto_trigger_modals();
});

$(document).ready(function () {
  if ($('.js-ga4-script').length) {
    $.getScript('/users/load_ga4_data');
  }
});

$(document).on('click', '.js-expand-all-accordions', function (event) {
  event.preventDefault();
  $('.accordion-collapse.collapse').collapse('show');
});

$(document).on('click', '.js-contract-all-accordions', function (event) {
  event.preventDefault();
  $('.accordion-collapse.collapse').collapse('hide');
});

$(document).ready(function () {
  var $loading_overlay = $('#loading-overlay');

  if ($loading_overlay.length) {
    $loading_overlay.hide();
  }
});

$(document).ready(function () {
  var navbar = $('#user-navbar');

  if (navbar.length > 0 && navbar.data('fetch-message-url') != undefined) {
    $.getScript(navbar.data('fetch-message-url'));
  }
});

$(document).on('hidden.bs.modal', '.message-modal, .feedback-conversation-modal', function () {
  var message_id = $(this).data('message-id');

  if (!message_id) {
    return;
  }

  if ($('#user-navbar .nav-link .profile.active').length > 0) {
    $.post({ url: $(this).data('mark-as-read-url'), dataType: 'script', data: { message_id: message_id } });
  }
});

$(document).on('show.bs.modal hidden.bs.modal', '#confirmation-modal, .message-modal, .feedback-conversation-modal', function (event) {
  if (event.type == 'show' && $('#notification-message-modal').hasClass('show')) {
    $('#notification-message-modal').modal('hide');
    $('#notification-message-modal').addClass('confirmation-closed');
  } else if (event.type == 'hidden' && $('#notification-message-modal').hasClass('confirmation-closed')) {
    $('#notification-message-modal').removeClass('confirmation-closed');
    $('#notification-message-modal').modal('show');
  } else if ($('#notification-message-modal').hasClass('message-view-more-closed')) {
    $('#notification-message-modal').removeClass('message-view-more-closed');
    $('#notification-message-modal').modal('show');
  }
});

$(document).on('click', 'a.view-more-message-modal', function () {
  $('#notification-message-modal').addClass('message-view-more-closed');
});

$(document).ready(function () {
  $('form.submit-on-ready').each(function () {
    $(this).submit();
  });
});
