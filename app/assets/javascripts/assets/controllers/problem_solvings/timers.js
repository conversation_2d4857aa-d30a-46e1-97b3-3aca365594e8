/* exported load_base_timer_values, start_timer, pause_timer, resume_evaluation */
/* global is_problem_previously_answered */

function seconds_since_to_minutes_and_seconds (seconds_since) {
  seconds_since = Math.abs(seconds_since);
  var minutes = parseInt(seconds_since / 60, 10);
  var seconds = parseInt(seconds_since % 60, 10);
  if (seconds < 10) {
    seconds = '0' + seconds;
  }
  if (minutes < 10) {
    minutes = '0' + minutes;
  }

  return minutes + ':' + seconds;
}

function current_evaluation_et () {
  return seconds_since_to_minutes_and_seconds($.estimated_evaluation_finish_time - $.current_evaluation_et);
}

function current_problem_et () {
  return seconds_since_to_minutes_and_seconds($.time_per_question - $.current_problem_et);
}

function pause_timer (call_server) {
  $.timer_running = false;

  if (call_server) {
    $.getScript('/evaluation/' + $.evaluation_attempt_id + '/problem/' + $.problem_attempt_id + '/pause');
  }
}

function update_timers () {
  var evaluation_et = current_evaluation_et();
  var problem_e_t = current_problem_et();

  $('.test-time .time').html(evaluation_et);
  $('.question-time .time').html(problem_e_t);

  // Mark in over time
  var $section = $('section#problem-solving');
  var evaluation_over_time = $.current_evaluation_et > $.estimated_evaluation_finish_time;

  if (evaluation_over_time && !$section.data('adaptive-evaluation')) {
    $('.timers .test-time').addClass('out-of-time');
  }

  if (evaluation_over_time && $section.data('adaptive-evaluation')) {
    var mark_current_section_as_timed_out_path = $section.data('mark-current-section-as-timed-out-path');
    $.timer_running = false;

    $('#loading-overlay').show();
    $('.test-time .time').html('00:00');

    $.ajax({
      url: mark_current_section_as_timed_out_path,
      type: 'POST',
      dataType: 'json'
    }).then(function (mark_current_section_response) {
      var checkStatusInterval = setInterval(function () {
        $.ajax({
          url: mark_current_section_response.background_task_url,
          type: 'GET',
          dataType: 'json'
        }).then(function (background_task_response) {
          if (!$('#loading-overlay').is(':visible')) {
            $('#loading-overlay').show();
          };

          if (background_task_response.status === 'finished') {
            clearInterval(checkStatusInterval);
            window.location.href = background_task_response.redirect_url;
          }
        });
      }, 5000);
    });
  }

  if ($.current_problem_et > $.time_per_question) {
    $('.question-time').addClass('out-of-time');
  }
}

function load_base_timer_values () {
  var $section = $('section#problem-solving');

  $.seconds_since_last_paused = $section.data('seconds-since-last-paused');

  $.current_evaluation_et = $section.data('evaluation-et') - $.seconds_since_last_paused;
  $.current_problem_et = $section.data('problem-et');

  $.estimated_evaluation_finish_time = $section.data('estimated-finish-time');
  $.problem_attempt_id = $section.data('problem-attempt-id');
  $.evaluation_attempt_id = $section.data('evaluation-attempt-id');

  $.time_per_question = parseInt($section.data('time-per-question'), 10);
}

function increment_ets () {
  $.increment_timeout = setTimeout(function () {
    increment_ets();
  }, 1000);

  if ($.timer_running) {
    $.current_evaluation_et += 1;
    $.current_problem_et += 1;

    update_timers();
  }
}

function resume_timer (call_server) {
  $.timer_running = true;

  if ($.increment_timeout === undefined) {
    increment_ets();
  }

  if (call_server) {
    $.getScript('/evaluation/' + $.evaluation_attempt_id + '/problem/' + $.problem_attempt_id + '/resume');
  }
}

function start_timer () {
  if ($.seconds_since_last_paused > 0) {
    $.timer_running = false;

    if ($('body').hasClass('sat')) {
      resume_timer(true);
    } else {
      $('#pause-test-time').modal('show');
    }
  } else {
    $.timer_running = true;
    if ($.increment_timeout === undefined) {
      increment_ets();
    }
  }
}

function resume_evaluation () {
  var $page = $('section#problem-solving:not(.final-page)');
  if ($page.length > 0) {
    $.current_problem_et = $page.data('problem-et');
    $.problem_attempt_id = $page.data('problem-attempt-id');
    $.evaluation_attempt_id = $page.data('evaluation-attempt-id');
    is_problem_previously_answered();
    resume_timer(false);

    $.go_to_next_problem = false;
  }
}
