/* global custom_message, is_blank, check_attempt_answer, table_responsive, triggerModal, bootstrap handle_intercom_visibility */

function lesson_bookmark (bookmark_button) {
  $.ajax({
    url: bookmark_button.attr('href'),
    type: 'post',
    headers: { 'X-CSRF-Token': $('meta[name=csrf-token]').attr('content') },
    dataType: 'json',
    success: function (data, textstatus, xhrreq) {
      if (xhrreq.getAllResponseHeaders().match(/Content-Type: text\/html/)) {
        custom_message('error', 'You need to login first. Reload the page.');
        return;
      }
      if (data.status == 'ok') {
        bookmark_button.toggleClass('flagged');

        if ($('body').data('controller') == 'lessons' && $('body').data('action') == 'show') {
          $('.topics-list li.current-topic > .js-wrapper > .bookmark').toggleClass('d-none');
        }
      } else {
        custom_message('warning', data.message);
      }
      $('#loading-overlay').hide();
    },
    fail: function (xhr) {
      $('#loading-overlay').hide();
      custom_message('warning', 'Something went wrong');
    }
  });
}

function is_single_line_paragraph ($paragraph) {
  $paragraph.addClass('force-single-line');
  var final_height = $paragraph.height();
  $paragraph.removeClass('force-single-line');

  return final_height == $paragraph.height();
}

function determine_gre_stem_alignment () {
  $('.gre#problem-solving .problem:visible, .gre .exercise.quantitative_comparison:visible').each(function () {
    var paragraphs_with_content = [];
    var belongs_to_stem = true;

    $(this).find('.interrogation_part').children().each(function () {
      if ($(this).hasClass('quantity-a')) {
        belongs_to_stem = false;
      }

      if (belongs_to_stem && $(this).is('p') && is_blank($(this).text()) == false) {
        paragraphs_with_content.push(this);
      }
    });

    var alignment = 'center';

    $.each(paragraphs_with_content, function (index, paragraph) {
      if (!is_single_line_paragraph($(paragraph))) {
        alignment = 'left';
      }
    });

    $.each(paragraphs_with_content, function (index, paragraph) {
      $(paragraph).css('text-align', alignment);
    });

    if (is_single_line_paragraph($(this).find('.quantity-a p'))) {
      $(this).find('.quantity-a p').css('text-align', 'center');
    } else {
      $(this).find('.quantity-a p').css('text-align', 'left');
    }

    if (is_single_line_paragraph($(this).find('.quantity-b p'))) {
      $(this).find('.quantity-b p').css('text-align', 'center');
    } else {
      $(this).find('.quantity-b p').css('text-align', 'left');
    }
  });
}

$(document).on('keyup', 'body.sat .answers .numerator-wrapper input, body.gre .answers .numerator-wrapper input, body.gre .answers .denominator-wrapper input', function (event) {
  const $input = $(this);
  const value = $input.val();
  const is_negative = value.startsWith('-') || (event.key === '-' && $input[0].selectionStart === 0);
  const invalid_fraction = /[^0-9.\-\/]|(?!^)-|^-?\/|\.{2,}|(?:\d+\.\d+\.)|\/.*\/|\d*\.\d+\/|\/\d*\.\d+|\/\d*\.$|\d+\.$|\.\/|^-\.$/;

  if (event.key === '-') {
    if ($input[0].selectionStart !== 0 || value.includes('-')) {
      event.preventDefault();
    }
  }

  if (invalid_fraction.test(value)) {
    $input.closest('.numerator-wrapper').find('.error-message').removeClass('d-none');
    const message = error_message(value);
    $input.siblings('.error-message').find('.error-text').text(message);
  } else {
    $input.closest('.numerator-wrapper').find('.error-message').addClass('d-none');
  }

  const new_max_length = is_negative ? 6 : 5;
  $(this).attr('maxlength', new_max_length);

  if ($('body').hasClass('gre')) {
    const has_denominator = $(this).closest('.fraction-input').find('.denominator-wrapper').length > 0;
    if (has_denominator && event.key === '.') {
      event.preventDefault();
    }
  }

  update_answer_preview();
});

function update_answer_preview () {
  const numerator = $('.sat#problem-solving .question-content .answers .numerator-wrapper input').val() || '';
  const preview_element = $('.sat#problem-solving .question-content .answers .answer-preview .result.fill-in-the-blank');
  preview_element.text(numerator);
}

function error_message (value) {
  if (/[^0-9.\-\/]/.test(value)) return 'Whitespace or fixed fraction are not allowed';
  if (/(?!^)-/.test(value)) return 'Negative sign can only be at the beginning.';
  if (/^-.*-/.test(value)) return 'Only one negative sign is allowed.';
  if (/(?:\d+\.\d+\.)/.test(value)) return 'Only one decimal point is allowed.';
  if (/\/.*\//.test(value)) return 'Only one slash is allowed.';
  if (/\d*\.\d+\/|\/\d*\.\d+/.test(value)) return 'Decimals are not allowed in fractions';
  return "You've entered a decimal point or slash in the wrong position";
}

function toggle_sidebar ($icon) {
  $icon.toggleClass('section-color-2 pushed');
  $('.lesson-sidebar').toggleClass('active');
  $('section#lesson-show, #lesson-content-wrapper, footer, .trial-banner').toggleClass('pushed');

  if ($icon.hasClass('pushed')) {
    $('.icn-arrow-l, .icn-arrow-r').fadeOut(150);

    setTimeout(function () {
      $('ul.topics-list').animate({ scrollTop: $('.current-topic').offset().top - $(document).scrollTop() - ($('.lesson-nav').outerHeight(true) + 90) });
    }, 300);
  } else {
    $('.icn-arrow-l, .icn-arrow-r').fadeIn(150);
    $('ul.topics-list').animate({ scrollTop: 0 });
  }
  $('.lesson-sidebar').css('top', $('.lesson-nav').outerHeight(true));
}

function disable_scroll () { // eslint-disable-line no-unused-vars
  $('body').addClass('scroll-disabled');
}

function enable_scroll () { // eslint-disable-line no-unused-vars
  $('body').removeClass('scroll-disabled');
}

function hide_progress_bar ($progress_bar) {
  var y_scroll_pos = window.pageYOffset;
  var y_bar_pos;

  if ($progress_bar.length != 0) {
    y_bar_pos = ($progress_bar.position().top + $progress_bar.height());

    if (y_scroll_pos > y_bar_pos) {
      $('.lesson-nav').addClass('scrolled');
    } else {
      $('.lesson-nav').removeClass('scrolled');
    }

    $('.lesson-sidebar').css('top', $('.lesson-nav').outerHeight(true));
    $('.edit-lesson-link').css('top', $('.lesson-nav').outerHeight(true));
  }
}

function position_lesson_progress_bar_mobile () {
  var $progress_bar = $('.lesson-progress-bar.mobile');
  hide_progress_bar($progress_bar);
}

function position_lesson_progress_bar_desktop () {
  var $progress_bar = $('.lesson-progress-bar.desktop');
  hide_progress_bar($progress_bar);
}

function open_locked_chapter_modal () {
  if ($('#lessons-wrapper').hasClass('ondemand-locked')) {
    $('#locked-lesson').modal('show');
  }
}

$(document).on('click', 'a.see-all-topics', function (event) {
  event.preventDefault();
  event.stopPropagation();
  toggle_sidebar($(this));
});

$(document).on('click', '.lesson-sidebar .close-sidebar', function (event) {
  event.preventDefault();
  toggle_sidebar($('a.see-all-topics'));
});

$(document).on('click', '#lessons-wrapper a[data-remote="true"]:not(.skip-overlay)', function () {
  $('#calculator').hide();

  if (navigator.onLine) {
    $('#loading-overlay').show();
  }
});

$(document).on('click', 'section#lesson-show .example.problem_solving .answers label, ' +
  'section#lesson-show .example.data_sufficiency .answers label, ' +
  'section#lesson-show .example.quantitative_comparison .answers label', function (event) {
  event.preventDefault();

  var $this_styled_radio = $(this).find('.styledRadio');

  if ($this_styled_radio.hasClass('user-choice')) {
    $this_styled_radio.removeClass('user-choice selected');
    $(this).find('input:radio').prop('checked', false);
  }
});

$(document).on('click', '.feedback-score-button', function (event) {
  event.preventDefault();
  var score = parseInt($(this).find('a').data('value'));

  $('#lesson_feedback_score').val(score);
  $('.feedback-score-button').removeClass('active');
  $(this).addClass('active');

  if (!($('.lesson-feedback .feedback-body').is(':visible'))) {
    $('#lesson_feedback_comment').val('');
    $('#loading-overlay').show();
    $(this).closest('form').submit();
  }
});

$(document).on('click', '#note-guide-button', function (event) {
  event.preventDefault();
  $('#note-guide').modal('toggle');
});

$(document).on('click', '#toggle-fullscreen', function () {
  if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement) {
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen();
    } else if (document.documentElement.mozRequestFullScreen) {
      document.documentElement.mozRequestFullScreen();
    } else if (document.documentElement.webkitRequestFullscreen) {
      document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
    }
    if (!$(this).hasClass('full-screen-active')) {
      $(this).addClass('full-screen-active');
    }
  } else {
    if (document.cancelFullScreen) {
      document.cancelFullScreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.webkitCancelFullScreen) {
      document.webkitCancelFullScreen();
    }
    if ($(this).hasClass('full-screen-active')) {
      $(this).removeClass('full-screen-active');
    }
  }
});

$(document).on('click', '.flags a, .bookmark a', function (event) {
  event.preventDefault();
  $('#loading-overlay').show();

  lesson_bookmark($(this));
});

$(document).on('click', '.archive-info', function (event) {
  if (window.matchMedia('(max-width: 991px)').matches) {
    event.preventDefault();
    const tooltip = new bootstrap.Tooltip(this, {
      trigger: 'manual',
      html: true
    });
    tooltip.show();
  }
});

$(document).ready(function () {
  open_locked_chapter_modal();
  determine_gre_stem_alignment();
  check_attempt_answer();
  table_responsive($('.lesson-body table'), true);
  if (window.matchMedia('(max-width: 767px)').matches) {
    position_lesson_progress_bar_mobile();
  } else {
    position_lesson_progress_bar_desktop();
  }

  if ($('.modal.not-using-feedback').length) {
    triggerModal('not-using-feedback');
  }

  $('.edit-lesson-link').css('top', $('.lesson-nav').outerHeight(true));
});

$(document).resize(function () {
  determine_gre_stem_alignment();

  if (window.matchMedia('(max-width: 767px)').matches) {
    position_lesson_progress_bar_mobile();
  } else {
    position_lesson_progress_bar_desktop();
  }
});

$(window).scroll(function () {
  if (window.matchMedia('(max-width: 767px)').matches) {
    $('.tooltip').removeClass('show');
    position_lesson_progress_bar_mobile();
  } else {
    position_lesson_progress_bar_desktop();
  }
});

$(document).on('click', '.coordinate-geometry-lesson-bar .btn-close', function (event) {
  var wrapper = $(this).closest('.coordinate-geometry-lesson-bar');

  $.ajax({
    type: 'post',
    url: $(this).data('flag-url'),
    dataType: 'script',
    success: function (data, textstatus, xhrreq) {
      wrapper.addClass('d-none');
    },
    fail: function (xhr) {
      custom_message('warning', 'Something went wrong');
    }
  });
});

$(document).on('click', '.video-container-note .btn-close', function (event) {
  var wrapper = $(this).closest('.video-container-note');

  $.ajax({
    type: 'post',
    url: $(this).data('flag-url'),
    dataType: 'script',
    success: function (data, text_status, xhr_req) {
      wrapper.addClass('d-none');
    },
    fail: function (xhr) {
      custom_message('warning', 'Something went wrong');
    }
  });
});

$(document).on('click', '.switch-video-tab, .ondemand-btn-wrapper', function (event) {
  var selected_tab = $(event.target);
  var example_id = selected_tab.data('example-id');
  var is_on_demand_tab = selected_tab.hasClass('ondemand-btn') || selected_tab.hasClass('ondemand-btn-wrapper');

  var instructor_types = ['ondemand', 'screenshare'];

  instructor_types.forEach(function (type) {
    var should_show = (type === 'ondemand') === is_on_demand_tab;
    toggle_instructor_info(example_id, type, should_show);
  });

  $(`#ondemand-video-wrapper-example-card-${example_id}`).toggleClass('ondemand-background', is_on_demand_tab);

  var dropdown = selected_tab.closest('.ondemand-video-wrapper-dropdown');
  if (dropdown.length > 0) {
    if (is_on_demand_tab) {
      dropdown.find('.selected-screenshare-video-btn').addClass('d-none');
      dropdown.find('.selected-ondemand-video-btn').removeClass('d-none');
    } else {
      dropdown.find('.selected-screenshare-video-btn').removeClass('d-none');
      dropdown.find('.selected-ondemand-video-btn').addClass('d-none');
    }
  }
});

function toggle_instructor_info (example_id, type, should_show) {
  $(`.${type}-instructor-image-${example_id}`).toggleClass('d-none', !should_show);
  $(`.${type}-instructor-name-${example_id}`).toggleClass('d-none', !should_show);
  $(`.${type}-instructor-title-${example_id}`).toggleClass('d-none', !should_show);
}

function close_custom_modal_and_reset_icon () {
  $('#intercom-custom-modal').addClass('d-none');
  $('.custom-message-icon').removeClass('d-none');
  $('.custom-up-arrow').addClass('d-none').removeClass('intercom-opened');
}

$(document).on('click', '#custom-intercom-button', function (event) {
  const arrow_icon = $('.custom-up-arrow');

  if (arrow_icon.hasClass('intercom-opened')) {
    close_custom_modal_and_reset_icon();

    if (typeof window.Intercom === 'function') {
      window.Intercom('hide');
    }

    return;
  }

  if (typeof window.Intercom === 'function' && window.Intercom('isOpen')) {
    return;
  }

  $('#intercom-custom-modal').removeClass('d-none');
  $('.custom-message-icon').addClass('d-none');
  arrow_icon.removeClass('d-none').addClass('intercom-opened');
});

$(document).on('click', '.go-to-intercom, .chat-feedback-wrapper .talk-team .go-to-intercom', function (e) {
  e.preventDefault();

  var chat_feedback_wrapper = $(this).closest('.chat-feedback');

  if (chat_feedback_wrapper.hasClass('show')) {
    chat_feedback_wrapper.modal('hide');
    close_chat_and_reset_ui();
  } else if ($('.chat-side-icon-right').hasClass('active')) {
    close_chat_and_reset_ui();
  }

  if (typeof window.Intercom === 'function') {
    if ($(this).hasClass('intercom-talk-to-ttp')) {
      window.Intercom('show');
    } else {
      setTimeout(function () {
        window.Intercom('show');
      }, 500);
    }
  }

  if ($(this).hasClass('intercom-modal-ttp-team')) {
    $('#intercom-custom-modal').addClass('d-none');
  }
});

$(document).ready(function () {
  if (typeof window.Intercom === 'function') {
    window.Intercom('onHide', function () {
      close_custom_modal_and_reset_icon();
    });
  }
});

$(document).on('click', '#intercom-custom-modal .btn-close', function (e) {
  e.preventDefault();

  close_custom_modal_and_reset_icon();
});

function close_chat_and_reset_ui () {
  $('.chat-side-icon-right').trigger('click');
  close_custom_modal_and_reset_icon();
  $('.custom-message-icon').addClass('d-none');
  $('.custom-up-arrow').removeClass('d-none').addClass('intercom-opened');
}

function handle_intercom_visibility () {
  if (typeof window.Intercom === 'function' && $('.chat-component:not(.d-none)').length) {
    $('#custom-intercom-button').removeClass('d-none');
    $('.lesson-sidebar-chat-ai .head-right-btn .headset').removeClass('d-none');
    $('.chat-feedback-modals .chat-feedback-wrapper .chat-feedback .talk-team').removeClass('d-none');
  } else {
    $('#custom-intercom-button').addClass('d-none');
    $('.lesson-sidebar-chat-ai .head-right-btn .headset').addClass('d-none');
    $('.chat-feedback-modals .chat-feedback-wrapper .chat-feedback .talk-team').addClass('d-none');
  }
}
