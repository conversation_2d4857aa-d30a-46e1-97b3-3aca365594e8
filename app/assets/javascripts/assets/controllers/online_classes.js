/* global insert_content_on_radio_button_label_subscription */
$(document).ready(function () {
  insert_content_on_radio_button_label_subscription($('section#checkout div.payment-option'));
  const $checkout_section = $('section#checkout');
  const $use_card_on_file = $checkout_section.find('#use-card-on-file');
  const $use_new_card = $checkout_section.find('#use-new-card');
  const new_card_value = $('#online-class-subscription .card-body input[name="use_new_card"]').val();

  if (new_card_value !== 'yes') {
    $('#online-class-subscription #accordion-payment-affirm, #online-class-subscription #accordion-payment').addClass('collapse').collapse('hide');
  }

  if ($use_card_on_file.length > 0) {
    $use_card_on_file.prop('checked', true).change();
  } else {
    $use_new_card.prop('checked', true).change();
  }
});

$(document).on('change', 'section#online-classes input', function () {
  if ($(this).is(':checked')) {
    $('div.online-class').removeClass('active');
    $(this).closest('div.online-class').addClass('active');
  }

  $(this).closest('form').find('input[type="submit"]').prop('disabled', false);
});

$(document).on('click', 'button[data-bs-target^="#onlineclass-"]', function () {
  var $accordion = $(this).closest('div.accordion-item');

  $('#accordion-online-classes div.accordion-item.opened').removeClass('opened');

  if (!$accordion.hasClass('opened') && $(this).attr('aria-expanded') == 'true') {
    $accordion.addClass('opened');
  }
});

$(document).on('click', 'button[data-bs-target="#verify-user"]', function () {
  var online_id = $(this).attr('data-online-class-id');
  $('#verify-user').find('a.sign-in').each(function () {
    $(this).attr('href', `/users/sign_in?online_class=${online_id}`);
  });

  $('#verify-user').find('a.sign-up').each(function () {
    $(this).attr('href', `/online_classes/${online_id}/checkout`);
  });
});

$(document).on('click', '#checkout #another-code', function () {
  var $form = $(this).closest('form');
  var $onlineclass_plan = $form.find('#plan-onlineclass');
  var plan_price = $onlineclass_plan.data('plan-price');
  var before_price = $onlineclass_plan.data('before-price');
  var before_plan_price = $onlineclass_plan.data('before-plan-price') || plan_price;
  $form.find('.current-price .amount').first().text(plan_price);
  $form.find('.before-price .amount').text(before_price).addClass('filled').text(before_plan_price);
});

$(document).on('show.bs.collapse hide.bs.collapse', '.next-liveteach-class .session-homework', function (event) {
  toggle_class_on_collapse(event, '.session-homework', 'active', event.type === 'show');
});

$(document).on('show.bs.collapse hide.bs.collapse', '.next-liveteach-class .session-accordion', function (event) {
  const schedule_card = $(event.target).parent().find('.schedule-card');
  if (schedule_card.length) {
    schedule_card.toggleClass('session-expanded', event.type === 'show');
  }
});

$(document).on('click', '.next-liveteach-class .session-files', function (event) {
  event.preventDefault();
  var target_modal = $(this).data('bs-target');
  if (target_modal) {
    const online_class_session_files_modal = new bootstrap.Modal(target_modal);
    online_class_session_files_modal.show();
  }
});

$(document).on('click', '.next-liveteach-class .online-class-recording', function (event) {
  event.preventDefault();
  var target_modal = $(this).data('bs-target');
  if (target_modal != '') {
    const online_class_recording_modal = new bootstrap.Modal(target_modal);
    online_class_recording_modal.show();
  }
});

$(document).on('click', '.next-liveteach-class .schedule-card', function (event) {
  const $target = $(event.target);
  if ($target.closest('[data-stop-propagation]').length > 0) {
    return;
  }

  const collapse_target = $(this).data('target');
  if (collapse_target) {
    $(collapse_target).collapse('toggle');
  }
});

function highlight_study_module ($session_homeworks) {
  $session_homeworks.find('.homework-group').each(function () {
    $(this).find('.indicator, .bar').addClass('highlighted');

    if ($(this).find('.next-task').length) {
      return false;
    }
  });
}

function update_next_homework_task ($accordion, $homework, $next_task) {
  $accordion.find('.indicator, .bar').removeClass('highlighted');
  $accordion.find('.session-homework').removeClass('next-task');
  $accordion.find('.accordion-body').removeClass('completed');
  $accordion.find('.homework-group:last-child .bar').css('height', 'inherit');
  $homework.closest('.accordion-item').find('.next-item-indicator').addClass('d-none');

  $next_task.find('.session-homework').addClass('next-task active').end().find('.collapse').addClass('show').end().find('.header').removeClass('collapsed').end().find('.next-item-indicator').removeClass('d-none');

  highlight_study_module($accordion);

  if ($homework.parent().data('task-id') !== $next_task.data('task-id')) {
    $homework.find('.next-item-indicator').addClass('d-none');
  }

  const $current_group = $homework.closest('.homework-group');
  const $next_group = $next_task.closest('.homework-group');

  if ($current_group.data('homework-group-id') !== $next_group.data('homework-group-id')) {
    $next_group.find('.session-homework').not('.next-task').each(function () {
      if ($(this).prevAll('.next-task').length > 0) {
        $(this).addClass('next-task-shown');
      }
    });
  }
}

$(document).on('change', 'input.homework-task-completion', function () {
  const $checkbox = $(this);
  const is_checked = $checkbox.is(':checked');
  const $task_wrapper = $checkbox.closest('.homework-task-wrapper');
  const $homework = $task_wrapper.find('> .session-homework');
  const completion_percentage = is_checked ? 100 : 0;

  $('#loading-overlay').show();

  $.ajax({
    url: $checkbox.data('url'),
    type: 'POST',
    headers: { 'X-CSRF-Token': $('meta[name=csrf-token]').attr('content') },
    dataType: 'json',
    success: function (response, _status, xhr) {
      if (xhr.getAllResponseHeaders().includes('Content-Type: text/html')) {
        custom_message('error', 'You need to login first. Reload the page.');
        return;
      }

      if (response.status !== 'ok') {
        custom_message('warning', response.message);
        return;
      }

      update_homework_task_ui($homework, $task_wrapper, is_checked, completion_percentage, response.next_task_id);
    },
    error: function () {
      custom_message('warning', 'Something went wrong');
    },
    complete: function () {
      $('#loading-overlay').hide();
    }
  });
});

$(document).on('hidden.bs.modal', '.online-class-session-recording', function () {
  $(this).find('iframe.cohort-session-video').remove();
});

$(document).on('show.bs.modal', '.online-class-session-recording', function () {
  const iframe = $('<iframe>', {
    class: 'cohort-session-video',
    src: `https://player.vimeo.com/video/${$(this).data('video-src')}?autoplay=1`,
    frameborder: 0,
    allowfullscreen: true,
    webkitallowfullscreen: true,
    mozallowfullscreen: true,
    id: `cohort-video-player-${$(this).data('model-id')}`
  });
  $(this).find('.modal-body .video-wrapper').append(iframe);
});

function toggle_class_on_collapse (event, selector, className, add) {
  const $target = $(event.target).closest(selector);
  if ($target.length) {
    $target.toggleClass(className, add);
  }
}

function update_homework_task_ui ($homework, $task_wrapper, is_checked, completion_percentage, next_task_id) {
  $homework.removeClass('active').toggleClass('completed', is_checked).find('> .collapse').removeClass('show').end().find('> .header').addClass('collapsed');
  $homework.find('.completion-status').toggleClass('completed', is_checked);
  $homework.find('.completion-status .completed-text').toggleClass('d-none', !is_checked).toggleClass('text-grey-5 completed', is_checked);
  $homework.find('.completion-status .start-text').toggleClass('d-none', is_checked);
  $homework.find('.status-icon .trophy-icon, .status-icon .check-circle-icon').toggleClass('completed', is_checked);
  $homework.find('.header-info .header-title').toggleClass('text-decoration-line-through text-grey-4', is_checked);
  $homework.find('.status-icon .knob').val(completion_percentage).change();

  const $next_task = $(`[data-task-id="${next_task_id}"]`);

  if ($next_task.length === 0) {
    const $accordion = $task_wrapper.closest('.session-accordion');
    $homework.find('.next-item-indicator').addClass('d-none');
    $accordion.find('.indicator, .bar').addClass('highlighted');
    $accordion.find('.homework-group:last-child .bar').css('height', '95px');
    $accordion.find('.accordion-body').addClass('completed');
    $homework.removeClass('next-task');
    return;
  }

  const $accordion = $next_task.closest('.session-accordion');
  update_next_homework_task($accordion, $homework, $next_task);
}

$(document).on('hidden.bs.modal', '.homework-takeaways-modal', function () {
  var modal = $(this);
  var textarea = modal.find('.takeaways-editor');

  if (textarea.length > 0) {
    var editor_id = textarea.attr('id');

    if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances[editor_id]) {
      CKEDITOR.instances[editor_id].setData(textarea.data('value'));
    }
  }
});
