/* global table_responsive */

$(document).ready(function () {
  table_responsive($('section#question-review table'), false);
  initialize_filters();
  var $evaluation_attemtps_show_controller = $('[data-controller="evaluation_attempts"][data-action="show"]');

  if ($evaluation_attemtps_show_controller.length) {
    $('#loading-overlay').hide();
  }
});

$(document).on('click', '.diagnostic-result-view-filter a', function (event) {
  event.preventDefault();

  $('.diagnostic-result-view-filter button').text($(this).text());
});

function initialize_filters () {
  if ($('.nav.header-tabs-sub-nav .tab-btn:last').hasClass('active')) {
    $('.result-view-filters-wrapper').removeClass('d-none');
  } else {
    $('.result-view-filters-wrapper').addClass('d-none');
  }
}

$(document).on('click', '.nav.header-tabs-sub-nav', function () {
  initialize_filters();
});

$(document).ready(function () {
  var $accuracy_stats_boxes = $('div.accuracy-stats-box');
  var heights = [];

  $accuracy_stats_boxes.each(function () {
    heights.push($(this).height());
  });

  var min_height = Math.max.apply(null, heights);

  $accuracy_stats_boxes.css('min-height', (min_height + 66));
});
