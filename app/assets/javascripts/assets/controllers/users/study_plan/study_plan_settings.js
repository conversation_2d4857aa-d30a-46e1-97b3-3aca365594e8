/* global custom_message, is_blank */

var calendar_unavailable_date_picker = {
  date: new Date(`${$('body').data('user-current-date')}T00:00:00`),
  month: null,
  year: null,
  excluded_dates: null,
  exam_name: null,
  exam_dates: [],
  study_start_date: null,

  setup: function (options) {
    this.bind_events();

    options = options || {};

    this.study_start_date = options.study_start_date || this.date;
    this.month = this.study_start_date.getMonth();
    this.year = this.study_start_date.getFullYear();

    this.exam_name = options.exam_name;
    this.exam_dates = is_blank(options.exam_dates) ? [] : options.exam_dates.split(',');
    this.excluded_dates = is_blank(options.excluded_dates) ? [] : options.excluded_dates.split(',');
  },

  bind_events: function () {
    var self = this;

    $(document).on('click', '#next-month', function () {
      self.month++;

      if (self.month > 11) {
        self.month = 0;
        self.year++;
      }

      self.get_days(self.month);
    });

    $(document).on('click', '#prev-month', function () {
      self.month--;

      if (self.month < 0) {
        self.month = 11;
        self.year--;
      }

      self.get_days(self.month);
    });

    $(document).on('click', '#date-today', function (event) {
      event.preventDefault();

      self.month = self.date.getMonth();
      self.year = self.date.getFullYear();

      self.get_days(self.month);
    });

    if ($('#calendar-setup-modal section#availability').length > 0) {
      $(document).on('click', '.date.present:not(.exam), .date.future:not(.exam)', function () {
        if ($(this).hasClass('excluded')) {
          $(this).removeClass('excluded');
          self.excluded_dates.splice($.inArray($(this).data('date'), self.excluded_dates), 1);
        } else {
          $(this).addClass('excluded');
          self.excluded_dates.push($(this).data('date'));
        }

        $('#calendar-setup-modal form input#study_plan_setting_study_dates_to_exclude').remove();

        for (var i = 0; i < self.excluded_dates.length; i++) {
          $('#calendar-setup-modal form').append(
            '<input multiple="multiple" value="" type="hidden" name="study_plan_setting[study_dates_to_exclude][]" id="study_plan_setting_study_dates_to_exclude"></input>'
          );
        }

        $('input[name="study_plan_setting[study_dates_to_exclude][]"]').each(function (index) {
          $(this).val(self.excluded_dates[index]);
        });
      });
    }

    if ($('.adjust-study-plan-settings section#availability').length > 0) {
      $(document).on('click', '.date.present:not(.exam), .date.future:not(.exam)', function () {
        if ($(this).hasClass('excluded')) {
          $(this).removeClass('excluded');
          self.excluded_dates.splice($.inArray($(this).data('date'), self.excluded_dates), 1);
        } else {
          $(this).addClass('excluded');
          self.excluded_dates.push($(this).data('date'));
        }

        $('.adjust-study-plan-settings input#user_study_plan_setting_attributes_study_dates_to_exclude').remove();

        for (var i = 0; i < self.excluded_dates.length; i++) {
          $('.adjust-study-plan-settings').append(
            '<input multiple="multiple" value="" type="hidden" name="user[study_plan_setting_attributes][study_dates_to_exclude][]" id="user_study_plan_setting_attributes_study_dates_to_exclude"></input>'
          );
        }

        if (self.excluded_dates.length == 0) {
          $('.adjust-study-plan-settings').append(
            '<input multiple="multiple" value="" type="hidden" name="user[study_plan_setting_attributes][study_dates_to_exclude][]" id="user_study_plan_setting_attributes_study_dates_to_exclude"></input>'
          );
        } else {
          $('input[name="user[study_plan_setting_attributes][study_dates_to_exclude][]"]').each(function (index) {
            $(this).val(self.excluded_dates[index]);
          });
        }
      });
    }
  },

  get_days: function (month) {
    var self = this;

    month = month || this.month;

    $('#weeks-wrapper').empty();

    var months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

    $('#calendar #current-month').text(`${months[month]} ${self.year}`);

    var first_day = new Date(self.year, month, 1);
    var last_day = new Date(self.year, month + 1, 0);

    var off_set = first_day.getDay();

    var last_month_day_count = new Date(self.year, month, 0).getDate() - off_set + 1;
    var present_day_count = 1;
    var future_day_count = 1;

    for (var i = 0, l = last_day.getDate() + off_set; i < l; i += 7) {
      $('#weeks-wrapper').append(`<div class=week data-week-number="${i}">`);

      for (var day_count = 0; day_count < 7; day_count++) {
        if (off_set == 0) {
          if (present_day_count > last_day.getDate()) {
            var is_exam_date = self.exam_dates.indexOf(self.format_date(new Date(self.year, month + 1, future_day_count))) > -1;

            var element_tag = `<div class="date ${is_exam_date ? 'exam' : ''} future inactive"><span class="date-text">${future_day_count == 1 ? `${months[month + 1 > 11 ? 0 : month + 1].substring(0, 3)} ${future_day_count}` : `${future_day_count}`}</span>`;

            if (is_exam_date) {
              element_tag = `${element_tag}<span class="exam-name">${self.exam_name}</span>`;
            }

            $(`.week[data-week-number="${i}"]`).append(`${element_tag}</div`);

            future_day_count++;
          } else {
            is_exam_date = self.exam_dates.indexOf(self.format_date(new Date(self.year, month, present_day_count))) > -1;

            var classes = [
              is_exam_date ? 'exam' : '',
              self.excluded_dates.indexOf(self.format_date(new Date(self.year, month, present_day_count))) > -1 && !is_exam_date ? 'excluded' : ''
            ];

            if (self.year > self.date.getFullYear() || month > self.date.getMonth()) {
              classes.push('future');
            } else if (self.year == self.date.getFullYear() && month == self.date.getMonth()) {
              classes.push('present');

              if (present_day_count == self.date.getDate()) {
                classes.push('today');
              }
            } else {
              classes.push('past');
            }

            element_tag = `<div class="date ${classes.filter(item => item).join(' ')}" data-date="${self.format_date(new Date(self.year, month, present_day_count))}"><span class="date-text">${present_day_count == 1 ? `<span class='hidden-mobile'>${months[month].substring(0, 3)}</span> ${present_day_count}` : `${present_day_count}`}</span>`;

            if (is_exam_date) {
              element_tag = `${element_tag}<span class="exam-name">${self.exam_name}</span>`;
            }

            $(`.week[data-week-number="${i}"]`).append(`${element_tag}</div`);

            present_day_count++;
          }
        } else {
          is_exam_date = self.exam_dates.indexOf(self.format_date(new Date(self.year, month - 1, last_month_day_count))) > -1;

          element_tag = `<div class="date ${is_exam_date ? 'exam' : ''} past"><span class="date-text">${last_month_day_count}</span>`;

          if (is_exam_date) {
            element_tag = `${element_tag}<span class="exam-name">${self.exam_name}</span>`;
          }

          $(`.week[data-week-number="${i}"]`).append(`${element_tag}</div`);

          last_month_day_count++;
          off_set--;
        }
      }

      $('#weeks-wrapper').append('</div');
    }
  },

  format_date: function (date) {
    var yyyy = date.getFullYear().toString();
    var mm = (date.getMonth() + 1).toString();
    var dd = date.getDate().toString();

    var mm_chars = mm.split('');
    var dd_chars = dd.split('');

    return yyyy + '-' + (mm_chars[1] ? mm : '0' + mm_chars[0]) + '-' + (dd_chars[1] ? dd : '0' + dd_chars[0]);
  }
};

var form_modified = false;

function set_study_start_date_for_calendar () {
  calendar_unavailable_date_picker.study_start_date = new Date($('#user_study_plan_setting_attributes_study_start_date').val());
  calendar_unavailable_date_picker.month = calendar_unavailable_date_picker.study_start_date.getMonth();
  calendar_unavailable_date_picker.get_days(calendar_unavailable_date_picker.month);
}

function get_date_string_from_date (date) {
  return [date.getFullYear(), ('0' + (date.getMonth() + 1)).slice(-2), ('0' + date.getDate()).slice(-2)].join('-');
}

// To fix the issue happening on safari
function parse_date (date) {
  var parsed = Date.parse(date);
  if (!isNaN(parsed)) {
    return parsed;
  }

  return Date.parse(date.replace(/-/g, '/').replace(/[a-z]+/gi, ' '));
}

$(document).on('click', '.adjust-study-plan-settings section#view-preferences .study-plan-view-card', function () {
  $(this).closest('section#view-preferences').find('.study-plan-view-card').removeClass('active');
  $(this).addClass('active');

  if ($(this).closest('section#view-preferences').find('.study-plan-view-card.calendar-view').hasClass('active')) {
    $('.adjust-study-plan-settings input#user_study_plan_setting_attributes_is_calendar_view_enabled').val(true);
    $('.adjust-study-plan-settings').find('.date-time-preferences-and-availability-wrapper').removeClass('d-none');
    $('.adjust-plan-mission-reminder').addClass('d-none');
    $('#form-submit-button').removeClass('show-overlay');
    $('#form-submit-button').text($('#form-submit-button').data('calendar-button-text'));
  } else {
    $('.adjust-study-plan-settings input#user_study_plan_setting_attributes_is_calendar_view_enabled').val(false);
    $('.adjust-study-plan-settings').find('.date-time-preferences-and-availability-wrapper').addClass('d-none');
    $('.adjust-plan-mission-reminder').removeClass('d-none');
    $('#form-submit-button').addClass('show-overlay');
    $('#form-submit-button').text($('#form-submit-button').data('mission-button-text'));
  }

  form_modified = true;
});

$(document).on('click', '.adjust-study-plan-settings section#date-time-preferences .choice-card .title', function () {
  $(this).closest('section#date-time-preferences').find('.choice-card').removeClass('active');
  $(this).closest('.choice-card').addClass('active');

  $(this).closest('section#date-time-preferences').find('.choice-card.active input').removeAttr('disabled');
  $(this).closest('section#date-time-preferences').find('.choice-card:not(.active) input').attr('disabled', true);

  if ($(this).closest('section#date-time-preferences').find('.choice-card.per-week').hasClass('active')) {
    $('.adjust-study-plan-settings input#user_study_plan_setting_attributes_is_study_hours_per_week').val(true);
  } else {
    $('.adjust-study-plan-settings input#user_study_plan_setting_attributes_is_study_hours_per_week').val(false);
  }

  form_modified = true;
});

$(document).on('submit', 'form#customize-study-plan-form', function (event) {
  var all_valid = true;
  $('#exit-study-plan-modal').modal('hide');

  if ($('#customize-study-plan-form .study-plan').attr('data-legacy-course-enabled') == 'true') {
    if ($('.score-bar-container #score-bar .for-legacy button.active').length != 1) {
      all_valid = false;
    }
  } else {
    if ($('.score-bar-container #score-bar .for-default button.active').length != 1) {
      all_valid = false;
    }
  }

  if (!all_valid) {
    event.preventDefault();
    $('#loading-overlay').hide();

    custom_message('error', 'Please select your desired score');
    return;
  }

  if ($('.adjust-study-plan-settings section#view-preferences .study-plan-view-card.calendar-view.active').length != 0 && $(this).attr('data-show-calendar-regenerate-modal') != 'false') {
    event.preventDefault();

    if (is_blank($('#user_study_plan_setting_attributes_study_start_date').val())) {
      custom_message('error', 'Please select a start date');
      all_valid = false;
    } else if (parse_date(get_date_string_from_date(new Date(`${$('#user_study_plan_setting_attributes_study_start_date').val()}`))) < parse_date(get_date_string_from_date(new Date(`${$('body').data('user-current-date')}T00:00:00`)))) {
      custom_message('error', "You can't choose a past date to regenerate your calendar");
      all_valid = false;
    } else if ($('.hours-wrapper .choice-card.per-week').hasClass('active')) {
      if (is_blank($('#user_study_plan_setting_attributes_study_hours_per_week').val())) {
        custom_message('error', 'Please enter your study hours per week');
        all_valid = false;
      } else if ($('#user_study_plan_setting_attributes_study_hours_per_week').val().indexOf('.') != -1 || parseInt($('#user_study_plan_setting_attributes_study_hours_per_week').val()) < 0 || parseInt($('#user_study_plan_setting_attributes_study_hours_per_week').val()) > 168) {
        custom_message('error', 'Please enter a valid number of hours');
        all_valid = false;
      } else if ($('#user_study_plan_setting_attributes_study_hours_per_week').val() < 10) {
        custom_message('error', 'Total hours per week should be equal or greater than 10');
        all_valid = false;
      }
    } else if ($('.hours-wrapper .choice-card.per-day').hasClass('active')) {
      var blank_hours = $('.hours-wrapper .choice-card.per-day input[name="user[study_plan_setting_attributes][study_hours_per_day][]"]').filter(function () {
        return is_blank($(this).val()) == true;
      });

      var float_hours = $('.hours-wrapper .choice-card.per-day input[name="user[study_plan_setting_attributes][study_hours_per_day][]"]').map(function () {
        return $(this).val().indexOf('.') == -1;
      });

      var negative_or_maxed_hours = $('.hours-wrapper .choice-card.per-day input[name="user[study_plan_setting_attributes][study_hours_per_day][]"]').map(function () {
        return parseInt($(this).val()) < 0 || parseInt($(this).val()) > 24;
      });

      var hours_sum = $('.hours-wrapper .choice-card.per-day input[name="user[study_plan_setting_attributes][study_hours_per_day][]"]').map(function () {
        return is_blank($(this).val()) ? 0 : parseInt($(this).val());
      }).toArray().reduce((a, b) => a + b);

      if (blank_hours.length == $('.hours-wrapper .choice-card.per-day input[name="user[study_plan_setting_attributes][study_hours_per_day][]"]').length) {
        custom_message('error', 'Please enter hours per day');
        all_valid = false;
      } else if (float_hours.toArray().indexOf(false) > -1 || negative_or_maxed_hours.toArray().indexOf(true) > -1) {
        custom_message('error', 'Please enter a valid number of hours');
        all_valid = false;
      } else if (hours_sum < 10) {
        custom_message('error', 'Total hours for days should be equal or greater than 10');
        all_valid = false;
      }
    }

    if (all_valid) {
      $('#calendar-regenerate-confirmation-modal').modal('show');
    } else {
      $('#loading-overlay').hide();
    }
  }
});

$(document).on('click', '.modal#calendar-regenerate-confirmation-modal .confirm', function () {
  $(this).closest('.modal').modal('hide');

  $('form#customize-study-plan-form').attr('data-show-calendar-regenerate-modal', 'false').submit();
});

$(document).ready(function () {
  if ($('.adjust-study-plan-settings #availability').length > 0) {
    calendar_unavailable_date_picker.setup({
      excluded_dates: $('.adjust-study-plan-settings #availability').data('excluded-dates'),
      exam_name: $('.adjust-study-plan-settings #availability').data('exam-name'),
      exam_dates: $('.adjust-study-plan-settings #availability').data('exam-dates')
    });

    set_study_start_date_for_calendar();

    $('#user_study_plan_setting_attributes_study_start_date').pickadate('picker').on('set', function (event) {
      set_study_start_date_for_calendar();
    });
  }

  $('.adjust-study-plan-course .course-selection').removeClass('pe-none');
});

$(document).on('click', '.adjust-study-plan-course .course-card-wrapper .study-plan-course-card', function () {
  const $adjust_plan_form = $('#customize-study-plan-form .adjust-plan');
  const $verbal_section = $adjust_plan_form.find('.verbal-section a.update-form-field-value');

  $('#customize-study-plan-form .study-plan').attr('data-legacy-course-enabled', $(this).hasClass('legacy-course'));
  $adjust_plan_form.find('a.update-form-field-value').removeClass('active');

  var active_course = $(this).hasClass('legacy-course') ? '.for-legacy' : '.for-default';

  if ($(this).hasClass('legacy-course')) {
    $verbal_section[0].dataset.value = 'verbal,ir,awa';

    $('.recommendation-default-content').addClass('d-none');
    $('.recommendation-legacy-content').removeClass('d-none');
  } else {
    $verbal_section[0].dataset.value = 'verbal,bwa';

    $('.recommendation-default-content').removeClass('d-none');
    $('.recommendation-legacy-content').addClass('d-none');
  }

  $adjust_plan_form.find(`${active_course} a.update-form-field-value:first`).click();
  form_modified = true;
});

$(document).on('click', '.customize-study-plan-back-btn, #user-navbar .nav-link:not(.dropdown-toggle):not(.topics-search-icon), #user-navbar .navbar-brand, #user-navbar .dropdown-menu a.dropdown-item', function (event) {
  if (form_modified && window.location.href.includes('users/adjust_study_plan')) {
    open_exit_modal(event);
  }
});

$(document).on('change', '#customize-study-plan-form input, #customize-study-plan-form textarea', function () {
  form_modified = true;
});

$(document).on('click', '#customize-study-plan-form a.update-form-field-value, #customize-study-plan-form #score-bar button', function () {
  form_modified = true;
});

function open_exit_modal (event) {
  event.preventDefault();
  $('#loading-overlay').hide();
  $('#loading-overlay-dark.study-plan').hide();

  $('#exit-study-plan-modal').modal('show');
  $('#link_href_url').val(event.target.closest('a').href);
}

$(document).on('click', '.guide-item#dropdown-guide-year', function () {
  const selected_year = $(this).data('value');
  const label = $(this).text().trim();
  const form = $(this).closest('form');
  form.find('.selected-guide-year').val(selected_year);
  $('#dropdownGuide .selected-guide-label').text(label);

  $('.guide-item').removeClass('selected');
  $(this).addClass('selected');
});
