function initialize_time_charts () {
  $('.time-chart').each(function () {
    var $chart = $(this);
    var series_a = $chart.data('series');

    $.each(series_a, function (index, element) {
      if (element == -1) {
        series_a[index] = null;
      }
    });

    bar = new Chartist.Line(this, {
      labels: $chart.data('labels'),
      series: [series_a]
    }, {
      high: Math.max.apply(Math, $chart.data('series')),
      low: 0,
      showArea: true,
      showPoint: true,
      fullWidth: true,
      chartPadding: { right: 11, left: -30, top: 10, bottom: -13 },
      height: '68px',
      axisY: {
        onlyInteger: true,
        showGrid: false,
        labelInterpolationFnc: function (value, index) {
          return null;
        }
      },
      axisX: {
        showGrid: false,
        labelInterpolationFnc: function (value, index) {
          if ($chart.data('labels').length <= 7) {
            return value;
          } else if (index === 0) {
            return value;
          } else if (index == 29 && series_a.length == 31) {
            return null;
          } else if (index == series_a.length - 1) {
            return value;
          } else {
            return (index + 1) % 5 === 0 ? value : null;
          }
        }
      },
      lineSmooth: false
    });
  });
}

$('.course-stats .nav-pills .nav-item').click(function (event) {
  event.stopImmediatePropagation();
  event.preventDefault();
});

$(document).on('click', '.copy-button', function () {
  const $button = $(this);
  const target = $($button.data('clipboard-target')).text().trim();

  if (target.length === 0) {
    return;
  }
  navigator.clipboard.writeText(target).then(() => {
    // eslint-disable-next-line no-undef
    custom_message('success', 'Link copied to your clipboard');
  });
});

$(document).ready(function () {
  function update_instructor_details ($tab) {
    var instructor_title = $tab.data('title');
    var instructor_description = $tab.data('description');
    $('#ondemand-office-hour-modal .instructor-details h6').text(instructor_title);
    $('#ondemand-office-hour-modal .instructor-details p').html(instructor_description);
    $('#ondemand-office-hour-modal .section-tab-group').removeClass('active-tab');
    $tab.closest('.section-tab-group').addClass('active-tab');
  }

  var first_instructor = $('#ondemand-office-hour-modal .section-tab-group:first-child .instructor-tab');
  if (first_instructor.length) {
    update_instructor_details(first_instructor);
  }

  $(document).on('click', '#ondemand-office-hour-modal .instructor-tab', function (event) {
    event.preventDefault();
    update_instructor_details($(this));
  });

  adjust_width_on_resize();
});

$(window).on('resize', function () { adjust_width_on_resize(); });

function adjust_width_on_resize () {
  const scroll_full_box = $('#ondemand-office-hour-modal .scroll-full-box');
  if (scroll_full_box.length) {
    const section_tab_groups = scroll_full_box.find('.section-tab-group');
    let total_width = 0;
    section_tab_groups.each(function () {
      total_width += $(this).outerWidth(true);
    });
    scroll_full_box.css('min-width', `${total_width}px`);
  }
}

$('#ondemand-office-hour-modal').on('shown.bs.modal', function () {
  adjust_width_on_resize();
});

$('#ondemand-office-hour-modal').on('hidden.bs.modal', function () {
  adjust_width_on_resize();
});
