$(document).ready(function () {
  $('#cohort-search-form .search-input').on('input', function () {
    clearTimeout($.data(this, 'timer'));
    var wait = setTimeout(function () {
      $('#cohort-search-form').submit();
    }, 500);
    $(this).data('timer', wait);
  });

  if ($('#search-video-input').length > 0) {
    set_responsive_placeholder();
    $(window).on('resize', set_responsive_placeholder);
  }

  if ($('.recorded-video-list').length > 0) {
    initialize_cohort_video_repository();
  }

  if ($('.instructor-and-topic-filter-group').length > 0) {
    initialize_cohort_video_library();
  }
});

function set_responsive_placeholder () {
  const search_input = $('#search-video-input');

  if (window.matchMedia('(max-width: 767px)').matches) {
    search_input.attr('placeholder', 'Search any topic');
  } else {
    search_input.attr('placeholder', 'Search any Quant, Verbal or Data Insights topic');
  }
}

function initialize_cohort_video_repository () {
  var mixer = mixitup('.recorded-video-list', {
    selectors: {
      target: '.filter-cohort-video-list'
    }
  });

  var delay = (function () {
    var timer = 0;
    return function (callback, ms) {
      clearTimeout(timer);
      timer = setTimeout(callback, ms);
    };
  })();

  const $result_count = $('#cohort-video-result-count');

  $('#search-topic-input').keyup(function () {
    delay(function () {
      var input_text = $('#search-topic-input').val().toLowerCase();
      var matching = [];

      if (input_text.length > 0) {
        $('.filter-cohort-video-list').each(function () {
          var item_name = $(this).data('online-class-session-description').toLowerCase();

          if (item_name.includes(input_text)) {
            matching.push(this);
          }
        });
        mixer.filter(matching).then(function (state) {
          state.show.forEach(function (item) {
            $(item).addClass('filtered-data');
          });

          $result_count.text(`RESULTS: ${state.show.length}`);
        });
      } else {
        mixer.filter('all').then(function (state) {
          state.show.forEach(function (item) {
            $(item).removeClass('filtered-data');
          });

          $result_count.text('ALL SESSIONS');
        });
      }
    }, 200);
  });
}

$(document).on('shown.bs.modal', '.online-class-session-recording', function () {
  var modal = $(this);

  if (modal.closest('.classes-sessions-detail-sections').length) {
    return;
  }

  var video_wrapper = modal.find('.video-wrapper .cohort-session-video');
  var player_id = video_wrapper.attr('id').replace('cohort-video-player-', '');
  if (!player_id) return;

  var iframe = $('#cohort-video-player-' + player_id);
  var player = new Vimeo.Player(iframe);

  var current_position = 0;

  player.on('timeupdate', function (data) {
    current_position = data.seconds;
    video_duration = data.duration;

    if (current_position >= video_duration) {
      flag_url = modal.data('flag-url');
      $.ajax({
        url: flag_url,
        type: 'post',
        dataType: 'script',
        success: function () {
          modal.closest('.list-group').find('.indicator, .status').addClass('completed');
        }
      });
    }
  });
});

$('.bookmark-icon-link').on('click', function (event) {
  const target_id = $(this).data('session-id');
  const toggle_class = $(this).hasClass('recording-bookmark-session-btn') ? `.online-class-session-bookmark-${target_id}` : `.recording-modal-bookmark-${target_id}`;

  const $target = $(toggle_class);
  $target.toggleClass('flagged');
});

$(document).on('click', ' #instructor-list .instructor-card', function (event) {
  if ($(event.target).closest('.instructor-bio-link').length) return;

  var url = $(this).data('cohort-video-repository-path');
  if (url) {
    window.location.href = url;
  }
});

$(document).on('click', '#cohort-video-card', function (event) {
  const $target = $(event.target);
  if ($target.closest('.delete-bookmark').length > 0 || ($target.closest('.bookmark-icon-link').length > 0)) {
    return;
  }
  var target_modal = $(this).data('bs-target');
  if (target_modal != '') {
    const online_class_recording_modal = new bootstrap.Modal(target_modal);
    online_class_recording_modal.show();
  }
});

function initialize_cohort_video_library () {
  var mixer = mixitup('.instructor-and-topic-filter-group', {
    selectors: {
      target: '.filter-library-cards'
    }
  });

  var delay = (function () {
    var timer = 0;
    return function (callback, ms) {
      clearTimeout(timer);
      timer = setTimeout(callback, ms);
    };
  })();

  const $result_count = $('#result-count');

  $('#search-video-input').keyup(function () {
    delay(function () {
      var input_text = $('#search-video-input').val().toLowerCase();
      var matching = [];

      if (input_text.length > 0) {
        $('.filter-library-cards').each(function () {
          var item_name = $(this).data('online-class-session-description');

          if (item_name) {
            item_name = item_name.toLowerCase();

            if (item_name.includes(input_text)) {
              matching.push(this);
            }
          }
        });

        mixer.filter(matching).then(function (state) {
          $('#instructor-list').addClass('invisible-container');
          $('#cohort-library-video-list').removeClass('invisible-container');

          $result_count.text(`RESULTS: ${state.show.length}`);
        });
      } else {
        $('#instructor-list').removeClass('invisible-container');
        $('#cohort-library-video-list').addClass('invisible-container');

        mixer.filter('all').then(function (state) {
          $('#instructor-list').removeClass('invisible-container');
          $('#cohort-library-video-list').addClass('invisible-container');
          $result_count.text('TTP INSTRUCTORS');
        });
      }
    }, 500);
  });
}
