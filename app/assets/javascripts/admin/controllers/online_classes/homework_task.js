/* global main_builder */

var homework_task = {
  setup: function () {
    this.bind_events();
  },

  bind_events: function () {
    var self = this;

    $(document).on('change', '.task-type-select-id', function () {
      const option = $(this).val();
      const section = $('#add-homework-task-modal .nav.nav-pills li.nav-item a.nav-link.active').data('section');
      var label_text = option === 'custom_test' ? 'Problems selected:' : 'Tests selected:';
      $('.selected-count-label').text(label_text);
      self.load_task_type(option, section);

      $('#add-homework-task-modal .selected-count-number').text('');
    });

    $(document).on('change', '.problems-modal-chapter-id', function () {
      const task_type = $('.task-type-select-id').val();
      if ($('#chapter_id').val() == '') {
        $('.search-problems').val('');
        $('.input-group').addClass('d-none');
        $('.search-input').removeClass('show-search-bar');
      } else {
        $('.input-group').removeClass('d-none');
        $('.search-input').addClass('show-search-bar');
        $('.search-problems').val('');
      }

      $('.search-problems').attr('placeholder', task_type === 'custom_test' ? 'Search an example on this chapter' : 'Search on this chapter');

      if (task_type == 'chapter_test') {
        self.search_by_chapter_test();
      } else {
        self.search_problem_topic_by_chapter();
      }
    });

    $(document).on('click', '.button-search', function (e) {
      const task_type = $('.task-type-select-id').val();
      if (task_type == 'chapter_test') {
        self.search_by_chapter_test();
      } else {
        self.search_problem_topic_by_chapter();
      }
    });

    $(document).on('click', '.close-modal-button', function () {
      self.reset_problems_search();
      self.load_chapters_by_section('quant');
    });

    $(document).on('click', '#add-homework-task-modal .diagnostic-problems-table tr.problem-description', function (e) {
      var problem_index = $(this).find(':checkbox').attr('id').match(/\d+/)[0];
      var current_checkbox = $(`.problem-checkbox-field#problem-index-${problem_index}`);
      var problem_id = current_checkbox.attr('data-problem-id');

      var problem_ids = [];
      if (current_checkbox.is(':checked')) {
        $(this).addClass('checked');
        current_checkbox.prop('checked', true);
        problem_ids.push(problem_id);
      } else {
        $(this).removeClass('checked');
      }

      var selected_problem_ids = $('.problems-id-input-field').val() || 0;
      if (selected_problem_ids != 0) {
        selected_problem_ids.split(',').forEach(function (p_id) {
          if (p_id != problem_id) {
            problem_ids.push((p_id));
          }
        });
      }

      $('.problems-id-input-field').val(problem_ids);
      var input_values = $('.problems-id-input-field').val();
      var total_checked_problems = input_values == '' ? 0 : input_values.split(',').length;
      total_checked_problems = total_checked_problems == 0 ? '' : total_checked_problems;
      $('#add-homework-task-modal .selected-count span').text(total_checked_problems);

      if (total_checked_problems > 0) {
        $('.btn-save-problem').removeAttr('disabled');
      } else {
        $('.btn-save-problem').attr('disabled', 'disabled');
      }
    });

    $(document).on('click', '#add-homework-task-modal .btn-save-problem', function () {
      if ($('.problems-id-input-field').val() == '' && $('.problem-checkbox-field:checked').length == 0) {
        $('.problems-modal-chapter-id').val('');
        $('.problems-modal-chapter-id').prop('selectedIndex', 0);

        self.reset_problems_search();
      } else if ((!self.validate_problems_fields() && $('.problems-id-input-field').val() == '')) {
        self.reset_problems_search();

        $('.problems-modal-chapter-id').val('');
        $('.problems-modal-chapter-id').prop('selectedIndex', 0);
        $('.btn-save-problem').removeAttr('disabled');
      } else {
        var url = $(this).attr('data-add-problems-path');

        self.add_problems_to_module(url);
        self.reset_problems_search();
      }
    });

    $(document).on('click', '#add-homework-task-modal .nav.nav-pills li.nav-item', function () {
      var section = $(this).find('a.nav-link.active').data('section');
      self.load_chapters_by_section(section);
    });

    $(document).on('click', '.add-another-problem-btn', function () {
      $('.search-problems').val('');
      $('.input-group').addClass('d-none');
      $('.search-input').removeClass('show-search-bar');
      $('#add-homework-task-modal').attr('data-mode', 'add');
      self.reset_problems_search();
    });

    $(document).on('click', '.update-task-link', function () {
      var task_data = $(this);
      var modal = $('#add-homework-task-modal');
      var url = task_data.data('edit-path');

      task_type = task_data.data('task-type');
      task_data = task_data.data('task-data');
      task_data_count = task_data.length;

      modal.find('.task-type-select-id').val(task_type);
      modal.find('.problems-id-input-field').val(task_data);
      modal.find('.selected-count-number').text(task_data_count);
      modal.attr('data-mode', 'edit');
      modal.find('.btn-save-problem').text('SAVE CHANGES');

      if (task_data_count > 0) {
        modal.find('.btn-save-problem').addAttr('disabled');
      }

      $.get({ url: url, data: { type: task_type, task_data: task_data } });
    });

    $(document).on('click', '.detail-icon', function (e) {
      e.stopPropagation();
    });

    $(document).ready(function () {
      var problem_id = $('.problems-id-input-field').val();

      if (problem_id != '') {
        if (self.validate_problems_fields()) {
          self.search_problem_topic_by_chapter();
        }
        $(document).on('change', '.problems-modal-chapter-id', function () {
          self.search_problem_topic_by_chapter();
        });
        if ($('.problem-chapter-id-hidden').val() != '') {
          $('.problems-modal-chapter-id').val($('.problem-chapter-id-hidden').val());
        }
      } else if (problem_id == '') {
        self.load_chapters_by_section('quant');
      }
    });
  },

  search_problem_topic_by_chapter: function () {
    $('#loading-overlay').show();
    var chapter_id_param = $('.problems-modal-chapter-id').val();
    var search_key_param = $('.search-problems').val();
    var task_type = $('.task-type-select-id').val();
    var url = $('#add-homework-task-modal').data('homework-task-type-path');

    $.getScript({ url: url, data: { chapter_id: chapter_id_param, search_key_param: search_key_param, type: task_type } });
  },

  search_by_chapter_test: function () {
    $('#loading-overlay').show();
    var chapter_id_param = $('.problems-modal-chapter-id').val();
    var search_key_param = $('.search-problems').val();
    var task_type = $('.task-type-select-id').val();
    var url = $('#add-homework-task-modal').data('chapter-test-path');

    $.getScript({ url: url, data: { chapter_id: chapter_id_param, search_key_param: search_key_param, type: task_type } });
  },

  load_chapters_by_section: function (section) {
    $('#loading-overlay').show();
    var url = $('#add-homework-task-modal').data('chapters-path');
    $.get({ url: url, dataType: 'script', data: { section: section } });
  },

  load_task_type: function (option, section) {
    $('#loading-overlay').show();
    var url = $('#add-homework-task-modal').data('chapters-path');
    $.get({ url: url, dataType: 'script', data: { task_type: option, section: section } });
  },

  validate_problems_fields: function () {
    var chapter_id = $('.problems-modal-chapter-id').val();

    if (chapter_id == '' || chapter_id == undefined) {
      return false;
    } else {
      return true;
    }
  },

  reset_problems_search: function () {
    $('.empty-results').removeClass('d-none');
    $('.table-results').addClass('d-none');
    $('.table-results').html('');
    $('.selected-count').addClass('d-none');
    $('.problems-found').html('');
    $('.empty-result-message').html('Select a chapter to start');
    $('.btn-save-problem').attr('disabled', 'disabled');
    $('.problems-id-input-field').val('');
    $('.problem-checkbox-field:checked').each(function () {
      $(this).prop('checked', false);
    });

    $('.problems-modal-chapter-id').val('');
    $('#add-homework-task-modal .btn-save-problem').text('CREATE TASK');
    $('a.nav-link').removeClass('active');
    $("a[data-section='quant']").addClass('active');
    $('.selected-count-number').text('');
  },

  add_problems_to_module: function (url) {
    const mode = $('#add-homework-task-modal').attr('data-mode') || 'add';
    var problem_ids_param = $('.problems-id-input-field').val();
    var task_type = $('.task-type-select-id').val();

    $('#loading-overlay').show();

    $.post({ url: url, dataType: 'script', data: { problem_ids: problem_ids_param, task_type: task_type, mode: mode } });
  }

};

$(document).ready(function () {
  if (main_builder.is_valid_view()) {
    homework_task.setup();
  }
});
