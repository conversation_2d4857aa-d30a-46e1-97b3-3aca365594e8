// This is a manifest file that'll be compiled into admin.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, vendor/assets/javascripts,
// or vendor/assets/javascripts of plugins, if any, can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of
// the compiled file.
//
// WARNING: THE FIRST BLANK LINE MARKS THE END OF WHAT'S TO BE PROCESSED, ANY BLANK LINE SHOULD
// GO AFTER THE REQUIRES BELOW.
/*
= require jquery3
= require jquery_ujs
= require jquery.remotipart
= require jquery-ui/sortable
= require jquery-ui/draggable
= require awesome-checkbox
= require cocoon
= require chartist.min
= require popper
= require bsmultiselect.min

//   NOTE: These ckeditor assets need to be included in this order.
= require 'ckeditor/init'
= require 'ckeditor/ckeditor'
= require 'ckeditor/plugins/widget/plugin'
= require 'ckeditor/plugins/widget/lang/en'
= require 'ckeditor/plugins/lineutils/plugin'
= require 'ckeditor/plugins/specialchar/dialogs/lang/en'
= require 'ckeditor/plugins/specialchar/dialogs/specialchar'
= require 'ckeditor/plugins/autogrow/plugin'
= require jquery.knob
= require jquery.remotipart

= require assets/math
= require admin/main.js
= require admin/helpers/ckeditor.js
= require admin/chapters.js
= require admin/problems.js
= require admin/diagnostic_problems.js
= require admin/daily_questions.js
= require admin/examples.js
= require admin/en_translations.js
= require admin/topics.js
= require admin/tracks.js
= require admin/users.js
= require admin/messages.js
= require admin/failure_reasons.js
= require admin/online_classes.js
= require admin/videos.js
= require admin/word_definitions.js
= require admin/sale_banners.js
= require admin/ondemand.js

= require admin/components/dropzone

= require admin/controllers/topics/builder.js
= require admin/controllers/topics/example_tags_modal.js
= require admin/controllers/problems/builder.js
= require admin/controllers/problems/tags_modal.js
= require admin/controllers/problems/examples_modal.js
= require admin/controllers/problems/multiple_questions.js
= require admin/controllers/problems/question_types/option_list.js
= require admin/controllers/problems/question_types/data_sufficiency.js
= require admin/controllers/problems/question_types/quantitative_comparison.js
= require admin/controllers/problems/question_types/numeric_entry.js
= require admin/controllers/problems/question_types/reading_comprehension.js
= require admin/controllers/problems/question_types/targeted_practice_questions.js
= require admin/controllers/problems/question_types/text_completion.js
= require admin/controllers/problems/question_types/graphics_interpretation.js
= require admin/controllers/diagnostic_problems/problems_modal.js
= require admin/controllers/online_classes/multiple_sessions.js
= require admin/controllers/online_classes/homework_task.js
= require admin/controllers/leads.js
= require admin/controllers/marketing_modals.js
= require admin/controllers/videos/video_sections.js
= require admin/controllers/problems/question_types/empty_question.js
= require admin/controllers/word_definitions/multiple_other_forms.js
= require admin/controllers/problems/question_types/table_analysis.js
= require admin/controllers/problems/question_types/multi_source_reasoning.js
= require admin/controllers/problems/question_types/two_part_analysis.js
= require admin/controllers/leads/preview.js

*/

// Placeholder functions (Remove/Relocate when possible)
function determine_gre_stem_alignment () {}; // eslint-disable-line no-unused-vars
function load_notes () {}; // eslint-disable-line no-unused-vars

$(document).ready(function () {
  if ($('.card-body.course').find('.nav-link a').length == 0) {
    $('.card-body.course').remove();
  }

  if ($('.card-body.settings').find('.nav-link a').length == 0) {
    $('.card-body.settings').remove();
  }
});
