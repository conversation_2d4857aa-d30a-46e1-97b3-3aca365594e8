# rubocop:disable Security/Open, Style/SoleNestedConditional, Naming/PredicateName

class RecurlyProxy
  TRIAL_PLAN_CODES = (
    [
      Setting.for("recurly_5days_plan_code"),
      Setting.for("recurly_5days_ondemand_plan_code"),
      Setting.for("recurly_free_5days_plan_code"),
      Setting.for("recurly_free_trial_plan_code"),
      Setting.for("recurly_free_trial_gc_plan_code")
    ] + Setting.for("recurly_other_trial_plan_codes").to_s.split(",").map(&:strip)
  ).uniq.compact

  TRIAL_ONDEMAND_PLAN_CODE = Setting.for("recurly_5days_ondemand_plan_code")

  PARTNER_ORG_PLAN_CODES = Setting.for("recurly_partner_org_plan_codes").to_s.split(",").map(&:strip).uniq.compact

  ONLINE_CLASS_PLAN_CODE = Setting.for("recurly_online_class_plan_code")

  DEFAULT_ONDEMAND_PLAN_CODE = Setting.for("recurly_default_ondemand_plan_code")

  ONDEMAND_PLAN_CODES = ([DEFAULT_ONDEMAND_PLAN_CODE] + Setting.for("recurly_ondemand_plan_codes").to_s.split(",").map(&:strip)).uniq.compact

  TUTORING_PLAN_CODES = Setting.for("recurly_tutoring_plan_codes").to_s.split(",").map(&:strip).uniq.compact

  COMPLIMENTARY_SUBSCRIPTIONS = {
    "#{EXAM_NAME}-tutoring-5-hour" => Setting.for("recurly_2months_plan_code"),
    "#{EXAM_NAME}-tutoring-10-hour" => Setting.for("recurly_4months_plan_code"),
    "#{EXAM_NAME}-tutoring-20-hour" => DEFAULT_ONDEMAND_PLAN_CODE.present? ? DEFAULT_ONDEMAND_PLAN_CODE : Setting.for("recurly_6months_plan_code"),
    "#{EXAM_NAME}-tutoring-40-hour" => DEFAULT_ONDEMAND_PLAN_CODE.present? ? DEFAULT_ONDEMAND_PLAN_CODE : Setting.for("recurly_6months_plan_code")
  }

  PLAN_CODES = (
    [
      Setting.for("recurly_2months_plan_code"),
      Setting.for("recurly_4months_plan_code"),
      Setting.for("recurly_4months_btg_plan_code"),
      Setting.for("recurly_6months_plan_code"),
      Setting.for("recurly_6months_btg_plan_code"),
      Setting.for("recurly_monthly_plan_code")
    ] + TRIAL_PLAN_CODES + PARTNER_ORG_PLAN_CODES + ONDEMAND_PLAN_CODES + Setting.for("recurly_other_plan_codes").to_s.split(",").map(&:strip)
  ).uniq.compact

  ADMISSIONS_PLAN_CODES = Setting.for("recurly_admissions_plan_codes").to_s.split(",").map(&:strip).uniq.compact
  ALL_VALID_PLAN_CODES = (ADMISSIONS_PLAN_CODES + PLAN_CODES + TUTORING_PLAN_CODES + [ONLINE_CLASS_PLAN_CODE]).flatten.compact.uniq.freeze
  ALLOWED_STATES = %w(active in_trial canceled future).freeze

  def initialize(owner)
    @owner = owner
  end

  def active?
    active_subscription.present?
  end

  def onlineclass_subscription?
    @onlineclass_subscription_activated ||= active_subscriptions.blank? && onlineclass_subscriptions.any?
  end

  def current_period_ends_at
    active_subscription.current_period_ends_at
  end

  def has_plan?(plan_code)
    (active_subscriptions + active_tutoring_subscriptions + onlineclass_subscriptions).select do |subscription|
      subscription.plan.plan_code == plan_code
    end.any?
  end

  def recurly_email
    account.email
  end

  def recurly_login_token
    account.hosted_login_token
  end

  def recurly_first_name
    account.first_name
  end

  def recurly_last_name
    account.last_name
  end

  def billing_info
    return nil unless account.present?

    account.billing_info
  end

  def recurly_current_plan
    subscription = current_subscription
    return unless active? and subscription

    subscription.plan.plan_code
  end

  def recurly_current_plan_cost
    subscription = current_subscription
    return unless active? and subscription

    subscription.unit_amount_in_cents / 100
  end

  def current_subscription
    @current_subscription ||= active_subscriptions.find do |subscription|
      subscription.current_term_started_at <= Time.zone.now
    end
  end

  def future_subscription
    @future_subscription ||= active_subscriptions.find do |subscription|
      subscription.state == "future"
    end
  end

  def recurly_current_subscription_total_price
    if (invoice = current_subscription.invoice)
      invoice.total_in_cents.to_f / 100
    else
      get_discounted_price_for_subscription(current_subscription)
    end
  end

  def recurly_future_subscription_total_price
    get_discounted_price_for_subscription(future_subscription, true)
  end

  def invoices
    return [] if account.blank?

    @invoices ||= account.invoices
  end

  def self.create_recurly_coupon_for_user(coupon_code)
    coupon = Recurly::Coupon.find(coupon_code)

    special_coupon = coupon.generate(1)
    special_coupon.first
  rescue Recurly::Resource::NotFound, Recurly::API::BadRequest => e
    message = { error: e.message }

     UserMailer.personal_mail_dev_logs(nil, "Coupon #{coupon_code} Not Created #{ENV['APP_NAME']}", message).deliver if ENV["APP_NAME"].to_s.include?("-production")
    OpenStruct.new(persisted?: false, errors: { base: "Coupon Not Created" })
  end

  def self.valid_plan_code?(plan_code)
    PLAN_CODES.include?(plan_code) or TUTORING_PLAN_CODES.include?(plan_code) or ONLINE_CLASS_PLAN_CODE.include?(plan_code) or ADMISSIONS_PLAN_CODES.include?(plan_code)
  end

  def self.app_plan_code?(plan_code)
    PLAN_CODES.include?(plan_code)
  end

  def self.onlineclass_code?(plan_code)
    ONLINE_CLASS_PLAN_CODE == plan_code
  end

  def self.plan(plan_code)
    Recurly::ALL_PLANS.find { |plan| plan.plan_code == plan_code }
  end

  def self.yesterday_invoices_csv(on: "modified")
    uri = URI.parse("https://#{Recurly.subdomain}.recurly.com/v2/export_dates/#{Date.current.strftime('%Y-%m-%d')}/export_files/invoices_#{on}.csv.gz")

    request = Net::HTTP::Get.new(uri)
    request.basic_auth Recurly.api_key, ""
    request.content_type = "application/xml; charset=utf-8"
    request["Accept"] = "application/xml"
    request["X-Api-Version"] = "2.29"

    req_options = {
      use_ssl: uri.scheme == "https"
    }

    response = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
      http.request(request)
    end

    doc = Nokogiri::XML(response.body)
    gz = Zlib::GzipReader.new(URI.open(URI.parse(doc.at_xpath("//download_url").text)))
    CSV.parse(gz.read)
  end

  def self.find_subscription(subscription_uuid)
    Recurly::Subscription.find(subscription_uuid)
  end

  def self.create_subscription(params)
    subscription_options = {
      plan_code: params[:subscription][:plan_code],
      currency: "USD",
      account: {
        account_code: params[:email],
        email: params[:email],
        first_name: params[:first_name],
        last_name: params[:last_name],
        billing_info: {
          token_id: params[:recurly_token]
        }
      }
    }

    subscription_options.merge!(quantity: params[:quantity]) if params[:quantity].present?
    subscription_options.merge!(coupon_code: params[:subscription][:coupon_code]) if params[:subscription][:coupon_code].present?
    subscription_options[:account][:billing_info][:three_d_secure_action_result_token_id] = params[:three_d_secure_action_result_token_id] if params[:three_d_secure_action_result_token_id].present?

    Recurly::Subscription.create(subscription_options)
  rescue Recurly::Transaction::ThreeDSecureError => e
    OpenStruct.new(require_three_d_secure: true, token: e.three_d_secure_action_token_id)
  rescue StandardError => e
    message = e.try(:message).present? ? e.message : "Your transaction was declined. Please use a different card or contact your bank"

    OpenStruct.new(persisted?: false, errors: { base: message })
  end

  def self.create_recurly_account_and_free_subscription(user)
    account = Recurly::Account.create(account_code: user.email, email: user.email, first_name: user.first_name, last_name: user.last_name)

    subscription_options = {
      plan_code: Setting.for("recurly_free_5days_plan_code"),
      currency: "USD",
      collection_method: "automatic",
      unit_amount_in_cents: 0,
      trial_ends_at: 1.minute.ago,
      account: {
        account_code: user.email,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name
      }
    }

    user.update(recurly_account_code: account.account_code)
    user.update_subscription_data

    Recurly::Subscription.create(subscription_options)
  end

  def self.create_subscription_and_handle_future_ending_subscription(account, subscription_options)
    future_ending_subscription = if PLAN_CODES.include?(subscription_options[:plan_code])
      account.subscriptions
        .select { |subscription| ALLOWED_STATES.include?(subscription.state) && PLAN_CODES.include?(subscription.plan.plan_code) && subscription.current_period_ends_at >= Time.zone.now }
        .max_by(&:current_period_ends_at)
    end

    subscription = Recurly::Subscription.create(subscription_options)

    if subscription.persisted? and future_ending_subscription.present?
      future_ending_subscription.terminate(:none)

      extension_duration = (future_ending_subscription.current_period_ends_at - subscription.current_period_started_at).abs
      subscription.postpone(subscription.current_period_ends_at + extension_duration) if !ONDEMAND_PLAN_CODES.include?(subscription.plan.plan_code) or TRIAL_PLAN_CODES.include?(future_ending_subscription.plan.plan_code)
    end

    subscription
  end

  def self.create_complimentary_subscription(subscription)
    complimentary_plan_code = COMPLIMENTARY_SUBSCRIPTIONS[subscription.plan_code]
    return nil unless complimentary_plan_code.present?

    subscription_options = {
      plan_code: complimentary_plan_code,
      currency: "USD",
      collection_method: "manual",
      unit_amount_in_cents: 0,
      account: subscription.account
    }

    create_subscription_and_handle_future_ending_subscription(subscription.account, subscription_options)
  end

  def create_subscription(subscription_options)
    if subscription_options[:plan_code].in?(ONDEMAND_PLAN_CODES)
      ondemand_price, _ondemand_discount = RecurlyProxy.calculate_ondemand_price(@owner, subscription_options[:plan_code])
      subscription_options[:unit_amount_in_cents] = (ondemand_price * 100).to_i
    end

    subscription = RecurlyProxy.create_subscription_and_handle_future_ending_subscription(account, subscription_options)

    if subscription.persisted?
      @account = nil
      @active_subscriptions = nil
    else
      @errors = { base: subscription.errors.full_messages.join(", ") }
    end

    subscription
  rescue Recurly::Transaction::ThreeDSecureError => e
    OpenStruct.new(require_three_d_secure: true, token: e.three_d_secure_action_token_id)
  rescue Recurly::Resource::NotFound => e
    @errors = { base: e.message }
    nil
  rescue Recurly::API::UnprocessableEntity => e
    @errors = { base: e.message }
    nil
  end

  def update_account_card(token, three_d_secure_action_result_token_id = nil)
    @account ||= account
    billing_info = { token_id: token }
    billing_info[:three_d_secure_action_result_token_id] = three_d_secure_action_result_token_id if three_d_secure_action_result_token_id.present?
    @account.billing_info = billing_info
    @account.save
    true
  rescue Recurly::Transaction::ThreeDSecureError => e
    OpenStruct.new(require_three_d_secure: true, token: e.three_d_secure_action_token_id)
  rescue Recurly::Resource::NotFound => e
    Rails.logger.error("Error: #{e}")
    @errors = e.message
    false
  rescue Recurly::API::UnprocessableEntity => e
    Rails.logger.error("Error: #{e}")
    @errors = e.message
    false
  end

  def cancel_current_subscription
    current_subscription.cancel
    true
  rescue StandardError
    false
  end

  def terminate_current_subscription
    current_subscription.terminate
    true
  rescue StandardError
    false
  end

  def cancel_future_subscription
    future_subscription.cancel
    true
  rescue StandardError
    false
  end

  def remove_billing_info
    billing_info&.destroy
  end

  def trial_subscription?
    active? and TRIAL_PLAN_CODES.include?(active_subscription.plan.plan_code)
  end

  def calculate_discount(plan_code, coupon_code, ignore_redemptions = false, allow_inactive_state = false, quantity = 1)
    if (plan = get_plan(plan_code)) and (coupon = get_coupon(coupon_code))
      if can_redeem_coupon?(coupon, plan_code, ignore_redemptions, allow_inactive_state)
        plan_cost_cents = plan.unit_amount_in_cents.to_i
        total_cost_cents = plan_cost_cents * quantity
        total_cost = total_cost_cents.to_f / 100

        if plan_code.in?(ONDEMAND_PLAN_CODES)
          ondemand_price, _ondemand_discount = RecurlyProxy.calculate_ondemand_price(@owner, plan_code)
          plan_cost_cents = ondemand_price * 100
          total_cost_cents = plan_cost_cents * quantity
          total_cost = ondemand_price * quantity
        end

        discounted_cost = if coupon.discount_type == "dollars"
          discount_amount = coupon.discount_in_cents.to_i * quantity
          (total_cost_cents - discount_amount).to_f / 100
        else
          (total_cost_cents * (1 - (coupon.discount_percent.to_f / 100))).to_f / 100
        end

        return [total_cost, discounted_cost]
      end
    end

    false
  end

  def self.calculate_ondemand_price(user, plan_code = DEFAULT_ONDEMAND_PLAN_CODE)
    ondemand_plan_cost = plan_cost(plan_code)

    return [ondemand_plan_cost, 0.00] if user.nil? or user.current_plan.nil? or user.current_plan.in?(TRIAL_PLAN_CODES) or user.ondemand_subscription?
    return [400, ondemand_plan_cost - 400] if user.current_plan.in?(PARTNER_ORG_PLAN_CODES)

    current_plan_cost = plan_cost(user.current_plan)
    plan_duration = subscription_length(user.current_plan)
    return [ondemand_plan_cost, 0.00] if user.subscription_ends_at.to_date < Date.current or plan_duration.zero?

    remaining_months = calculate_remaining_months(user)
    ondemand_discount = [[(current_plan_cost / plan_duration) * remaining_months, current_plan_cost].min, 0].max
    ondemand_price = ondemand_plan_cost - ondemand_discount

    [ondemand_price.to_f, ondemand_discount.to_f]
  end

  def self.subscription_length(plan_code)
    return 6 if plan_code.include?("6months")
    return 4 if plan_code.include?("4months")
    return 1 if plan_code.include?("monthly") or plan_code.include?("1month")

    0
  end

  def self.calculate_remaining_months(user)
    end_date = user.subscription_ends_at.to_date
    today = Date.current

    years_diff = end_date.year - today.year
    months_diff = end_date.month - today.month
    days_diff = end_date.day - today.day

    remaining_months = (years_diff * 12) + months_diff

    if days_diff.negative?
      remaining_months -= 1
    elsif days_diff.zero? and today.day >= end_date.day
      remaining_months -= 1
    end

    case remaining_months
    when (6..12)
      6
    when (5..6)
      6
    when (4..5)
      5
    when (3..4)
      4
    when (2..3)
      3
    when (1..2)
      2
    when (0..1)
      1
    else
      0
    end
  end

  def errors
    if @errors.blank?
      @errors = begin
        account.errors["base"].join(". ")
      rescue StandardError
        nil
      end
    end
    @errors = account.errors if @errors.blank?
    @errors = "Your transaction was declined. Please use a different card or contact your bank" if @errors.blank?

    @errors
  end

  def last_transaction
    return nil unless account.present?

    @last_transaction ||= account.transactions.first
  end

  def recurly_transactions_total_value_cents
    return 0 unless account.present?

    transactions = account.transactions.all.select do |t|
      PLAN_CODES.include?(t.subscriptions.first.plan.plan_code) && t.status == "success"
    rescue StandardError
      false
    end

    transactions.map { |t| (t.action == "refund") ? (t.amount_in_cents * -1) : t.amount_in_cents }.reduce(&:+)
  end

  def available_coupon_for(plan_code)
    coupons_redeemed.select do |coupon|
      can_redeem_coupon?(coupon, plan_code, true)
    end.first.coupon_code
  rescue StandardError
    ""
  end

  def coupons_redeemed
    @coupons_redeemed ||= account.redemptions.map do |redemption|
      get_coupon(redemption.coupon_code)
    end
  rescue StandardError
    []
  end

  def self.plan_cost(plan_code)
    plan = Recurly::ALL_PLANS.find { |recurly_plan| recurly_plan.plan_code == plan_code }
    plan ? plan.unit_amount_in_cents.to_f / 100 : 0.0
  end

  private

  def account
    @account ||=
      attempt! do
        Recurly::Account.find(@owner.recurly_account_code)
      end
  end

  def active_subscription
    active_subscriptions.first
  end

  def active_subscriptions
    return [] unless account.present?

    @active_subscriptions ||=
      attempt! do
        account.subscriptions.select do |subscription|
          ALLOWED_STATES.include?(subscription.state) and PLAN_CODES.include?(subscription.plan.plan_code)
        end
      end
  end

  def active_tutoring_subscriptions
    return [] unless account.present?

    @active_tutoring_subscriptions ||=
      attempt! do
        account.subscriptions.select do |subscription|
          ALLOWED_STATES.include?(subscription.state) and TUTORING_PLAN_CODES.include?(subscription.plan.plan_code)
        end
      end
  end

  def onlineclass_subscriptions
    return [] unless account.present?

    @onlineclass_subscriptions ||=
      attempt! do
        account.subscriptions.select do |subscription|
          %w(active expired future).include?(subscription.state) and (subscription.plan.plan_code == ONLINE_CLASS_PLAN_CODE)
        end
      end
  end

  def get_coupon(coupon_code)
    Recurly::Coupon.find(coupon_code)
  rescue StandardError
    false
  end

  def can_redeem_coupon?(coupon, plan_code, ignore_redemptions, allow_inactive_state = false)
    return false if coupon.coupon_type == "bulk"

    if account and !ignore_redemptions
      account_coupon_redemptions = coupons_redeemed.map(&:coupon_code).count coupon.coupon_code

      return false if coupon.max_redemptions_per_account.present? and coupon.max_redemptions_per_account <= account_coupon_redemptions
    end
    validate_coupon_state = coupon.state == "redeemable" || (coupon.state == "inactive" && allow_inactive_state) || (coupon.redemption_resource == "account" && coupon.duration == "forever" && coupon.state == "maxed_out")

    validate_coupon_state and (coupon.plan_codes.blank? or coupon.plan_codes.include?(plan_code))
  end

  def get_discounted_price_for_subscription(subscription, allow_inactive_state = false)
    redemption = account.redemptions.last
    ignore_redemptions = redemption.present?

    if redemption.present? and can_redeem_coupon?(redemption.coupon, subscription.plan_code, ignore_redemptions, allow_inactive_state)
      calculate_discount(subscription.plan_code, redemption.coupon.coupon_code, ignore_redemptions, allow_inactive_state)[1]
    else
      subscription.unit_amount_in_cents.to_f / 100
    end
  end

  def get_plan(plan_code)
    Recurly::ALL_PLANS.find { |plan| plan.plan_code == plan_code }
  rescue StandardError
    false
  end

  def attempt!
    yield
  rescue Recurly::Resource::NotFound
    false
  rescue StandardError
    raise
  end
end

# rubocop:enable Security/Open, Style/SoleNestedConditional, Naming/PredicateName
