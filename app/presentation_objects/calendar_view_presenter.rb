# rubocop:disable Layout/LineLength

class CalendarViewPresenter
  include CalendarHelper
  DAYS = %w(Sun Mon Tuw Wed Thu Fri Sat).freeze
  START_DAY = :sunday

  delegate :content_tag, :button_tag, :link_to, :safe_join, :capture, to: :view

  attr_accessor :view, :callback, :calendar_date, :current_user

  def initialize(view, user_id, calendar_date, callback)
    @date_today = Date.current
    @view = view
    @current_user = User.find(user_id)
    @callback = callback
    @calendar_date = calendar_date
    @excluded_dates = current_user.study_plan_setting.study_dates_to_exclude.map(&:to_date)
    @exam_dates = current_user.test_dates.pluck(:next_exam_date)
    @study_start_date = current_user.study_plan_setting.study_start_date || @date_today
    @is_calendar_view_type_week = current_user.calendar_week_view_type?
  end

  def render_view
    content_tag :div, class: "border border-color-grey-6" do
      safe_join(
        [
          content_tag(:div, class: "sticky-top") do
            header
          end,
          body
        ]
      )
    end
  end

  def header
    safe_join(
      [
        content_tag(:div, class: "d-flex justify-content-between align-items-md-center flex-column flex-md-row", id: "header") do
          safe_join(
            [
              month_or_week_nav,
              content_tag(:div, class: "d-flex", id: "dropdown-wrapper") do
                safe_join(
                  [
                    task_filter_dropdown,
                    calendar_view_type_dropdown
                  ]
                )
              end
            ]
          )
        end,
        days_name_row
      ]
    )
  end

  def body
    content_tag(:div, id: "weeks-wrapper") do
      if @is_calendar_view_type_week
        week_row(weeks[0]).html_safe
      else
        weeks.map do |week|
          week_row(week)
        end.join.html_safe
      end
    end
  end

  def month_or_week_nav
    content_tag(:div, class: "d-flex align-items-center", id: "month-nav") do
      safe_join(
        [
          link_to(
            I18n.t("user.study_plan.modals.study_plan_settings.availability.calendar.header.today"),
            Rails.application.routes.url_helpers.study_plan_path(date: @date_today),
            class: "btn btn-outline-secondary show-overlay",
            id: "date-today",
            remote: true
          ),
          content_tag(:div, class: "d-flex") do
            safe_join(
              [
                link_to(Rails.application.routes.url_helpers.study_plan_path(date: @is_calendar_view_type_week ? calendar_date.prev_week : calendar_date.prev_month), { class: "ms-1 ms-sm-2 ms-md-3 ms-lg-4 show-overlay", id: "prev-month", remote: true, data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: "Previous #{@is_calendar_view_type_week ? 'Week' : 'Month'}" } }) do
                  content_tag(:i, nil, class: "far fa-chevron-left fs-14")
                end,
                link_to(Rails.application.routes.url_helpers.study_plan_path(date: @is_calendar_view_type_week ? calendar_date.next_week : calendar_date.next_month), { class: "ms-1 ms-sm-2 ms-md-3 ms-lg-4 show-overlay", id: "next-month", remote: true, data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: "Next #{@is_calendar_view_type_week ? 'Week' : 'Month'}" } }) do
                  content_tag(:i, nil, class: "far fa-chevron-right fs-14")
                end
              ]
            )
          end,
          content_tag(:div, "#{calendar_date.strftime('%B')} #{calendar_date.year}", id: "current-month")
        ]
      )
    end
  end

  def task_filter_dropdown
    content_tag(:div, class: "section-content d-flex mw-0", id: "task-filter-dropdown") do
      safe_join(
        [
          button_tag(class: "d-flex align-items-center dropdown-toggle", type: "button", data: { bs_toggle: "dropdown" }) do
            safe_join(
              [
                content_tag(:p, "All Tasks", class: "mb-0 text-truncate align-self-center fw-semibold"),
                content_tag(:i, nil, class: "far fa-chevron-down")
              ]
            )
          end,
          content_tag(:ul, class: "list-unstyled dropdown-flaggable-type-menu dropdown-menu") do
            safe_join(
              [
                link_to("All Tasks", "#", class: "w-100 dropdown-item active", data: { filter_type: "all", filter_name: "All Tasks" }),
                link_to("Complete Tasks", "#", class: "w-100 dropdown-item", data: { filter_type: "complete", filter_name: "Complete Tasks" }),
                link_to("Incomplete Tasks", "#", class: "w-100 dropdown-item", data: { filter_type: "incomplete", filter_name: "Incomplete Tasks" })
              ]
            )
          end
        ]
      )
    end
  end

  def calendar_view_type_dropdown
    content_tag(:div, class: "section-content d-flex mw-0 ms-4 align-items-center", id: "view-type-dropdown") do
      safe_join(
        [
          button_tag(class: "d-flex align-items-center dropdown-toggle", type: "button", data: { bs_toggle: "dropdown" }) do
            safe_join(
              [
                content_tag(:i, nil, class: "fal fa-calendar ms-1 fs-13"),
                content_tag(:p, current_user.study_plan_setting.calendar_view_type.titleize, class: "mb-0 ms-2 text-truncate align-self-center fw-semibold"),
                content_tag(:i, nil, class: "far fa-chevron-down")
              ]
            )
          end,
          content_tag(:ul, class: "list-unstyled dropdown-flaggable-type-menu dropdown-menu") do
            safe_join(
              [
                link_to("Month", Rails.application.routes.url_helpers.study_plan_setting_path(@current_user, { date: view.params[:date], study_plan_setting: { calendar_view_type: "month" } }), class: "w-100 dropdown-item show-overlay month-view #{@is_calendar_view_type_week ? '' : 'active disabled'}", remote: true, method: :put),
                link_to("Week", Rails.application.routes.url_helpers.study_plan_setting_path(@current_user, { date: view.params[:date], study_plan_setting: { calendar_view_type: "week" } }), class: "w-100 dropdown-item show-overlay week-view #{@is_calendar_view_type_week ? 'active disabled' : ''}", remote: true, method: :put)
              ]
            )
          end
        ]
      )
    end
  end

  def days_name_row
    content_tag(:div, id: "days-name-wrapper") do
      %w(sun mon tue wed thu fri sat).each_with_index.map do |day_name, index|
        if @is_calendar_view_type_week
          content_tag(:div, class: "day") do
            safe_join(
              [
                content_tag(:div, day_name, class: "name"),
                content_tag(:div, weeks[0][index].day, class: ["date", weeks[0][index] == @date_today ? "today" : ""])
              ]
            )
          end
        else
          content_tag(:div, class: "day") do
            content_tag(:div, day_name, class: "name")
          end
        end
      end.join.html_safe
    end
  end

  def week_row(week)
    content_tag(:div, class: "week") do
      week.map { |date| date_cell(date) }.join.html_safe
    end
  end

  def date_cell(date)
    tasks_for_date = grouped_calendar_tasks[date]&.select { |task| show_study_module_item?(task.study_module_item, current_user) } || []
    is_tasks_for_date_available = tasks_for_date.any?
    ondemand_videos_persist = tasks_for_date.any? { |task| ondemand_videos_in_range?(task) }
    data_attributes = { date: date.to_s }

    if @excluded_dates.include?(date)
      data_attributes[:bs_toggle] = "modal"
      data_attributes[:bs_target] = "#calendar-unavailable-study-day" unless is_tasks_for_date_available
    end

    data_attributes[:ondemand_videos] = true if ondemand_videos_persist

    content_tag(:div, capture(date, tasks_for_date, exam_score_type_calendar_task_dates, &callback), class: date_classes(date, is_tasks_for_date_available), data: data_attributes)
  end

  def date_classes(date, is_tasks_for_date_available)
    classes = ["date"]

    classes << "today" if date.today?
    classes << "inactive" if date.month != @calendar_date.month || date < @study_start_date
    classes << "past" if date < @date_today
    classes << "exam" if @exam_dates.include?(date)
    classes << "excluded" if @excluded_dates.include?(date)
    classes << "end-of-calendar" if last_calendar_date < date
    classes << "with-tasks" if is_tasks_for_date_available
    classes << "need-more-tasks" if current_user.user_calendar.study_time_left_for_date?(date)
    classes << "locked-task-modal" if is_tasks_for_date_available and current_user.ondemand_active? and current_user.user_calendar.ondemand_trial_study_modules?(date) and !current_user.access_ondemand?
    classes.empty? ? nil : classes.join(" ")
  end

  def weeks
    if @is_calendar_view_type_week
      first = calendar_date.beginning_of_week(START_DAY)
      last = calendar_date.end_of_week(START_DAY)
    else
      first = calendar_date.beginning_of_month.beginning_of_week(START_DAY)
      last = calendar_date.end_of_month.end_of_week(START_DAY)
    end
    (first..last).to_a.in_groups_of(7)
  end

  def calendar_tasks
    @calendar_tasks ||= @current_user
      .user_calendar_tasks
      .includes(
        :study_module_item,
        {
          study_module_item: [
            { topic: :lesson_tracks },
            { study_module_subsection: %i(study_module chapter) }
          ]
        }
      )
  end

  def grouped_calendar_tasks
    @grouped_calendar_tasks ||= calendar_tasks
      .group_by { |task| task.completed_at&.to_date || task.rescheduled_date || task.schedule_date }
  end

  def exam_score_type_calendar_task_dates
    @exam_score_type_calendar_tasks ||= calendar_tasks
      .where(study_module_item_id: StudyModuleItem.where(item_type: "exam_score").ids).map { |task| task.rescheduled_date || task.schedule_date }
      .compact.sort
  end

  def last_calendar_date
    @last_calendar_date ||= @grouped_calendar_tasks.keys.compact.max
  end
end

# rubocop:enable Layout/LineLength
