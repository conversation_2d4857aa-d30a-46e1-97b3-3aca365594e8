class LessonContentPresenter < BaseContentPresenter
  include ActionView::Helpers
  include ViewHelper

  attr_reader :current_user

  def initialize(lesson, user)
    @lesson = lesson
    @current_user = user
    @lessons_content_presenter = LessonNavigator.new(lesson, nil, user)
  end

  def to_html
    raw_html = @lesson.content
    process_html(raw_html).html_safe
  end

  def self.lesson_title(lesson, track)
    lesson_number = lesson.dynamic_number(track)
    "#{lesson_number}. #{formatted_lesson_name(lesson.name, track)}".html_safe
  end

  def self.lesson_subtitle(lesson, track)
    lesson_number = lesson.dynamic_number(track)
    "#{lesson_number} #{formatted_lesson_name(lesson.name, track)}".html_safe
  end

  def self.chopped_dynamic_topic_number(lesson, track)
    topic_number = lesson.dynamic_number(track) || ""
    chopped_number = topic_number.slice(topic_number.index(".").to_i + 1, topic_number.length)
    chopped_number.to_s
  end

  def self.lesson_name(lesson, current_user = nil)
    is_gmat_focus_edition = EXAM_NAME == "gmat" && current_user && !current_user.for_legacy_course
    return "#{lesson.name} for GMAT".html_safe if is_gmat_focus_edition && lesson.name == "Coordinate Geometry"

    return "GMAT #{lesson.name}".html_safe if is_gmat_focus_edition && lesson.name == "Strategy"

    lesson.name.html_safe
  end

  def self.lesson_number(lesson, track)
    lesson&.dynamic_number(track)
  end

  def self.formatted_lesson_name(name, track)
    is_gmat_focus_edition = EXAM_NAME == "gmat" && !track.for_legacy_course
    return "#{name} for GMAT".html_safe if is_gmat_focus_edition && name == "Coordinate Geometry"

    return "GMAT #{name}".html_safe if is_gmat_focus_edition && name == "Strategy"

    name
  end

  private

  def process_html(raw_html)
    raw_html = replace_img_for_asset(raw_html, @lesson.content_assets)
    raw_html = replace_example_for_partial(raw_html)
    replace_placeholders(raw_html)
  end

  def replace_example_for_partial(content)
    examples = @lesson.examples.only_parents.ordered

    # To avoid having Multi Source Problems tabs with same ids we pass row_index. If not MR we pass nothing
    # as for FITB it can make the input become disabled (see _exercise.html for more info)
    content = replace_for_partial(content, /\[\[example\]\]/, examples) do |example, index|
      if current_user.blank? or !example.excluded_tracks.include?(current_user.track)
        LessonContentPresenter.render(
          "exercises/example",
          example: example,
          content_assets: @lesson.content_assets,
          user: current_user,
          row_index: index,
          display_calculator: display_calculator?(example),
          flagged_examples_ids: (@lesson.flagged_examples_for_user(current_user).pluck(:id) if current_user),
          base_exercise: example
        )
      end
    end

    targeted_practices = @lesson.targeted_practices.only_parents.ordered

    content = replace_for_partial(content, /\[\[targeted_practice\]\]/, targeted_practices) do |targeted_practice, index|
      LessonContentPresenter.render(
        "targeted_practices/parent_targeted_practice",
        targeted_practice: targeted_practice,
        content_assets: @lesson.content_assets,
        user: current_user,
        row_index: index,
        total_count: targeted_practices.count
      )
    end

    concept_masteries = @lesson.concept_masteries.ordered

    content = replace_for_partial(content, /\[\[concept_mastery\]\]/, concept_masteries) do |concept_mastery, index|
      LessonContentPresenter.render(
        "exercises/concept_mastery",
        concept_mastery: concept_mastery,
        content_assets: @lesson.content_assets,
        user: current_user,
        row_index: index,
        display_calculator: display_calculator?(concept_mastery)
      )
    end

    glossary = @lesson.chapter.word_definitions.ordered

    content = replace_for_partial(content, /\[\[VOCAB_LIST\]\]/, glossary) do
      LessonContentPresenter.render(
        "word_definitions/vocab_glossary",
        glossary: glossary,
        lesson_number: @lesson.chapter.dynamic_number(current_user.track)
      )
    end

    must_knows = {}
    @lesson.must_knows.map { |item| must_knows[item.id] = item }

    replace_for_partial(content, /\[\[must_know=\d+\]\]/, must_knows) do |must_know|
      LessonContentPresenter.render(
        "must_knows/show",
        must_know: must_know,
        current_user: current_user
      )
    end
  end

  def replace_for_partial(content, regexp, object_list)
    (content || "").gsub(regexp).with_index do |data, index|
      if data.include?("must_know")
        order_number = data.split("=")
        index = order_number[1].split("]")[0].to_i
      end

      object = object_list[index]

      yield(object, index) if object.present?
    end
  end

  def replace_placeholders(content)
    regexp = Regexp.new(Regexp.escape("[[NEXT_TOPIC_NAME]]"))

    content.gsub(regexp) do |_data|
      if current_user.present? and @lessons_content_presenter.show_next?
        @lessons_content_presenter.next_node.name.downcase
      else
        "the next topic"
      end
    end
  end
end
