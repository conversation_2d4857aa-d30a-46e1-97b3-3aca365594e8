require "spec_helper"

RSpec.describe OnlineClassSessionDocument, type: :model do
  describe "associations" do
    it { should belong_to(:online_class_session) }
  end

  describe "validations" do
    it { should validate_presence_of(:file) }
    it { should validate_presence_of(:name) }
  end

  describe "file uploader" do
    it "mounts the uploader to the file attribute" do
      document = described_class.new
      expect(document.file).to be_an_instance_of(OnlineClassSyllabusUploader)
    end
  end
end
