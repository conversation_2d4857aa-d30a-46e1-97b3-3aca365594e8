require "spec_helper"

describe HomeworkTask, type: :model do
  let!(:online_class) { FactoryBot.create(:online_class, :skip_validate) }
  let!(:online_class_session) { FactoryBot.create(:online_class_session, online_class: online_class) }
  let!(:homework_group) { FactoryBot.create(:homework_group, online_class_session: online_class_session) }

  describe "associations" do
    it { should belong_to(:homework_group) }
  end

  describe "validations" do
    it { should validate_inclusion_of(:task_type).in_array(HomeworkTask::TASK_TYPES.values) }
  end

  describe "scopes" do
    let!(:custom_test_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:custom_test], homework_group: homework_group) }
    let!(:prereading_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:prereading], homework_group: homework_group) }

    describe ".custom_tests" do
      it "returns only custom test tasks" do
        expect(described_class.custom_tests).to include(custom_test_task)
        expect(described_class.custom_tests).not_to include(prereading_task)
      end
    end

    describe ".prereadings" do
      it "returns only prereading tasks" do
        expect(described_class.prereadings).to include(prereading_task)
        expect(described_class.prereadings).not_to include(custom_test_task)
      end
    end
  end

  describe "#generate_completion_for" do
    let(:user) { FactoryBot.create(:user) }

    context "when task_type is prereading" do
      let(:topic_1) { FactoryBot.create(:topic) }
      let(:topic_2) { FactoryBot.create(:topic) }
      let(:homework_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:prereading], data: [topic_1.id, topic_2.id], homework_group: homework_group) }

      context "when some topics are completed" do
        before do
          FactoryBot.create(:flag, user: user, flaggable: topic_1, label: "completed")
        end

        it "returns correct completion data" do
          result = homework_task.generate_completion_for(user)

          expect(result[:total]).to eq(1)
          expect(result[:completed]).to eq(1)
          expect(result[:total_tasks]).to eq(2)
          expect(result[:percentage]).to eq(50.0)
        end
      end

      context "when data is empty" do
        let(:homework_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:prereading], data: [], homework_group: homework_group) }

        it "returns zero completion data" do
          result = homework_task.generate_completion_for(user)

          expect(result[:total]).to eq(1)
          expect(result[:completed]).to eq(0)
          expect(result[:total_tasks]).to eq(0)
          expect(result[:percentage]).to eq(0)
        end
      end
    end

    context "when task_type is custom_test" do
      let(:homework_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:custom_test], homework_group: homework_group) }

      context "when no evaluation exists" do
        it "returns correct completion data" do
          result = homework_task.generate_completion_for(user)

          expect(result[:total]).to eq(1)
          expect(result[:completed]).to eq(0)
          expect(result[:percentage]).to eq(0)
        end
      end

      context "when evaluation attempt is finished" do
        let!(:evaluation) { FactoryBot.create(:user_evaluation, user: user).tap { |e| e.update!(homework_task_id: homework_task.id) } }
        let!(:evaluation_attempt) { FactoryBot.create(:evaluation_attempt, user: user, evaluation: evaluation, percentage_correct: 85, end_time: Time.current) }

        it "returns correct completion data" do
          result = homework_task.generate_completion_for(user)

          expect(result[:total]).to eq(1)
          expect(result[:completed]).to eq(1)
          expect(result[:percentage]).to eq(100)
          expect(result[:accuracy]).to eq(85)
        end
      end
    end
  end

  describe "#find_evaluation_for" do
    let(:user) { FactoryBot.create(:user) }
    let(:homework_task) { FactoryBot.create(:homework_task, homework_group: homework_group) }

    context "when no evaluation exists" do
      it "returns nil for both evaluation and evaluation_attempt" do
        evaluation, evaluation_attempt = homework_task.find_evaluation_for(user)

        expect(evaluation).to be_nil
        expect(evaluation_attempt).to be_nil
      end
    end

    context "when evaluation and attempt exist" do
      let!(:evaluation) { FactoryBot.create(:user_evaluation, user: user).tap { |e| e.update!(homework_task_id: homework_task.id) } }
      let!(:evaluation_attempt) { FactoryBot.create(:evaluation_attempt, user: user, evaluation: evaluation) }

      it "returns both evaluation and evaluation_attempt" do
        evaluation_result, evaluation_attempt_result = homework_task.find_evaluation_for(user)

        expect(evaluation_result).to eq(evaluation)
        expect(evaluation_attempt_result).to eq(evaluation_attempt)
      end
    end
  end

  describe "#generate_evaluation_link_for" do
    let(:user) { FactoryBot.create(:user) }
    let(:homework_task) { FactoryBot.create(:homework_task, homework_group: homework_group) }

    context "when no evaluation exists" do
      it "returns nil" do
        link = homework_task.generate_evaluation_link_for(user)
        expect(link).to be_nil
      end
    end

    context "when evaluation exists but no attempt" do
      let!(:evaluation) { FactoryBot.create(:user_evaluation, user: user).tap { |e| e.update!(homework_task_id: homework_task.id) } }

      before do
        allow(Rails.application.routes.url_helpers).to receive(:evaluation_start_path).with(evaluation).and_return("/evaluations/#{evaluation.id}/start")
      end

      it "returns start path" do
        link = homework_task.generate_evaluation_link_for(user)
        expect(link).to eq("/evaluations/#{evaluation.id}/start")
      end
    end
  end

  describe "session homework integration" do
    let(:user) { FactoryBot.create(:user) }

    describe "custom test homework" do
      let(:homework_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:custom_test], homework_group: homework_group) }

      context "when homework is not started" do
        it "shows correct completion status" do
          completion = homework_task.generate_completion_for(user)

          expect(completion[:total]).to eq(1)
          expect(completion[:completed]).to eq(0)
          expect(completion[:percentage]).to eq(0)
          expect(completion[:to_start]).to be false
        end
      end

      context "when homework has evaluation ready to start" do
        let!(:evaluation) { FactoryBot.create(:user_evaluation, user: user).tap { |e| e.update!(homework_task_id: homework_task.id) } }

        it "shows ready to start status" do
          completion = homework_task.generate_completion_for(user)

          expect(completion[:total]).to eq(1)
          expect(completion[:completed]).to eq(0)
          expect(completion[:percentage]).to eq(0)
          expect(completion[:to_start]).to be true
          expect(completion[:evaluation_link]).to be_present
        end
      end

      context "when homework is in progress" do
        let!(:evaluation) { FactoryBot.create(:user_evaluation, user: user).tap { |e| e.update!(homework_task_id: homework_task.id) } }
        let!(:evaluation_attempt) { FactoryBot.create(:evaluation_attempt, user: user, evaluation: evaluation, end_time: nil) }

        it "shows in progress status" do
          completion = homework_task.generate_completion_for(user)

          expect(completion[:total]).to eq(1)
          expect(completion[:completed]).to eq(0.5)
          expect(completion[:percentage]).to eq(50)
          expect(completion[:to_start]).to be false
          expect(completion[:evaluation_link]).to be_present
        end
      end

      context "when homework is completed" do
        let!(:evaluation) { FactoryBot.create(:user_evaluation, user: user).tap { |e| e.update!(homework_task_id: homework_task.id) } }
        let!(:evaluation_attempt) { FactoryBot.create(:evaluation_attempt, user: user, evaluation: evaluation, percentage_correct: 92, end_time: Time.current) }

        it "shows completed status with accuracy" do
          completion = homework_task.generate_completion_for(user)

          expect(completion[:total]).to eq(1)
          expect(completion[:completed]).to eq(1)
          expect(completion[:percentage]).to eq(100)
          expect(completion[:accuracy]).to eq(92)
          expect(completion[:to_start]).to be false
          expect(completion[:evaluation_link]).to be_present
        end
      end
    end

    describe "prereading homework" do
      let(:topic_1) { FactoryBot.create(:topic) }
      let(:topic_2) { FactoryBot.create(:topic) }
      let(:topic_3) { FactoryBot.create(:topic) }
      let(:homework_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:prereading], data: [topic_1.id, topic_2.id, topic_3.id], homework_group: homework_group) }

      context "when no topics are completed" do
        it "shows zero completion" do
          completion = homework_task.generate_completion_for(user)

          expect(completion[:total]).to eq(1)
          expect(completion[:completed]).to eq(0)
          expect(completion[:total_tasks]).to eq(3)
          expect(completion[:percentage]).to eq(0)
        end
      end

      context "when some topics are completed" do
        before do
          FactoryBot.create(:flag, user: user, flaggable: topic_1, label: "completed")
          FactoryBot.create(:flag, user: user, flaggable: topic_2, label: "completed")
        end

        it "shows partial completion" do
          completion = homework_task.generate_completion_for(user)

          expect(completion[:total]).to eq(1)
          expect(completion[:completed]).to eq(2)
          expect(completion[:total_tasks]).to eq(3)
          expect(completion[:percentage]).to eq(67.0)
        end
      end

      context "when all topics are completed" do
        before do
          FactoryBot.create(:flag, user: user, flaggable: topic_1, label: "completed")
          FactoryBot.create(:flag, user: user, flaggable: topic_2, label: "completed")
          FactoryBot.create(:flag, user: user, flaggable: topic_3, label: "completed")
        end

        it "shows full completion" do
          completion = homework_task.generate_completion_for(user)

          expect(completion[:total]).to eq(1)
          expect(completion[:completed]).to eq(3)
          expect(completion[:total_tasks]).to eq(3)
          expect(completion[:percentage]).to eq(100.0)
        end
      end
    end
  end
end
