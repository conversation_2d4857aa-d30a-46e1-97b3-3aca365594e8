require "spec_helper"

RSpec.describe OnlineClassSession, type: :model do
  describe "associations" do
    it { is_expected.to belong_to(:online_class) }
  end

  describe "validations" do
    subject { FactoryBot.build(:online_class_session) }

    it "is valid with valid attributes" do
      expect(subject).to be_valid
    end

    it "is not valid without date" do
      subject.date = nil
      expect(subject).to_not be_valid
    end

    it "is not valid without description" do
      subject.description = nil
      expect(subject).to_not be_valid
    end
  end

  describe "enums" do
    it { is_expected.to define_enum_for(:homework_status).with_values(not_ready: 0, ready_for_release: 1, released: 2) }
  end

  describe "serializations" do
    it { is_expected.to serialize(:homework_task_data).as(Array) }
  end

  context "when online class and user are referenced by online_classes_user" do
    let!(:user) { create(:member) }
    let!(:online_class) { create(:online_class, :skip_validate) }
    let!(:online_class_session_1) { create(:online_class_session, online_class: online_class) }
    let!(:online_class_session_2) { create(:online_class_session, online_class: online_class, date: online_class_session_1.date + 1.day) }
    let!(:online_classes_user) { create(:online_classes_user, online_class: online_class, user: user) }

    describe ".next_session" do
      it "returns next online class session" do
        expect([online_class_session_1, online_class_session_2]).to include(OnlineClassSession.next_session(user))
      end
    end

    describe "#starts_at" do
      it { expect(online_class_session_1.starts_at).to eq "#{online_class_session_1.date} -07:00:00 -0500" }
    end

    describe "#ends_at" do
      it { expect(online_class_session_1.ends_at).to eq "#{online_class_session_1.date} -08:00:00 -0500" }
    end

    describe "#number_of_session" do
      it do
        last_session = (online_class_session_2.date > online_class_session_1.date) ? online_class_session_2 : online_class_session_1
        expect(last_session.number_of_session).to eq 2
      end
    end

    describe "#recording_not_added?" do
      it "returns true when recording_url is blank" do
        session = build(:online_class_session, recording_url: nil)
        expect(session.recording_not_added?).to be true
      end
    end

    describe "#recording_added?" do
      it "returns true when recording_url is present and show_recording is false" do
        session = build(:online_class_session, recording_url: "1234", show_in_library: false)
        expect(session.recording_added?).to be true
      end
    end

    describe "#recording_released?" do
      it "returns true when recording_url is present and show_recording is true" do
        session = build(:online_class_session, recording_url: "1234", show_in_library: true)
        expect(session.recording_released?).to be true
      end
    end
  end
end
