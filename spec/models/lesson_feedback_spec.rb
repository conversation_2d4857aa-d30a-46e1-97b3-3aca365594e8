require "spec_helper"

RSpec.describe LessonFeedback, type: :model do
  let(:chapter) { FactoryBot.create(:chapter) }
  let(:lesson) { FactoryBot.create(:topic, chapter: chapter) }
  let(:user) { FactoryBot.create(:user) }
  let(:resolved_by_user) { FactoryBot.create(:user) }

  subject { FactoryBot.build(:lesson_feedback, lesson: lesson, user: user) }

  describe "associations" do
    it { is_expected.to belong_to(:lesson) }
    it { is_expected.to belong_to(:user) }
    it { is_expected.to belong_to(:resolved_by).class_name("User") }
    it { is_expected.to have_many(:messages).dependent(:destroy) }
  end

  describe "validations" do
    it { is_expected.to validate_presence_of(:lesson) }
    it { is_expected.to validate_presence_of(:user) }
    it { is_expected.to validate_presence_of(:score) }
    it { is_expected.to validate_numericality_of(:score).is_greater_than_or_equal_to(1).is_less_than_or_equal_to(5) }
  end

  describe "scopes" do
    let!(:unresolved_feedback) { FactoryBot.create(:lesson_feedback, lesson: lesson, status: "unresolved") }
    let!(:resolved_feedback) { FactoryBot.create(:lesson_feedback, lesson: lesson, status: "resolved") }

    it "filters by comment" do
      feedback_with_comment = FactoryBot.create(:lesson_feedback, lesson: lesson, comment: "Some feedback")
      feedback_without_comment = FactoryBot.create(:lesson_feedback, lesson: lesson, comment: "")

      expect(LessonFeedback.filter_by_comment("Feedback")).to include(feedback_with_comment)
      expect(LessonFeedback.filter_by_comment("No Feedback")).to include(feedback_without_comment)
    end

    it "filters by status" do
      expect(LessonFeedback.filter_by_status("Resolved")).to include(resolved_feedback)
      expect(LessonFeedback.filter_by_status("Unresolved")).to include(unresolved_feedback)
    end

    it "filters by chapter" do
      other_chapter = FactoryBot.create(:chapter)
      feedback_in_other_chapter = FactoryBot.create(:lesson_feedback, lesson: FactoryBot.create(:topic, chapter: other_chapter))

      expect(LessonFeedback.by_chapter(chapter.id)).to include(unresolved_feedback, resolved_feedback)
      expect(LessonFeedback.by_chapter(other_chapter.id)).to include(feedback_in_other_chapter)
    end

    it "filters by status string" do
      expect(LessonFeedback.by_status("")).to include(unresolved_feedback, resolved_feedback)
      expect(LessonFeedback.by_status("unresolved")).to include(unresolved_feedback)
      expect(LessonFeedback.by_status("resolved")).to include(resolved_feedback)
    end

    it "filters by user name or user email" do
      user_feedback = FactoryBot.create(:lesson_feedback, user: user)
      other_user_feedback = FactoryBot.create(:lesson_feedback, user: FactoryBot.create(:user))

      expect(LessonFeedback.by_user_name_or_user_email(user.name)).to include(user_feedback)
      expect(LessonFeedback.by_user_name_or_user_email(user.email)).to include(user_feedback)
      expect(LessonFeedback.by_user_name_or_user_email("nonexistent")).not_to include(user_feedback)
      expect(LessonFeedback.by_user_name_or_user_email("")).to include(user_feedback, other_user_feedback)
    end

    it "filters by negative scores" do
      negative_feedback = FactoryBot.create(:lesson_feedback, score: 1)
      positive_feedback = FactoryBot.create(:lesson_feedback, score: 4)

      expect(LessonFeedback.negative_scores).to include(negative_feedback)
      expect(LessonFeedback.negative_scores).not_to include(positive_feedback)
    end

    it "filters by positive scores" do
      negative_feedback = FactoryBot.create(:lesson_feedback, score: 1)
      positive_feedback = FactoryBot.create(:lesson_feedback, score: 4)

      expect(LessonFeedback.positive_scores).to include(positive_feedback)
      expect(LessonFeedback.positive_scores).not_to include(negative_feedback)
    end

    it "filters by score" do
      positive_feedback = FactoryBot.create(:lesson_feedback, score: 4)
      negative_feedback = FactoryBot.create(:lesson_feedback, score: 1)

      expect(LessonFeedback.by_score("positive")).to include(positive_feedback)
      expect(LessonFeedback.by_score("positive")).not_to include(negative_feedback)
      expect(LessonFeedback.by_score("negative")).to include(negative_feedback)
      expect(LessonFeedback.by_score("negative")).not_to include(positive_feedback)
    end
  end

  describe "methods" do
    it "returns analytics for a lesson feedback" do
      lesson_feedback = FactoryBot.create(:lesson_feedback, lesson: lesson, created_at: 1.day.ago, score: 4)

      analytics = LessonFeedback.analytics_for(lesson_feedback.lesson, nil, nil)

      expect(analytics[:mean]).to be_a(Float)
      expect(analytics[:median]).to be_a(Integer)
      expect(analytics[:mode]).to be_an(Integer)
      expect(analytics[:total]).to eq(1)
    end

    it "returns score class for unhelpful score" do
      feedback = FactoryBot.create(:lesson_feedback, score: 1)
      expect(feedback.score_to_class).to eq("unhelpful")
    end

    it "returns score class for helpful score" do
      feedback = FactoryBot.create(:lesson_feedback, score: 4)
      expect(feedback.score_to_class).to eq("helpful")
    end

    it "marks feedback as resolved" do
      feedback = FactoryBot.create(:lesson_feedback, resolved_by: resolved_by_user)

      expect { feedback.resolved! }.to change { feedback.status }.from("unresolved").to("resolved")
      expect(feedback.resolved_at).not_to be_nil
    end

    it "marks feedback as unresolved" do
      feedback = FactoryBot.create(:lesson_feedback, status: "resolved", resolved_by: resolved_by_user)

      expect { feedback.unresolved! }.to change { feedback.status }.from("resolved").to("unresolved")
      expect(feedback.resolved_at).to be_nil
      expect(feedback.resolved_by).to be_nil
    end
  end
end
