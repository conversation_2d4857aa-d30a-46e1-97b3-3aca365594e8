require "spec_helper"

feature "Member tries to access messages user feedback  page", js: true do
  let(:user) { FactoryBot.create(:member) }

  it "renders 'not found' page" do
    login(user)
    visit user_feedback_admin_messages_path

    expect(page).to have_content(/Page not found/i)
  end
end

feature "Admin accesses the Messages user feedback page", js: true do
  let(:user) { FactoryBot.create(:admin) }

  before do
    login(user)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
  end

  it "navigates from the root path" do
    visit admin_root_path

    click_link("Messages")

    expect(page).to have_content(/Messages/i)
    expect(page).to have_current_path(user_feedback_admin_messages_path)
    expect(page).to have_selector("select#chapter_id")
    expect(page).to have_selector("select#score")
    expect(page).to have_selector("select#status")
    expect(page).to have_selector("input#query")

    within(".ttp-table.table") do
      expect(page).to have_selector("thead tr th", text: "SENT BY")
      expect(page).to have_selector("thead tr th", text: "FEEDBACK")
      expect(page).to have_selector("thead tr th", text: "STATUS")
      expect(page).to have_selector("thead tr th", text: "LAST RESOLVED BY")
      expect(page).to have_selector("thead tr th", text: "ACTIONS")
    end
  end
end

feature "Admin accesses the Home Messages tab", js: true do
  let(:user) { FactoryBot.create(:admin) }

  before do
    login(user)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
  end

  it "navigates from the root path" do
    visit admin_root_path

    click_link("Messages")

    expect(page).to have_content(/Message Center/i)

    click_link("HOME MESSAGES")

    expect(page).to have_current_path(admin_announcements_path(section: "quant"))
    expect(page).to have_selector("select#type")
    expect(page).to have_selector("input#query")
    expect(page).to have_content("SEND NEW")

    within(".ttp-table.table") do
      expect(page).to have_selector("thead tr th", text: "SENT BY")
      expect(page).to have_selector("thead tr th", text: "CONTENT")
      expect(page).to have_selector("thead tr th", text: "ACTIONS")
    end
  end

  it "accesses new announcements form modal" do
    visit admin_announcements_path(section: "quant")

    click_link("SEND NEW")

    within "#announcements-form-modal" do
      expect(page).to have_content(/New Announcement/i)
      expect(page).to have_content(/SEND TO:/i)
      expect(page).to have_content(/PLAN/i)
      expect(page).to have_content(/TRACK/i)
      expect(page).to have_content(/COURSE CONFIG/i)
      expect(page).to have_content(/TEST DATE/i)
      expect(page).to have_content(/SIGNED-UP TIME/i)
      expect(page).to have_content(/COUNTRY/i)
      expect(page).to have_selector("input#announcement_sticky")
      expect(page).to have_content(/MESSAGE/i)
    end
  end
end

feature "Non admin user accesses Message module", js: true do
  let(:user) { FactoryBot.create(:module_editor) }
  let(:role) { user.roles.find_by(name: "module_editor") }

  before do
    Permission.destroy_all
    login(user)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
  end

  context "with no permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "none", role_id: role.id)
    end

    it "renders 'not found' page" do
      visit user_feedback_admin_messages_path

      expect(page).to have_content(/Page not found/i)
    end
  end

  context "with read permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "read", role_id: role.id)
    end

    it "navigates from the user feedback page" do
      visit admin_root_path

      click_link("Messages")

      expect(page).to have_content(/Messages/i)
      expect(page).to have_current_path(user_feedback_admin_messages_path)
      expect(page).to have_selector("select#chapter_id")
      expect(page).to have_selector("select#score")
      expect(page).to have_selector("select#status")
      expect(page).to have_selector("input#query")

      within(".ttp-table.table") do
        expect(page).to have_selector("thead tr th", text: "SENT BY")
        expect(page).to have_selector("thead tr th", text: "FEEDBACK")
        expect(page).to have_selector("thead tr th", text: "STATUS")
        expect(page).to have_selector("thead tr th", text: "LAST RESOLVED BY")
        expect(page).not_to have_selector("thead tr th", text: "ACTIONS")
      end
    end
  end

  context "with edit permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "edit", role_id: role.id)
    end

    it "redirects to the message page" do
      visit user_feedback_admin_messages_path

      expect(page).to have_content(/Messages/i)
      expect(page).to have_current_path(user_feedback_admin_messages_path)
      expect(page).to have_selector("select#chapter_id")
      expect(page).to have_selector("select#score")
      expect(page).to have_selector("select#status")
      expect(page).to have_selector("input#query")

      within(".ttp-table.table") do
        expect(page).to have_selector("thead tr th", text: "SENT BY")
        expect(page).to have_selector("thead tr th", text: "FEEDBACK")
        expect(page).to have_selector("thead tr th", text: "STATUS")
        expect(page).to have_selector("thead tr th", text: "LAST RESOLVED BY")
        expect(page).to have_selector("thead tr th", text: "ACTIONS")
      end
    end

    it "accesses new announcements form modal" do
      visit admin_announcements_path(section: "quant")

      click_link("SEND NEW")

      within "#announcements-form-modal" do
        expect(page).to have_content(/New Announcement/i)
        expect(page).to have_content(/SEND TO:/i)
        expect(page).to have_content(/PLAN/i)
        expect(page).to have_content(/TRACK/i)
        expect(page).to have_content(/COURSE CONFIG/i)
        expect(page).to have_content(/TEST DATE/i)
        expect(page).to have_content(/SIGNED-UP TIME/i)
        expect(page).to have_content(/COUNTRY/i)
        expect(page).to have_selector("input#announcement_sticky")
        expect(page).to have_content(/MESSAGE/i)
      end
    end
  end
end

feature "Non admin user accesses Announcement module", js: true do
  let(:user) { FactoryBot.create(:module_editor) }
  let(:role) { user.roles.find_by(name: "module_editor") }

  before do
    Permission.destroy_all
    login(user)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
  end

  context "with no permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "none", role_id: role.id)
    end

    it "renders 'not found' page" do
      visit user_feedback_admin_messages_path

      expect(page).to have_content(/Page not found/i)
    end
  end

  context "with read permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "read", role_id: role.id)
    end

    it "navigates from the root path" do
      visit admin_root_path

      click_link("Messages")

      expect(page).to have_content(/Messages/i)

      click_link("HOME MESSAGES")

      expect(page).to have_current_path(admin_announcements_path(section: "quant"))
      expect(page).to have_selector("select#type")
      expect(page).to have_selector("input#query")
      expect(page).not_to have_content("SEND NEW")

      within(".ttp-table.table") do
        expect(page).to have_selector("thead tr th", text: "SENT BY")
        expect(page).to have_selector("thead tr th", text: "CONTENT")
        expect(page).to have_selector("thead tr th", text: "ACTIONS")
      end
    end
  end

  context "with edit permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "edit", role_id: role.id)
    end

    it "navigates from the root path" do
      visit admin_root_path

      click_link("Messages")

      expect(page).to have_content(/Messages/i)

      click_link("HOME MESSAGES")

      expect(page).to have_current_path(admin_announcements_path(section: "quant"))
      expect(page).to have_selector("select#type")
      expect(page).to have_selector("input#query")
      expect(page).to have_content("SEND NEW")

      within(".ttp-table.table") do
        expect(page).to have_selector("thead tr th", text: "SENT BY")
        expect(page).to have_selector("thead tr th", text: "CONTENT")
        expect(page).to have_selector("thead tr th", text: "ACTIONS")
      end
    end

    it "accesses new announcements form modal" do
      visit admin_announcements_path(section: "quant")

      click_link("SEND NEW")

      within "#announcements-form-modal" do
        expect(page).to have_content(/New Announcement/i)
        expect(page).to have_content(/SEND TO:/i)
        expect(page).to have_content(/PLAN/i)
        expect(page).to have_content(/TRACK/i)
        expect(page).to have_content(/COURSE CONFIG/i)
        expect(page).to have_content(/TEST DATE/i)
        expect(page).to have_content(/SIGNED-UP TIME/i)
        expect(page).to have_content(/COUNTRY/i)
        expect(page).to have_selector("input#announcement_sticky")
        expect(page).to have_content(/MESSAGE/i)
      end
    end
  end
end

feature "Admin accesses the Marketing Modals module", js: true do
  let(:user) { FactoryBot.create(:admin) }

  before do
    login(user)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
    visit admin_marketing_modals_path
  end

  it "navigates from the root path" do
    visit admin_root_path

    click_link("Messages")

    expect(page).to have_content(/Message Center/i)

    click_link("MODAL MESSAGES")

    expect(page).to have_content("Message Center")
    expect(page).to have_current_path(admin_marketing_modals_path)
    expect(page).to have_selector("input[type='submit']", visible: :all)

    within(".ttp-table.table") do
      expect(page).to have_selector("thead tr th", text: "START DATE")
      expect(page).to have_selector("thead tr th", text: "END DATE")
      expect(page).to have_selector("thead tr th", text: "RECIPIENT")
      expect(page).to have_selector("thead tr th", text: "CONTENT")
      expect(page).to have_selector("thead tr th", text: "URL")
      expect(page).to have_selector("thead tr th", text: "ACTIONS")
    end
  end

  it "assigns marketing modals in descending order" do
    marketing_modal_1 = create(:marketing_modal, start_date: Date.today + 1.day, end_date: Date.today + 2.days)
    marketing_modal_2 = create(:marketing_modal, start_date: Date.today + 3.days, end_date: Date.today + 5.days)

    visit admin_marketing_modals_path

    within ".ttp-table.table > tbody > tr:first-child" do
      expect(page).to have_content(marketing_modal_2.title)
    end

    within ".ttp-table.table > tbody > tr:nth-child(2)" do
      expect(page).to have_content(marketing_modal_1.title)
    end
  end

  context "with new marketing modal" do
    context "with empty fields" do
      it "shows error messages" do
        expect(MarketingModal.count).to eq 0
        click_on "Schedule New Modal"

        within ".modal" do
          find(".modal form [type='submit']").click
        end

        expect(page).to have_content("New Modal")
        expect(MarketingModal.count).to eq 0
      end
    end

    context "with valid fields" do
      it "creates a new marketing modal" do
        expect(MarketingModal.count).to eq 0
        new_modal_info = build(:marketing_modal)

        click_on "Schedule New Modal"

        expect(page).to have_content("New Modal")

        execute_script("$('textarea#marketing_modal_title').remove()")
        execute_script("$('#cke_marketing_modal_title').remove()")
        execute_script("$('textarea#marketing_modal_subtitle').remove()")
        execute_script("$('#cke_marketing_modal_subtitle').remove()")
        execute_script("$('textarea#marketing_modal_message').remove()")
        execute_script("$('#cke_marketing_modal_message').remove()")

        execute_script(%|$('.modal form').append("<input id='marketing_modal_title' name='marketing_modal[title]' value='#{new_modal_info.title}' />")|)
        execute_script(%|$('.modal form').append("<input id='marketing_modal_subtitle' name='marketing_modal[subtitle]' value='#{new_modal_info.subtitle}' />")|)
        execute_script(%|$('.modal form').append("<input id='marketing_modal_message' name='marketing_modal[message]' value='#{new_modal_info.message}' />")|)

        within ".modal" do
          fill_in "marketing_modal[start_date]", with: new_modal_info.start_date
          fill_in "marketing_modal[end_date]", with: new_modal_info.end_date
          check "Enable call to action"
          fill_in "marketing_modal[button_label]", with: new_modal_info.button_label
          fill_in "marketing_modal[url]", with: new_modal_info.url

          find(".modal form [type='submit']").click
        end

        expect(page).to have_content("Marketing Modal created")
        expect(MarketingModal.count).to eq 1
      end

      it "edits a marketing modal" do
        marketing_modal_1 = create(:marketing_modal, start_date: Date.today + 1.day, end_date: Date.today + 2.days, custom_subgroup: false, filter: { include_no_plan: "true" })

        visit admin_marketing_modals_path

        expect(page).to have_content(marketing_modal_1.title)

        find("button.menu-button").click
        click_on "Edit"

        expect(page).to have_content("Edit Modal")

        start_date_modified = Date.today + 3.days
        end_date_modified = Date.today + 4.days

        within ".modal" do
          first('input[name="marketing_modal[custom_subgroup]"][value="true"]').choose

          find(".modal form [type='submit']").click
        end
      end

      it "creates a new marketing modal with All Self-study Users" do
        expect(MarketingModal.count).to eq 0
        new_modal_info = build(:marketing_modal, filter: { include_no_plan: "false" })

        click_on "Schedule New Modal"

        expect(page).to have_content("New Modal")

        find("#marketing_type_all_self_study_users").click

        first(".dashboardcode-bsmultiselect").click
        all(".dashboardcode-bsmultiselect .dropdown-menu ul li").each(&:click)

        execute_script("$('textarea#marketing_modal_title').remove()")
        execute_script("$('#cke_marketing_modal_title').remove()")
        execute_script("$('textarea#marketing_modal_subtitle').remove()")
        execute_script("$('#cke_marketing_modal_subtitle').remove()")
        execute_script("$('textarea#marketing_modal_message').remove()")
        execute_script("$('#cke_marketing_modal_message').remove()")

        execute_script(%|$('.modal form').append("<input id='marketing_modal_title' name='marketing_modal[title]' value='#{new_modal_info.title}' />")|)
        execute_script(%|$('.modal form').append("<input id='marketing_modal_subtitle' name='marketing_modal[subtitle]' value='#{new_modal_info.subtitle}' />")|)
        execute_script(%|$('.modal form').append("<input id='marketing_modal_message' name='marketing_modal[message]' value='#{new_modal_info.message}' />")|)

        within ".modal" do
          fill_in "marketing_modal[start_date]", with: new_modal_info.start_date
          fill_in "marketing_modal[end_date]", with: new_modal_info.end_date
          check "Enable call to action"
          fill_in "marketing_modal[button_label]", with: new_modal_info.button_label
          fill_in "marketing_modal[url]", with: new_modal_info.url

          find(".modal form [type='submit']").click
        end

        expect(page).to have_content("Marketing Modal created")
        expect(new_modal_info.show_to?(user)).to be_falsey
        expect(MarketingModal.count).to eq 1
      end

      it "creates a new marketing modal with All Self-study Users" do
        expect(MarketingModal.count).to eq 0
        new_modal_info = build(:marketing_modal, filter: { include_no_plan: "true" })

        click_on "Schedule New Modal"

        expect(page).to have_content("New Modal")

        find("#marketing_type_custom_subgroup").click

        first(".dashboardcode-bsmultiselect").click
        all(".dashboardcode-bsmultiselect .dropdown-menu ul li").each(&:click)

        execute_script("$('textarea#marketing_modal_title').remove()")
        execute_script("$('#cke_marketing_modal_title').remove()")
        execute_script("$('textarea#marketing_modal_subtitle').remove()")
        execute_script("$('#cke_marketing_modal_subtitle').remove()")
        execute_script("$('textarea#marketing_modal_message').remove()")
        execute_script("$('#cke_marketing_modal_message').remove()")

        execute_script(%|$('.modal form').append("<input id='marketing_modal_title' name='marketing_modal[title]' value='#{new_modal_info.title}' />")|)
        execute_script(%|$('.modal form').append("<input id='marketing_modal_subtitle' name='marketing_modal[subtitle]' value='#{new_modal_info.subtitle}' />")|)
        execute_script(%|$('.modal form').append("<input id='marketing_modal_message' name='marketing_modal[message]' value='#{new_modal_info.message}' />")|)

        within ".modal" do
          fill_in "marketing_modal[start_date]", with: new_modal_info.start_date
          fill_in "marketing_modal[end_date]", with: new_modal_info.end_date
          check "Enable call to action"
          fill_in "marketing_modal[button_label]", with: new_modal_info.button_label
          fill_in "marketing_modal[url]", with: new_modal_info.url

          find(".modal form [type='submit']").click
        end

        expect(page).to have_content("Marketing Modal created")
        expect(new_modal_info.show_to?(user)).to be_truthy
        expect(MarketingModal.count).to eq 1
      end
    end
  end

  it "deletes a marketing modal" do
    marketing_modal_1 = create(:marketing_modal, start_date: Date.today + 1.day, end_date: Date.today + 2.days)

    visit admin_marketing_modals_path

    expect(page).to have_content(marketing_modal_1.title)

    find("button.menu-button").click
    execute_script("$('ul.dropdown-menu a:contains(Delete)').click()")
    accept_alert "Are you sure you want to delete this marketing modal?"

    expect(page).to have_content("Message Center")
    expect(page).not_to have_content(marketing_modal_1.title)
    expect(page).to have_content("No modals found")
    expect(page).to have_content("Marketing modal successfully deleted")
  end
end

feature "Non admin user accesses Marketing Modals module", js: true do
  let(:user) { FactoryBot.create(:module_editor) }
  let(:role) { user.roles.find_by(name: "module_editor") }

  before do
    Permission.destroy_all
    login(user)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
  end

  context "with no permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Marketing Modals", action_type: "none", role_id: role.id)
    end

    it "renders 'not found' page" do
      visit admin_marketing_modals_path

      expect(page).to have_content(/Page not found/i)
    end
  end

  context "with read permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "read", role_id: role.id)
      FactoryBot.create(:permission, module_name: "Messages", action_type: "read", role_id: role.id)
    end

    it "navigates from the root path" do
      visit admin_root_path

      click_link("Messages")

      expect(page).to have_content(/Messages/i)

      click_link("MODAL MESSAGES")

      expect(page).to have_current_path(admin_marketing_modals_path)
      expect(page).to have_content("RECIPIENT")

      within(".ttp-table.table") do
        expect(page).to have_selector("thead tr th", text: "START DATE")
        expect(page).to have_selector("thead tr th", text: "END DATE")
        expect(page).to have_selector("thead tr th", text: "RECIPIENT")
        expect(page).to have_selector("thead tr th", text: "CONTENT")
        expect(page).to have_selector("thead tr th", text: "URL")
        expect(page).to have_selector("thead tr th", text: "ACTIONS")
      end
    end
  end

  context "with edit permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "read", role_id: role.id)
      FactoryBot.create(:permission, module_name: "Messages", action_type: "edit", role_id: role.id)
    end

    it "navigates from the root path" do
      visit admin_root_path

      click_link("Messages")

      expect(page).to have_content(/Message Center/i)

      click_link("MODAL MESSAGES")

      expect(page).to have_current_path(admin_marketing_modals_path)
      expect(page).to have_content("Whenever there are marketing modals to show they will appear here")

      within(".ttp-table.table") do
        expect(page).to have_selector("thead tr th", text: "START DATE")
        expect(page).to have_selector("thead tr th", text: "END DATE")
        expect(page).to have_selector("thead tr th", text: "RECIPIENT")
        expect(page).to have_selector("thead tr th", text: "CONTENT")
        expect(page).to have_selector("thead tr th", text: "URL")
        expect(page).to have_selector("thead tr th", text: "ACTIONS")
      end
    end

    it "accesses marketing modals form modal" do
      visit admin_marketing_modals_path
      click_on "Schedule New Modal"
      first("input[type='submit']").click

      within ".modal" do
        expect(page).to have_content(/New Modal/i)
      end
    end
  end
end

feature "admin user create Announcement", js: true do
  let(:user) { FactoryBot.create(:module_editor) }
  let(:role) { user.roles.find_by(name: "module_editor") }

  before do
    Permission.destroy_all
    login(user)
    user.update(signed_up_for_free_account: true)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
  end

  context "with edit permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Messages", action_type: "edit", role_id: role.id)
    end

    it "navigates from the root path" do
      visit admin_root_path

      click_link("HOME MESSAGES")

      click_link("SEND NEW")

      fill_in_ckeditor("MESSAGE", with: "Messages")
      find(".submit-buttons-actions [type='submit']").click

      expect(page).to have_content("SEND NEW")
      expect(Announcement.count).to eq 1
    end

    it "Create announcement with Custom subgroup plan options" do
      visit admin_root_path

      click_link("HOME MESSAGES")
      click_link("SEND NEW")

      find("#announcement_type_custom_subgroup").click
      first(".dashboardcode-bsmultiselect").click
      all(".dashboardcode-bsmultiselect .dropdown-menu ul li").each(&:click)

      fill_in_ckeditor("MESSAGE", with: "Messages")
      find(".submit-buttons-actions [type='submit']").click

      expect(page).to have_content("SEND NEW")

      expect(Announcement.last.send_messages!).to be_truthy
      expect(Announcement.count).to eq 1
    end
  end
end
