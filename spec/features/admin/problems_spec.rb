require "spec_helper"

feature "Member tries to access Problems Analytics", js: true do
  let(:user) { FactoryBot.create(:member) }

  it "renders 'not found' page" do
    login(user)
    visit analytics_admin_problems_path

    expect(page).to have_content(/Page not found/i)
  end
end

feature "Admin accesses Problem List", js: true do
  let(:user) { FactoryBot.create(:admin, track: nil) }
  let!(:chapter_quant1) { FactoryBot.create(:chapter, section: "quant", name: "Quant 1") }
  let!(:chapter_quant2) { FactoryBot.create(:chapter, section: "quant", name: "Quant 2") }
  let!(:chapter_verbal) { FactoryBot.create(:chapter, section: "verbal", name: "Verbal 1") }

  before { login(user) }

  context "when problem belongs to chapters" do
    before do
      FactoryBot.create_list(:problem, 3)
      visit(admin_problems_path)
    end

    it "renders 3 problems" do
      expect(page).to have_link("Quant")
      expect(page).to have_link("Verbal")
      expect(page).to have_selector("table tbody#problems tr", count: 3)
    end

    it "displays problems when search by id" do
      problem = Problem.first
      problem.update_columns(content: "Problem to search")
      expect(page).to have_selector("table tbody#problems tr", count: 3)

      fill_in("query", with: "#{problem.id}")
      find(".button-search").click

      expect(page).to have_selector("table tbody#problems tr", count: 1)
      expect(page).to have_content(/Problem to search/i)
    end
  end

  context "when problem doesn't belongs to chapters" do
    let!(:problem) { FactoryBot.create(:problem) }

    before do
      problem.chapter_id = nil
      problem.save

      visit(admin_problems_path)
    end

    it "doesn't renders any problems" do
      expect(page).to have_link("Quant")
      expect(page).to have_link("Verbal")
      expect(page).to have_selector("table tbody#problems tr", count: 0)
    end

    it "shows when untagged is selected" do
      within "#chapter_id" do
        find("option[value='untagged']").click
      end

      expect(page).to have_selector("table tbody#problems tr", count: 1)
    end
  end

  context "when there are problems associated with the lesson" do
    let(:lesson) { FactoryBot.create(:topic, parent: chapter_quant1, name: "Topic with problems") }

    before do
      FactoryBot.create_list(:problem, 3)
      FactoryBot.create_list(:problem, 3, chapter: chapter_quant1)
      FactoryBot.create_list(:problem, 2, chapter: chapter_quant1, lessons: [lesson])

      visit(admin_problems_path)
    end

    it "renders 8 problems" do
      expect(page).to have_link("Quant")
      expect(page).to have_link("Verbal")
      expect(page).to have_selector("table tbody#problems tr", count: 8)
    end

    it "displays 5 problems for chapter 1" do
      select("Quant 1", from: "chapter_id")

      expect(page).to have_selector("table tbody#problems tr", count: 5)
    end

    it "displays 2 problems for lesson" do
      select("Quant 1", from: "chapter_id")
      select("Topic with problems", from: "lesson_id")

      expect(page).to have_selector("table tbody#problems tr", count: 2)
    end
  end
end

feature "Admin accesses Problem builder page", js: true do
  let(:user) { FactoryBot.create(:admin, track: nil) }
  let!(:chapter_quant1) { FactoryBot.create(:chapter, section: "quant", name: "Quant 1") }
  let!(:chapter_quant2) { FactoryBot.create(:chapter, section: "quant", name: "Quant 2") }
  let!(:chapter_verbal) { FactoryBot.create(:chapter, section: "verbal", name: "Verbal 1") }

  before do
    login(user)
    allow(Chapter).to receive(:testable) { Chapter.all }
  end

  it "navigates from the root path" do
    visit admin_root_path

    click_link("Problems")

    expect(page).to have_content(/Problems/i)

    click_link("ADD NEW")

    expect(page).to have_current_path(new_admin_problem_path)
    expect(page).to have_content(/Problem Builder/i)
    expect(page).to have_select("problem_chapter_id")
    expect(page).to have_select("problem_question_type")
  end

  it "gives validation errors" do
    visit new_admin_problem_path

    find("#problem_chapter_id").find(:xpath, "option[2]").select_option
    select("Graphics Interpretation", from: "problem_question_type")
    fill_in("problem_option_a", with: "Option A")
    fill_in("problem_option_b", with: "Option B")
    find(:css, "#correct_option_1_dropdown_1[value='0']").set(true)

    find(".dropdown-1 .add-another-option-btn").click
    fill_in("problem_option_b", with: "Option C")

    find(".save-exercise-btn").click

    expect(page).to have_current_path(admin_problems_path)
    expect(page).to have_content(/Question Stem can't be blank/i)
  end

  it "gives validation errors when question is not added in the problem" do
    visit new_admin_problem_path

    find("#problem_chapter_id").find(:xpath, "option[2]").select_option
    select("Reading Comprehension", from: "problem_question_type")
    find(".save-exercise-btn").click

    expect(page).to have_current_path(admin_problems_path)
    expect(page).to have_content(/passage can't be blank/i)
    expect(page).to have_content(/must have at least one question/i)
  end
end

feature "Admin accesses Problems Analytics", js: true do
  let(:user) { FactoryBot.create(:admin, track: nil) }
  let!(:chapter_quant1) { FactoryBot.create(:chapter, section: "quant", name: "Quant 1") }
  let!(:chapter_quant2) { FactoryBot.create(:chapter, section: "quant", name: "Quant 2") }
  let!(:chapter_verbal) { FactoryBot.create(:chapter, section: "verbal", name: "Verbal 1") }

  before do
    login(user)
    allow(Chapter).to receive(:testable) { Chapter.all }
  end

  it "navigates from the root path" do
    visit admin_root_path

    click_link("Problems")

    expect(page).to have_content(/Problems/i)

    click_link("ANALYTICS")

    expect(page).to have_current_path(analytics_admin_problems_path(section: "quant"))
    expect(page).to have_select("chapter_id")
    expect(page).to have_selector("table.ttp-table")
    expect(page).to have_selector("table.ttp-table tbody tr.level.e td", text: "Easy Questions (0)")
    expect(page).to have_selector("table.ttp-table tbody tr.level.m td", text: "Medium Questions (0)")
    expect(page).to have_selector("table.ttp-table tbody tr.level.h td", text: "Hard Questions (0)")
  end

  context "when chapters are available" do
    before { visit(analytics_admin_problems_path(chapter_id: chapter_quant1.id)) }

    it "renders a select with 2 chapters for quant" do
      expect(page).to have_link("Quant")
      expect(page).to have_link("Verbal")
      expect(page).to have_select("chapter_id", options: ["Quant 1", "Quant 2"])
      expect(page).to_not have_select("chapter_id", with_options: ["Verbal 1"])
    end

    it "renders a select with 1 chapter for verbal" do
      click_link("Verbal")

      expect(page).to have_link("Quant")
      expect(page).to have_link("Verbal")
      expect(page).to have_select("chapter_id", options: ["Verbal 1"])
      expect(page).to_not have_select("chapter_id", with_options: ["Quant 1", "Quant 2"])
    end
  end

  context "when problems are available" do
    before do
      FactoryBot.create_list(:problem, 3, chapter: chapter_quant1)
      FactoryBot.create_list(:problem, 2, chapter: chapter_quant2)

      visit(analytics_admin_problems_path(chapter_id: chapter_quant1.id))
    end

    it "displays 3 problems for chapter 1" do
      expect(page).to have_selector("table tbody tr.problem", count: 3)
    end

    it "displays 2 problems for chapter 2", js: true do
      select("Quant 2", from: "chapter_id")

      expect(page).to have_selector("table tbody tr.problem", count: 2)
    end

    it "does not display examples for chapter 3 (Verbal)" do
      click_on("Verbal")

      expect(page).to_not have_selector("table tbody tr.problem")
    end
  end

  context "when problem statistics are available" do
    let!(:problem) do
      FactoryBot.create(
        :problem,
        first_attempts_analytics_array: [1, 2, 3, 4, 5],
        chapter: chapter_quant1,
        percentage_correct: 80,
        times_attempted: 10,
        avg_time_correct: 90,
        avg_time_incorrect: 100
      )
    end

    it "displays attempts analytics data" do
      visit(analytics_admin_problems_path(chapter_id: chapter_quant1.id))

      expect(page).to have_selector("table tbody tr.problem", count: 1)
      expect(page).to have_selector("table tbody tr.problem td", text: "80")
      expect(page).to have_selector("table tbody tr.problem td", text: "10")
      expect(page).to have_selector("table tbody tr.problem td", text: "1:30")
      expect(page).to have_selector("table tbody tr.problem td", text: "1:40")

      [1, 2, 3, 4, 5].each do |index|
        expect(page).to have_selector("table tbody tr.problem td", text: index.to_s)
      end
    end

    it "displays attempts analytics data and red highlight" do
      problem.update(percentage_correct: 60)

      visit(analytics_admin_problems_path(chapter_id: chapter_quant1.id))

      expect(page).to have_selector("table tbody tr.problem", count: 1)
      expect(page).to have_selector("table tbody tr.problem td.highlight-red-amount", text: "60")
      expect(page).to have_selector("table tbody tr.problem td", text: "10")
      expect(page).to have_selector("table tbody tr.problem td", text: "1:30")
      expect(page).to have_selector("table tbody tr.problem td", text: "1:40")

      [1, 2, 3, 4, 5].each do |index|
        expect(page).to have_selector("table tbody tr.problem td", text: index.to_s)
      end
    end
  end
end

feature "Member tries to access Problem Editable List", js: true do
  let(:user) { FactoryBot.create(:member) }

  it "renders 'not found' page" do
    login(user)
    visit editable_list_admin_problems_path

    expect(page).to have_content(/Page not found/i)
  end
end

feature "Admin accesses Problem Editable List", js: true do
  let(:user) { FactoryBot.create(:admin, track: nil) }
  let!(:chapter_quant1) { FactoryBot.create(:chapter, section: "quant", name: "Quant 1") }
  let!(:chapter_quant2) { FactoryBot.create(:chapter, section: "quant", name: "Quant 2") }
  let!(:chapter_verbal) { FactoryBot.create(:chapter, section: "verbal", name: "Verbal 1") }

  before do
    login(user)
    FactoryBot.create_list(:topic, 3, parent: chapter_quant1)
    FactoryBot.create_list(:topic, 2, parent: chapter_quant2)
    FactoryBot.create_list(:topic, 1, parent: chapter_verbal)
  end

  it "navigates from the root path" do
    visit admin_root_path

    click_link("Problems")

    expect(page).to have_content(/Problems/i)

    click_link("EDITABLE LIST")

    expect(page).to have_current_path(editable_list_admin_problems_path(section: "quant"))
    expect(page).to have_select("chapter_id")
    expect(page).to have_selector("table.ttp-table")
    expect(page).to have_selector("table.ttp-table thead th", text: "QUESTION")
    expect(page).to have_selector("table.ttp-table thead th", text: "DIFFICULTY")
    expect(page).to have_selector("table.ttp-table thead th", text: "TYPE")
    expect(page).to have_selector("table.ttp-table thead th", text: "EXCLUDED TRACKS")
    expect(page).to have_selector("table.ttp-table thead th", text: "CHAPTER")
    expect(page).to have_selector("table.ttp-table thead th", text: "TAGS")
  end

  context "when chapters are available" do
    before { visit(editable_list_admin_problems_path(chapter_id: chapter_quant1.id)) }

    it "renders a select with 2 chapters for quant" do
      expect(page).to have_link("Quant")
      expect(page).to have_link("Verbal")
      expect(page).to have_select("chapter_id", options: ["Untagged", "Quant 1", "Quant 2"])
      expect(page).to_not have_select("chapter_id", with_options: ["Untagged", "Verbal 1"])
    end

    it "renders a select with 1 chapters for verbal" do
      click_link("Verbal")

      expect(page).to have_link("Quant")
      expect(page).to have_link("Verbal")
      expect(page).to have_select("chapter_id", options: ["Untagged", "Verbal 1"])
      expect(page).to_not have_select("chapter_id", with_options: ["Untagged", "Quant 1", "Quant 2"])
    end
  end

  context "when problems are available" do
    before do
      FactoryBot.create_list(:problem, 3, chapter: chapter_quant1)
      FactoryBot.create_list(:problem, 2, chapter: chapter_quant2)

      visit(editable_list_admin_problems_path(chapter_id: chapter_quant1.id))
    end

    it "displays 3 problems for chapter 1" do
      expect(page).to have_selector("table tbody tr.problem", count: 3)
    end

    it "displays 2 problems for chapter 2" do
      select("Quant 2", from: "chapter_id")

      expect(page).to have_selector("table tbody tr.problem", count: 2)
    end

    it "does not display problems for chapter 3 (Verbal)" do
      click_on("Verbal")

      select("Verbal 1", from: "chapter_id")

      expect(page).to_not have_selector("table tbody tr.problem")
    end
  end

  context "when the form is edited" do
    let(:problem) { chapter_quant1.problems.first }
    let(:editable_problem) { find("table tbody tr.problem#problem-#{problem.id}") }
    let!(:track) { FactoryBot.create(:track) }

    before do
      FactoryBot.create(:problem, chapter: chapter_quant1)

      visit(editable_list_admin_problems_path(chapter_id: chapter_quant1.id))
    end

    it "displays problem editable controls" do
      expect(editable_problem).to have_select("problem_level", selected: problem.level.upcase)
      expect(editable_problem).to have_select("problem_type", selected: "Problem")
      expect(editable_problem).to have_selector("input#problem_excluded_track_ids_#{track.id}")
      expect(editable_problem).to have_link(chapter_quant1.name)
      expect(editable_problem).to have_link("ADD TAGS")
    end

    it "displays problem excluded_track checkbox when edited", js: true do
      expect(problem.excluded_track_ids).to_not include(track.id)
      expect(editable_problem).to have_unchecked_field("problem_excluded_track_ids_#{track.id}")

      problem.excluded_track_ids = [track.id]

      visit(editable_list_admin_problems_path(chapter_id: chapter_quant1.id))

      expect(problem.excluded_track_ids).to include(track.id)
      expect(page).to have_checked_field("problem_excluded_track_ids_#{track.id}")
    end
  end
end

feature "Non admin accesses problem modules", js: true do
  let(:user) { FactoryBot.create(:module_editor) }
  let(:role) { user.roles.find_by(name: "module_editor") }
  let!(:chapter_quant1) { FactoryBot.create(:chapter, section: "quant", name: "Quant 1") }
  let!(:chapter_quant2) { FactoryBot.create(:chapter, section: "quant", name: "Quant 2") }
  let!(:chapter_verbal) { FactoryBot.create(:chapter, section: "verbal", name: "Verbal 1") }

  before do
    Permission.destroy_all
    FactoryBot.create_list(:topic, 3, parent: chapter_quant1)
    FactoryBot.create_list(:topic, 2, parent: chapter_quant2)
    FactoryBot.create_list(:topic, 1, parent: chapter_verbal)
    login(user)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
  end

  context "with no permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Problems and Diagnostic", action_type: "none", role_id: role.id)
    end

    it "renders 'not found' page" do
      visit(admin_problems_path)

      expect(page).to have_content(/Page not found/i)
    end
  end

  context "with read permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Problems and Diagnostic", action_type: "read", role_id: role.id)
    end

    it "accesses problems path" do
      visit admin_problems_path

      expect(page).to have_current_path(admin_problems_path)
      expect(page).to have_content(/Problems/i)
      expect(page).not_to have_content("PROBLEM COUNTS")
      expect(page).not_to have_content("EDITABLE LIST")
      expect(page).not_to have_content("ANALYTICS")
      expect(page).not_to have_content("ADD NEW")
      expect(page).not_to have_content("IMPORT FROM HTML")
    end

    it "does not access  editable problems page" do
      visit editable_list_admin_problems_path

      expect(page).to have_content(/Page not found/i)
    end

    it "does not access problem analytics page" do
      visit(analytics_admin_problems_path)

      expect(page).to have_content(/Page not found/i)
    end

    it "does not access problem count page" do
      visit counts_admin_problems_path

      expect(page).to have_content(/Page not found/i)
    end
  end

  context "with edit permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Problems and Diagnostic", action_type: "edit", role_id: role.id)
    end

    it "accesses problems path" do
      visit admin_problems_path

      expect(page).to have_current_path(admin_problems_path)
      expect(page).to have_content(/Problems/i)
      expect(page).to have_content(/PROBLEM COUNTS/i)
      expect(page).to have_content(/EDITABLE LIST/i)
      expect(page).to have_content(/ANALYTICS/i)
      expect(page).to have_content(/ADD NEW/i)
      expect(page).to have_content(/IMPORT FROM HTML/i)
    end

    it "accesses editable problems page" do
      visit editable_list_admin_problems_path

      expect(page).to have_select("chapter_id")
      expect(page).to have_selector("table.ttp-table")
      expect(page).to have_selector("table.ttp-table thead th", text: "QUESTION")
      expect(page).to have_selector("table.ttp-table thead th", text: "DIFFICULTY")
      expect(page).to have_selector("table.ttp-table thead th", text: "TYPE")
      expect(page).to have_selector("table.ttp-table thead th", text: "EXCLUDED TRACKS")
      expect(page).to have_selector("table.ttp-table thead th", text: "CHAPTER")
      expect(page).to have_selector("table.ttp-table thead th", text: "TAGS")
    end

    it "accesses problem analytics page" do
      visit(analytics_admin_problems_path)

      expect(page).to have_current_path(analytics_admin_problems_path)
      expect(page).to have_select("chapter_id")
      expect(page).to have_selector("table.ttp-table")
      expect(page).to have_selector("table.ttp-table tbody tr.level.e td", text: "Easy Questions (0)")
      expect(page).to have_selector("table.ttp-table tbody tr.level.m td", text: "Medium Questions (0)")
      expect(page).to have_selector("table.ttp-table tbody tr.level.h td", text: "Hard Questions (0)")
    end
  end
end
