require "spec_helper"

feature "Member tries to access admin panel", js: true do
  let(:user) { FactoryBot.create(:member) }

  it "renders 'not found' page" do
    login(user)
    visit admin_root_path

    expect(page).to have_content(/Page not found/i)
  end
end

feature "Admin accesses the online classes page", js: true do
  let(:user) { FactoryBot.create(:admin) }

  before do
    login(user)
  end

  it "redirects to the online classes page" do
    visit admin_online_classes_path

    expect(page).to have_content(/Live Classes/i)
    expect(page).to have_current_path(admin_online_classes_path)
  end

  it "navigates from the root path" do
    visit admin_root_path

    click_link("Live Classes")

    expect(page).to have_content(/Live Classes/i)
    expect(page).to have_current_path(admin_online_classes_path)
  end

  context "when multiple online classes are available" do
    let!(:online_class_1) { FactoryBot.create(:online_class, start_time: "07:00 AM", end_time: "08:00 AM", online_class_sessions: FactoryBot.build_list(:online_class_session, 3)) }
    let!(:online_class_2) { FactoryBot.create(:online_class, start_time: "10:00 AM", end_time: "11:00 AM", online_class_sessions: FactoryBot.build_list(:online_class_session, 2)) }

    before do
      visit admin_online_classes_path
    end

    it "displays 2 online classes" do
      expect(page).to have_selector("table.ttp-table tbody tr", count: 2)
    end
  end
end

feature "Admin accesses online class schedule page", js: true do
  let(:user) { FactoryBot.create(:admin) }
  let!(:online_class_1) { FactoryBot.create(:online_class, :with_sessions) }
  let(:online_class_message) { FactoryBot.create(:online_class_message, sender_id: user.id, online_class: online_class_1) }

  before do
    login(user)
  end

  it "opens the online classes index" do
    visit admin_online_classes_path

    find(".actions a:nth-child(1)").click

    expect(page).to have_content(/General Information/i)
    expect(page).to have_content(/Student List/i)
    expect(page).to have_current_path(admin_online_class_path(online_class_1))
  end

  it "displays 3 online class sessions" do
    visit admin_online_class_path(online_class_1)

    click_link("CLASSES")

    expect(page).to have_selector("table#online-class-schedules tbody tr", count: 3)
  end

  it "has online class syllabus link" do
    visit admin_online_class_path(online_class_1)

    expect(page).to have_current_path(admin_online_class_path(online_class_1))

    expect(page).to have_link("download syllabus (pdf)", href: online_class_1.syllabus_url)
  end

  context "when online class has enrolled user" do
    let!(:member_user) { create(:member, first_name: "member", last_name: "ttp", email: "<EMAIL>") }

    before do
      FactoryBot.create(:online_classes_user, online_class_id: online_class_1.id, user_id: member_user.id)
      visit admin_online_class_path(online_class_1)
    end

    it "displays 1 participant" do
      expect(page).not_to have_content(/No participants at this time/i)
      expect(page).to have_selector("table#online-class-users tbody tr", count: 1)
    end

    it "deletes participant" do
      within "table#online-class-users tbody" do
        expect(page).to have_selector("tr", count: 1)

        accept_alert do
          find(".actions a:nth-child(3)").click
        end
        expect(page).to have_content(/No participants at this time/i)
      end
    end

    it "downloads CSV for participants email list" do
      click_link "download email list (csv)"

      expect(DownloadHelpers.download_content).to have_content("Member Ttp,<EMAIL>")
    end
  end
end

feature "Admin manages users in online classes", js: true do
  let(:admin_user) { FactoryBot.create(:admin) }
  let!(:online_class) { FactoryBot.create(:online_class, :with_sessions, :skip_validate) }
  let(:user) { FactoryBot.create(:user) }
  let!(:online_class_1) { FactoryBot.create(:online_class, :with_sessions, :skip_validate) }

  context "when the online classes index is opened" do
    before :each do
      login(admin_user)
      visit admin_online_classes_path
      visit admin_online_class_path(online_class)

      click_link("Add New Student")
    end

    it "raises error when email is empty" do
      fill_in("email", with: nil)
      find(".modal form [type='submit']").click

      expect(page).to have_content(/User account with that email does not exist./i)
    end

    it "adds the user successfully to the class" do
      fill_in "email", with: user.email
      find("#add-new-student-modal .submit-buttons-actions .submit-btn").click

      expect(page).to have_content(/User successfully added to the class./i)
    end

    it "displays an error when the user is already enrolled in the class" do
      online_class_1.users << user
      fill_in "email", with: user.email
      find("#add-new-student-modal .submit-buttons-actions .submit-btn").click

      expect(page).to have_content(/This user is already registered in another class./i, wait: 10)
    end

    it "displays an error when the user tries to enroll in another online class without removing the previous enrollment" do
      online_class_1.users << user
      fill_in "email", with: user.email
      find("#add-new-student-modal .submit-buttons-actions .submit-btn").click
      find("#student-warning-modal", visible: :visible, wait: 5)

      within("#student-warning-modal .submit-buttons-actions") do
        click_link("ADD WITHOUT REMOVING")
      end

      expect(page).to have_content(/User successfully added to the class./i)
    end

    it "removes the user from another online class and enrolls them in the new class" do
      online_class_1.users << user
      fill_in "email", with: user.email
      find("#add-new-student-modal .submit-buttons-actions .submit-btn").click
      find("#student-warning-modal", visible: :visible, wait: 5)

      within("#student-warning-modal .submit-buttons-actions") do
        click_link("REMOVE FROM OTHER CLASS")
      end

      expect(page).to have_content(/User successfully added to the class./i)
    end
  end
end

feature "Admin accesses add new class schedule", js: true do
  let(:user) { FactoryBot.create(:admin) }

  before do
    login(user)
  end

  it "opens the online class index" do
    visit admin_online_classes_path

    click_link("ADD NEW COHORT")

    expect(page).to have_content(/New Class Schedule/i)
    expect(page).to have_content(/Syllabus/i)
    expect(page).to have_selector("form#new_online_class")
    expect(page).to have_select("online_class_instructor_id")
    expect(page).to have_select("online_class_start_time")
    expect(page).to have_select("online_class_end_time")
    expect(page).to have_selector("input#online_class_spots")
  end

  context "when creating a new online class" do
    before do
      @online_class_count = OnlineClass.count
      FactoryBot.create_list(:instructor, 3)

      visit admin_online_classes_path

      click_link("ADD NEW COHORT")

      expect(page).to have_content(/New Class Schedule/i)
    end

    it "does not save when the form is empty" do
      click_button("SAVE CHANGES")

      expect(page).to have_content(/New Class Schedule/i)
      expect(page).to have_selector("form#new_online_class")
      expect(OnlineClass.count).to eql(@online_class_count)
    end

    context "when the online class fields are not filled" do
      it "displays validation errors" do
        @online_class_count = OnlineClass.count

        click_button("SAVE CHANGES")

        expect(page).to have_content(/New Class Schedule/i)
        expect(OnlineClass.count).to eql(@online_class_count)
        expect(page).to have_selector("form#new_online_class")
        expect(page).to have_text("Spots can't be blank")
        expect(page).to have_text("End time should be later than the start time")
      end
    end

    context "when the online class fields are filled" do
      before do
        find("#online_class_instructor_id").find(:xpath, "option[2]").select_option
        find("#online_class_start_time").find(:xpath, "option[2]").select_option
        find("#online_class_end_time").find(:xpath, "option[3]").select_option
        fill_in("online_class_spots", with: 20)
      end

      it "shows new class element" do
        click_link("ADD NEW CLASS")

        expect(page).to have_content(/New Class Schedule/i)
        expect(page).to have_current_path(new_admin_online_class_path)
        expect(page).to have_selector(".content .online-class-session-container span", text: "Class 1")
        expect(page).to have_css("i.fa-trash.remove-class-session")
      end

      it "displays validation errors when class session fields are not fill" do
        @online_class_count = OnlineClass.count

        click_link("ADD NEW CLASS")

        click_button("SAVE CHANGES")

        expect(page).to have_content(/New Class Schedule/i)
        expect(OnlineClass.count).to eql(@online_class_count)
        expect(page).to have_selector("form#new_online_class")
        expect(page).to have_text("Class [1] date can't be blank")
        expect(page).to have_text("Class [1] description can't be blank")
      end

      it "displays validation errors when class spots fields are not greater then or equal to 1" do
        @online_class_count = OnlineClass.count

        click_link("ADD NEW CLASS")

        fill_in("online_class_spots", with: -1)

        click_button("SAVE CHANGES")

        expect(page).to have_content(/New Class Schedule/i)
        expect(OnlineClass.count).to eql(@online_class_count)
        expect(page).to have_selector("form#new_online_class")
        expect(page).to have_text("Spots must be greater than or equal to 1")
      end
    end
  end

  context "when generate classes automatically" do
    before do
      visit admin_online_classes_path

      click_link("ADD NEW COHORT")

      expect(page).to have_content(/New Class Schedule/i)
    end

    it "displays generate classes automatically modal" do
      click_link("GENERATE CLASSES AUTOMATICALLY")

      expect(page).to have_content(/Generate Classes Automatically/i)
      expect(page).to have_selector("input#start_date")
      expect(page).to have_selector("input#end_date")
    end

    it "displays validation errors" do
      click_link("GENERATE CLASSES AUTOMATICALLY")

      expect(page).to have_content(/Generate Classes Automatically/i)

      click_button("ADD CLASSES")

      expect(page).to have_text("End date should be later than the start date")
    end
  end

  context "when delete all class elements" do
    before do
      visit admin_online_classes_path

      click_link("ADD NEW COHORT")

      expect(page).to have_content(/New Class Schedule/i)
    end

    it "deletes all class elements" do
      click_link("ADD NEW CLASS")

      click_link("ADD NEW CLASS")

      click_link("ADD NEW CLASS")

      expect(page).to have_selector(".content .online-class-session-container", count: 3)

      click_link("Delete All")

      expect(page).to have_selector(".content .online-class-session-container", count: 0)
    end
  end

  it "deletes class elements" do
    visit admin_online_classes_path

    click_link("ADD NEW COHORT")

    expect(page).to have_content(/New Class Schedule/i)

    click_link("ADD NEW CLASS")

    expect(page).to have_selector(".content .online-class-session-container", count: 1)

    accept_alert do
      find(".accordion i.remove-class-session").click
    end

    expect(page).to have_selector(".content .online-class-session-container", count: 0)
  end
end

feature "Member tries to access new class schedule", js: true do
  let(:user) { FactoryBot.create(:member) }

  it "renders 'not found' page" do
    login(user)
    visit new_admin_online_class_path

    expect(page).to have_content(/Page not found/i)
  end
end

feature "Non admin user accesses online classes modules", js: true do
  let(:user) { FactoryBot.create(:module_editor) }
  let(:role) { user.roles.find_by(name: "module_editor") }

  before do
    Permission.destroy_all
    login(user)
    allow_any_instance_of(Admin::AdminController).to receive(:current_chapter_id) { nil }
  end

  context "when the user has no permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Live Classes and Instructors", action_type: "none", role_id: role.id)
    end

    it "renders 'not found' page" do
      visit admin_online_classes_path

      expect(page).to have_content(/Page not found/i)
    end
  end

  context "when the user has read permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Live Classes and Instructors", action_type: "read", role_id: role.id)
    end

    it "redirects to the online class page" do
      visit admin_online_classes_path

      expect(page).to have_current_path(admin_online_classes_path)
      expect(page).to have_content(/Live Classes/i)
      expect(page).not_to have_content(/ADD NEW COHORT/i)

      within(".ttp-table.table") do
        expect(page).to have_selector("thead tr th", text: "First Class", visible: false)
        expect(page).to have_selector("thead tr th", text: "Last Class", visible: false)
        expect(page).to have_selector("thead tr th", text: "Instructor", visible: false)
        expect(page).to have_selector("thead tr th", text: "Classes", visible: false)
        expect(page).to have_selector("thead tr th", text: "Days", visible: false)
        expect(page).to have_selector("thead tr th", text: "Time", visible: false)
        expect(page).to have_selector("thead tr th", text: "Enrolled", visible: false)
        expect(page).to have_selector("thead tr th", text: "Actions", visible: false)
      end
    end
  end

  context "when the user has edit permissions" do
    before do
      FactoryBot.create(:permission, module_name: "Live Classes and Instructors", action_type: "edit", role_id: role.id)
    end

    it "redirects to the online class page" do
      visit admin_online_classes_path

      expect(page).to have_current_path(admin_online_classes_path)
      expect(page).to have_content(/Live Classes/i)
      expect(page).to have_content(/ADD NEW COHORT/i)

      within(".ttp-table.table") do
        expect(page).to have_selector("thead tr th", text: "First Class", visible: false)
        expect(page).to have_selector("thead tr th", text: "Last Class", visible: false)
        expect(page).to have_selector("thead tr th", text: "Instructor", visible: false)
        expect(page).to have_selector("thead tr th", text: "Classes", visible: false)
        expect(page).to have_selector("thead tr th", text: "Days", visible: false)
        expect(page).to have_selector("thead tr th", text: "Time", visible: false)
        expect(page).to have_selector("thead tr th", text: "Enrolled", visible: false)
        expect(page).to have_selector("thead tr th", text: "ACTIONS")
      end
    end

    it "accesses new online class schedule modal" do
      visit admin_online_classes_path

      click_link("ADD NEW COHORT")

      within "#new_online_class" do
        expect(page).to have_current_path(new_admin_online_class_path)
        expect(page).to have_select("online_class_start_time")
        expect(page).to have_select("online_class_end_time")
        expect(page).to have_select("online_class_instructor_id")
        expect(page).to have_selector("input#online_class_spots")
      end
    end
  end
end

feature "Admin accesses online class homework page", js: true do
  let(:user) { FactoryBot.create(:admin) }
  let!(:online_class_1) { FactoryBot.create(:online_class, :with_sessions) }
  let!(:session_1) { FactoryBot.create(:online_class_session, online_class: online_class_1) }
  let!(:session_2) { FactoryBot.create(:online_class_session, online_class: online_class_1) }
  let!(:homework_group_1) { FactoryBot.create(:homework_group, online_class_session: session_1) }
  let!(:chapter) { FactoryBot.create(:chapter) }
  let!(:problem) { FactoryBot.create(:problem, chapter: chapter) }
  let!(:homework_task) { FactoryBot.create(:homework_task, task_type: "custom_test", data: [problem.id], homework_group: homework_group_1) }
  let!(:topic) { FactoryBot.create(:topic, chapter: chapter) }

  before do
    login(user)

    visit admin_online_class_path(online_class_1)

    click_link("CLASSES")
  end

  # Currently skipped. This has to be fixed when the admin panel is updated with the new homework task structure.
  xit "opens the online classes page" do
    find("table#online-class-schedules .recording-actions .edit-recording", match: :first).click

    expect(page).to have_content(/Class Homework/i)
    expect(page).to have_content(/Is This Homework Ready for Release ?/i)
    expect(page).to have_content(/Ready/)
    expect(page).to have_content(/Homework Tasks/)
  end

  context "when session have problems assigned" do
    before do
      visit admin_homework_problems_path(session: session_1)
    end

    # Currently skipped. This has to be fixed when the admin panel is updated with the new homework task structure.
    xit "displays assigned homework task" do
      visit admin_homework_problems_path(session: session_1.id, index: 0)
      expected_text = "A custom test with #{homework_task.data.length} questions (#{chapter.section.capitalize})"
      expect(page).to have_content(expected_text)
    end

    # Currently skipped. This has to be fixed when the admin panel is updated with the new homework task structure.
    xit "deletes homework task" do
      visit admin_homework_problems_path(session: session_1.id, index: 0)

      within(".homework-problems") do
        accept_alert do
          find(".actions a .fa-trash").click
        end
      end

      expect(page).to have_no_css(".homework-problems")
    end

    # Currently skipped. This has to be fixed when the admin panel is updated with the new homework task structure.
    xit "updates homework status to ready" do
      accept_alert do
        click_link "MARK AS READY"
      end

      session_1.update(homework_status: "ready_for_release")

      expect(session_1.homework_status).to eq("ready_for_release")
      expect(page).to have_content(/MARK AS NOT READY/i)
    end
  end

  context "when session have no problems assigned" do
    before do
      visit admin_homework_problems_path(session: session_2)
    end

    # Currently skipped. This has to be fixed when the admin panel is updated with the new homework task structure.
    xit "displays homework task empty state" do
      expect(page).to have_content(/Start adding tasks with the button above./i)
    end

    # Currently skipped. This has to be fixed when the admin panel is updated with the new homework task structure.
    xit "does not changes homework status" do
      accept_alert do
        click_link "MARK AS READY"
      end

      expect(page).to have_content(/MARK AS READY/i)
    end
  end

  context "when adding a new task" do
    before do
      visit admin_homework_problems_path(session: session_1)

      find(".download-schedule-btn").click
      expect(page).to have_content(/ADD TASK/i)
    end

    # Currently skipped. This has to be fixed when the admin panel is updated with the new homework task structure.
    xit "adds a custom test task successfully" do
      select "Custom Test", from: "task_type"
      select chapter.name, from: "chapter_id"

      within(".table-results") do
        find("tbody tr:first-child").click
      end

      click_button("CREATE TASK")

      expected_text = "A custom test with #{homework_task.data.length} questions (#{chapter.section.capitalize})"
      expect(page).to have_content(expected_text)
      expect(page).to have_current_path(admin_homework_problems_path(session: session_1))
    end

    # Currently skipped. This has to be fixed when the admin panel is updated with the new homework task structure.
    xit "adds a prereading task successfully" do
      select "Pre-reading", from: "task_type"
      select chapter.name, from: "chapter_id"

      within(".table-results") do
        find("tbody tr:first-child").click
      end

      click_button("CREATE TASK")

      expected_text = "Prereading of #{homework_task.data.length} lessons (#{chapter.section.capitalize})"
      expect(page).to have_content(expected_text)
      expect(page).to have_current_path(admin_homework_problems_path(session: session_1))
    end
  end
end

feature "Admin adds class recording", js: true do
  let(:user) { FactoryBot.create(:admin) }
  let!(:online_class) { FactoryBot.create(:online_class, :with_sessions) }
  let!(:session) { FactoryBot.create(:online_class_session, online_class: online_class, recording_url: nil, show_in_library: false) }

  before do
    login(user)
    visit admin_online_class_path(online_class)
    click_link("CLASSES")
  end

  it "adds a recording URL without show the recording" do
    find(".recording-actions .add-recording", match: :first).click

    within ".add-class-recording-modal" do
      fill_in "recording_url", with: "12344"
      click_button "Add Recording"
    end

    expect(page).to have_content(/ADDED/i)
    expect(page).to have_selector(".status.added")
  end

  it "adds a recording URL and show the recording" do
    find(".recording-actions .add-recording", match: :first).click

    within ".add-class-recording-modal" do
      fill_in "recording_url", with: "12344"
      check "show_in_library"
      click_button "Add Recording"
    end

    expect(page).to have_content(/RELEASED/i)
    expect(page).to have_selector(".status.released")
  end

  it "when recording URL is empty" do
    find(".recording-actions .add-recording", match: :first).click

    within ".add-class-recording-modal" do
      fill_in "recording_url", with: ""
      click_button "Add Recording"
    end

    expect(page).to have_content(/NOT ADDED/i)
    expect(page).to have_selector(".status.not-added")
  end

  context "when recording URL fails to save" do
    it "displays an error message" do
      find(".recording-actions .add-recording", match: :first).click

      within ".add-class-recording-modal" do
        allow_any_instance_of(OnlineClassSession).to receive(:save).and_return(false)
        click_button "Add Recording"
      end

      expect(page).to have_content(/Failed to update class recording status./i)
      expect(page).to have_selector(".status.not-added")
    end
  end
end

feature "Admin accesses the message module for the online classes", js: true do
  let(:user) { FactoryBot.create(:admin) }
  let!(:online_class) { create(:online_class, :skip_validate) }
  let!(:sessions) { create_list(:online_class_session, 2, weeks_later: 4, online_class: online_class) }
  let!(:online_class_user) { create(:online_classes_user, online_class: online_class, user: user) }

  before do
    login(user)

    visit admin_online_class_path(online_class)

    click_link("MESSAGES")
  end

  it "opens the new email message send modal" do
    find(".send-new-email-message-btn").click

    expect(page).to have_content(/SEND EMAIL/i)
    expect(page).to have_content(/MESSAGE/i)
  end

  it "creates a message for the online classes" do
    find(".send-new-email-message-btn").click

    fill_in "online_class_message[subject]", with: "test subject"
    fill_in "online_class_message[message]", with: "test message"

    click_button("SEND")

    expect(OnlineClass::Message.count).to eq(1)
  end

  context "when accessing the online classes, the message page" do
    let!(:online_class_user) { create(:online_classes_user, online_class: online_class, user: user) }
    let!(:online_class_message) { create(:online_class_message, online_class: online_class) }

    it "renders the online classes message content" do
      visit admin_online_class_message_path(online_class, online_class_message)

      expect(page).to have_content(/Message Detail/i)
      expect(page).to have_content(online_class_message.message)
    end
  end
end
