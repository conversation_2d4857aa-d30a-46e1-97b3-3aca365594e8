require "spec_helper"

RSpec.feature "Next LiveTeach Class", js: true do
  let!(:user) { create(:member, :with_billing_info) }
  let!(:instructor) { FactoryBot.create(:instructor, name: "<PERSON>") }
  let!(:online_class) { create(:online_class, :skip_validate) }
  let!(:sessions) { create_list(:online_class_session, 1, weeks_later: 4, online_class: online_class, date: 1.days.ago.to_date) }
  let!(:session_with_recording) { create(:online_class_session, online_class: online_class, weeks_later: 1, recording_url: "123456", date: 2.days.ago.to_date) }
  let!(:session_without_recording) { create(:online_class_session, online_class: online_class, weeks_later: 2, recording_url: nil, date: 2.days.from_now.to_date) }
  let!(:session_with_zoom_link) { create(:online_class_session, online_class: online_class, weeks_later: 1, zoom_link: "https://example.com", date: Date.current) }

  context "when logged in" do
    describe "visiting the next LiveTeach class page" do
      before do
        login(user)
        visit dashboard_next_liveteach_class_path(session_id: sessions.first.id)
      end

      it "shows the instructor information" do
        expect(page).to have_css(".instructor-image")
        expect(page).to have_content(online_class.instructor.name)
        expect(page).to have_content("See other cohorts")
      end

      it "shows the list of online class sessions" do
        sessions.each do |session|
          expect(page).to have_content(session.date.strftime("%b %-d"))
          expect(page).to have_content("#{session.start_time} - #{session.end_time}")
        end
      end

      it "shows the class schedule information" do
        expect(page).to have_content(online_class.first_class.strftime("%b %-d"))
        expect(page).to have_content(online_class.last_class.strftime("%b %-d"))
      end

      it "handles accordion toggle for session details" do
        first_session = sessions.first
        find("button[data-bs-target='#cohort-session-#{first_session.id}']").click
        expect(page).to have_css("#cohort-session-#{first_session.id}.show")
      end

      it "shows the session recording icon if recording is available" do
        within("[data-target='#cohort-session-#{session_with_recording.id}']") do
          expect(page).to have_css(".online-class-recording")
          find(".online-class-recording").click
        end
        expect(page).to have_content("#{online_class.instructor.name} - Session #{session_with_recording.number_of_session}")
        within(".online-class-session-recording .modal-content") do
          expect(page).to have_css(".modal-header")
          expect(page).to have_css(".modal-body")
          expect(page).to have_selector(".video-wrapper")
          expect(page).to have_selector("iframe[src='https://player.vimeo.com/video/#{session_with_recording.recording_url}?autoplay=1']")
        end
      end

      it "does not show the session recording icon if recording is not available" do
        within("[data-target='#cohort-session-#{session_without_recording.id}']") do
          expect(page).not_to have_css(".online-class-recording")
        end
      end

      it "shows zoom link for sessions with a zoom link" do
        within("[data-target='#cohort-session-#{session_with_zoom_link.id}']") do
          expect(page).to have_link(href: session_with_zoom_link.zoom_link)
        end
      end

      it "does not show zoom link for sessions without a zoom link" do
        within("[data-target='#cohort-session-#{session_without_recording.id}']") do
          expect(page).not_to have_link(href: session_with_zoom_link.zoom_link)
        end
      end
    end
  end
end
