require "spec_helper"

feature "User accesses study plan page", js: true do
  context "when logged out" do
    it "redirects to login page" do
      visit study_plan_path
      expect(page).to have_current_path(new_user_session_path)
    end
  end

  context "when logged in" do
    let!(:user) { FactoryBot.create(:member) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }

    before do
      Setting.create(key: "ondemand_enabled", value: true)
      user.mission_study_plan_completion.regenerate_data
      user.update(short_study_plan: false)
      login(user)

      visit study_plan_path
    end

    it "navigates from the root_path" do
      visit root_path

      expect(page).to have_current_path(home_path)

      within "nav.navbar" do
        click_link "Study Plan"
      end

      expect(page).to have_content(/Study Plan/i)
      expect(page).to have_current_path(study_plan_path)
    end

    context "when study plan view is mission view" do
      before do
        within "#calendar-setup-modal" do
          click_button "Set it up later"
        end
      end

      it "renders properly" do
        expect(page).to have_current_path(study_plan_path)
        expect(page).to have_content(/Before diving in/i)
        expect(page).to have_content(/Learning Phase/i)
        expect(page).to have_content(/Practice Test Phase/i)
        expect(page).to have_selector('section[data-view-type="mission"]')
      end

      it "shows subsections in module" do
        expect(page).to have_content(/Learning Phase/i)

        study_module = user.study_plan.study_modules.where(phase: "learning").first

        find("#study-module-#{study_module.id}").click

        quant_section = study_module.study_module_subsections.where(section: "quant").first
        verbal_section = study_module.study_module_subsections.where(section: "verbal").first

        expect(page).to have_content(/#{quant_section.name}/i)
        expect(page).to have_content(/#{verbal_section.name}/i)
      end

      it "marks mission as completed" do
        study_module = user.study_plan.study_modules.where(phase: "learning").first

        find("#study-module-#{study_module.id}").click

        first_module_subsection = user.study_plan.study_modules.first.study_module_subsections.first

        within "#study-module-subsection-#{first_module_subsection.id}" do
          expect(page).to have_content(/NOT COMPLETED/i)
          expect(page).to have_content(/MARK AS COMPLETE/i)

          first_module_subsection.study_module_items.each do |item|
            check("study-module-item-#{item.id}-checkbox", allow_label_click: true)
          end

          expect(page).to have_content(/COMPLETED/i)
          expect(page).to have_content(/MARK AS INCOMPLETE/i)
        end
      end

      it "renders adjust study plan" do
        expect(page).to have_current_path(study_plan_path)

        first("span.study-plan-button a").click

        expect(page).to have_current_path(adjust_study_plan_path)

        course = EXAM_NAME.upcase
        expect(page).to have_content("Adjust #{course} Study Plan")

        # partials
        expect(page).to have_selector(".test-dates-container")
        expect(page).to have_selector("#score-bar")
        expect(page).to have_selector(".study-plan-view-card.mission-view.active")
      end

      context "OnDemand" do
        let(:chapter) { FactoryBot.create(:chapter, :with_topics) }
        let!(:study_module) { user.study_plan.study_modules.first }
        let!(:study_module_subsection) { FactoryBot.create(:study_module_subsection, study_module: study_module, chapter: chapter) }
        let!(:study_module_item) { FactoryBot.create(:study_module_item, study_module_subsection: study_module_subsection, item_type: "reading") }

        before do
          user.instance_variable_set(:@mission_study_plan_completion, nil)
          user.mission_study_plan_completion.regenerate_data
        end

        it "enables ondemand trial" do
          within ".ondemand-switch" do
            find("a.status").click
            find(".ondemand-toggle").click
          end

          expect(page).to have_content(/You're about to switch to the TTP GMAT OnDemand Trial/i)

          within "#try-ondemand" do
            click_link "Confirm"
          end

          expect(page).to have_content(/Watch the OnDemand video classes for this chapter and read the supplementary lessons/i)
        end
      end
    end

    context "when study plan view is calendar view" do
      before do
        Track.all.each { |track| track.send(:regenerate_calendar_structure) }

        within "#calendar-setup-modal" do
          within "section#view-preferences" do
            find(".study-plan-view-card.calendar-view").click
            click_button "Confirm selection"
          end

          within "section#date-time-preferences" do
            find("input#study_plan_setting_study_hours_per_week").set("21")
            click_button "Next"
          end

          within "section#availability" do
            find("#calendar .date.today").click
            click_button "Finish"
          end
        end
      end

      it "renders calendar properly" do
        expect(page).to have_current_path(study_plan_path)
        expect(page).to have_content(/Today/i)
        expect(page).to have_content(/All Tasks/i)
        expect(page).to have_content("#{Date.current.strftime('%B')} #{Date.current.strftime('%Y')}")
        expect(page).to have_selector('section[data-view-type="calendar"]')
      end

      it "shows date in calendar" do
        expect(page).to have_selector("div", class: "date")
      end

      it "shows date with tasks in calendar" do
        expect(page).to have_selector(".date.with-tasks")
      end

      it "opens tasks modal for date which contains tasks" do
        task = user.user_calendar_tasks.first

        find(".date.with-tasks[data-date='#{task.schedule_date}'").click

        within ".modal.tasks-modal[data-for-date='#{task.schedule_date}'" do
          expect(page).to have_content(task.schedule_date.strftime("%a, %B %d, %Y"))
          expect(page).to have_content(calendar_task_title(task, user).to_s)
        end
      end

      it "marks task as completed" do
        task = user.user_calendar_tasks.first

        find(".date.with-tasks[data-date='#{task.schedule_date}'").click

        within ".modal.tasks-modal[data-for-date='#{task.schedule_date}'" do
          expect(page).to have_content(/MARK AS COMPLETE/i)

          check("tasks-modal-item-#{task.id}-checkbox", allow_label_click: true)

          expect(page).to have_content(/MARK AS INCOMPLETE/i)
        end
      end

      it "marks task as incompleted" do
        task = user.user_calendar_tasks.first

        find(".date.with-tasks[data-date='#{task.schedule_date}'").click

        within ".modal.tasks-modal[data-for-date='#{task.schedule_date}'" do
          check("tasks-modal-item-#{task.id}-checkbox", allow_label_click: true)

          expect(page).to have_content(/MARK AS INCOMPLETE/i)

          check("tasks-modal-item-#{task.id}-checkbox", allow_label_click: true)

          expect(page).to have_content(/MARK AS COMPLETE/i)
        end
      end

      it "opens unavailable to study modal for day marked excluded" do
        expect(page).to have_css(".date.excluded", wait: 10)

        page.find(".date.excluded").click until page.has_css?(".modal#calendar-unavailable-study-day", wait: 1)

        within ".modal#calendar-unavailable-study-day" do
          expect(page).to have_content(/This day is marked as unavailable to study/i, wait: 10)
        end
      end

      it "renders adjust study plan" do
        expect(page).to have_current_path(study_plan_path)

        first("span.study-plan-button a").click

        expect(page).to have_current_path(adjust_study_plan_path)
        course = EXAM_NAME.upcase
        expect(page).to have_content("Adjust #{course} Study Plan")

        # partials
        expect(page).to have_selector(".test-dates-container")
        expect(page).to have_selector("#score-bar")
        expect(page).to have_selector(".study-plan-view-card.calendar-view.active")
      end

      context "when calendar view is selected to month" do
        it "renders calendar properly with month view" do
          expect(page).to have_content(/Month/i)
          expect(page).to have_selector('#calendar[data-calendar-view-type="month"]')
        end
      end

      context "when calendar view is selected to week" do
        it "renders calendar properly with week view" do
          find("#view-type-dropdown").click
          find("a.week-view").click

          expect(page).to have_selector('#calendar[data-calendar-view-type="week"]')
          expect(page).to have_content(/Week/i)
        end
      end

      context "OnDemand Calendar View" do
        let(:chapter) { FactoryBot.create(:chapter, :with_topics) }
        let!(:study_module_1) { user.study_plan.study_modules.first }
        let!(:study_module_subsection_1) { FactoryBot.create(:study_module_subsection, study_module: study_module_1, chapter: chapter) }
        let!(:study_module_item_1) { FactoryBot.create(:study_module_item, study_module_subsection: study_module_subsection_1, item_type: "reading") }
        let!(:user_calendar_1) { user.user_calendar }
        let!(:user_calendar_task_1) { FactoryBot.create(:user_calendar_task, topic_from: 3, study_module_item: study_module_item_1) }

        before do
          user.update(ondemand_enabled: true)
          user.instance_variable_set(:@calendar_study_plan_completion, nil)
          user.calendar_study_plan_completion.regenerate_data
          allow(self).to receive(:formatted_chapter_name).and_return(chapter.name)
          allow(user_calendar_task_1).to receive(:completion_for).with(user).and_return({ total: 1, completed: 0, percentage: 0, last_access: nil, is_completed: false, completed_at: nil })
          allow(self).to receive(:current_user).and_return(user)
        end

        it "returns correct task title" do
          task_title = calendar_task_title(user_calendar_task_1, user)
          expect(task_title).to eq(calendar_task_title(user_calendar_task_1, user))
        end
      end
    end
  end
end
