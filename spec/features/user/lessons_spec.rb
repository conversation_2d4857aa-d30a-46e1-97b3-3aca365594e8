require "spec_helper"

RSpec.feature "User accesses lesson page", js: true do
  let!(:user) { create(:member) }
  let!(:topic) { FactoryBot.create(:topic) }
  let!(:flag) { FactoryBot.create(:flag, label: "first_visit_for_chat_ai_modal_acknowledged", user: user, flaggable: user) }

  context "when logged out" do
    it "redirects to log in page" do
      visit lesson_path(topic)
      expect(page).to have_current_path(new_user_session_path)
    end
  end

  context "when logged in" do
    before :each do
      login(user)

      visit lesson_path(topic)
    end

    it "renders properly" do
      expect(page).to have_current_path(lesson_path(topic))
      expect(page).to have_content(topic.chapter.name)
      expect(page).to have_content(topic.name)
    end

    context "when topic contains empty question type examples" do
      let!(:topic_with_empty_questions) { FactoryBot.create(:topic_with_empty_question_type_examples) }

      before do
        FactoryBot.create(:example, question_type: "empty_question", lesson: topic)

        visit lesson_path(topic_with_empty_questions)
      end

      it "renders the empty type question" do
        expect(page).to have_selector("div.example.empty_question[data-id='#{topic_with_empty_questions.examples.first.id}']")
      end
    end

    context "when topic contains vocab list" do
      let!(:topic_with_vocab_list) { FactoryBot.create(:topic_with_vocab_list_in_content) }

      before do
        visit lesson_path(topic_with_vocab_list)
      end

      it "renders the vocab list" do
        expect(page).to have_content(/Vocab Word List - Chapter/i)
        expect(page).to have_selector("div.word-definition")
        expect(page).to have_selector("div.vocab-interactive-list a")
        expect(page).to have_current_path(lesson_path(topic_with_vocab_list))
      end
    end

    context "User accesses the open chat ai" do
      let!(:topic) { FactoryBot.create(:topic) }

      before do
        visit lesson_path(topic)
      end

      it "renders properly" do
        find(:css, ".chat-side-icon")
        find(:css, ".chat-icon-image")
      end
    end

    context "User accesses AI example" do
      let!(:user) { create(:member, on_trial: true) }
      let!(:topic) { FactoryBot.create(:topic_with_example) }
      let!(:exercise) { topic.examples.first }
      let!(:exercise_attempt_1) { FactoryBot.create(:exercise_attempt, exercise: exercise, user: user) }

      before do
        FactoryBot.create(:flag, label: "first_visit_for_ai_learning_disclaimer_modal", user: user, flaggable: user)
        visit lesson_path(topic)
        find(".ai-example-assist-btn.ai-example").click
      end

      it "shows the AI Learning Disclaimer Modal on first visit" do
        Flag.where(user_id: user.id, flaggable: user, label: "first_visit_for_ai_learning_disclaimer_modal").first.destroy
        visit lesson_path(topic)

        find(".ai-example-assist-btn.ai-example").click

        expect(page).to have_selector("#ai-learning-disclaimer-modal-#{exercise.id}")

        find(".ai-learning-modal .btn-close").click
      end

      it "shows the AI example generator modal" do
        expect(page).to have_content("AI Example Generator")
        expect(page).to have_button("GENERATE")
        expect(page).to have_button("CANCEL")
        find(".generate-btn").click
        expect(exercise_attempt_1.user).to eq(user)
      end
    end

    describe "User Accesses AI Tutor" do
      context "when the user is trial user" do
        let!(:user) { create(:member, on_trial: true) }
        let!(:topic) { FactoryBot.create(:topic_with_example) }

        before do
          visit lesson_path(topic)
          user.lesson_feedbacks.destroy_all
          user.update(ai_tutor_hint_counter: 25)
          find(".ai-tutor-btn").click

          expect(page).to have_content("Need a Little Help?")
        end

        it "hint limit reached" do
          find("#hint-button").click

          expect(page).to have_content("You've reached your TTP AI Assist message limit. To continue using the service, upgrade to TTP paid plan for unlimited access.")
        end
      end

      context "when the user is non trial user" do
        let!(:user) { create(:member) }
        let!(:topic) { FactoryBot.create(:topic_with_example) }

        before do
          visit lesson_path(topic)

          find(".ai-tutor-btn").click

          expect(page).to have_content("Need a Little Help?")
        end

        it "renders properly" do
          find("#hint-button").click
        end
      end
    end

    describe "User Accesses AI flashcard" do
      context "when user creates flashcard" do
        let!(:user) { create(:member, on_trial: true) }
        let!(:topic) { FactoryBot.create(:topic_with_example) }

        before do
          visit lesson_path(topic)
        end

        it "shows the AI example generator modal" do
          find(".flashcard-btn.btn ").click
          expect(page).to have_content("CREATE FLASHCARDS")
          expect(page).to have_content("How many cards do you want to generate?")
          expect(page).to have_content("WRITE AN EXACT NUMBER")
        end
      end
    end

    describe "User Accesses AI Summary" do
      context "when user creates Summary" do
        let!(:user) { create(:member, on_trial: true) }
        let!(:topic) { FactoryBot.create(:topic_with_example) }
        let!(:exercise) { topic.examples.first }
        let!(:exercise_attempt_1) { FactoryBot.create(:exercise_attempt, exercise: exercise, user: user) }

        before do
          visit lesson_path(topic)
        end

        it "shows the AI summary modal" do
          find(".summarize-btn").click
          expect(page).to have_content("Summarize Lesson with AI")
        end
      end
    end
  end

  describe "when user has enabled ondemand" do
    let!(:user) { create(:member) }
    let!(:study_plan) { create(:study_plan, :complete, track: user.track) }

    context "when ondemand is enabled" do
      before :each do
        Setting.create(key: "ondemand_enabled", value: true)

        user.update(ondemand_enabled: true)

        login(user)

        visit lesson_path(topic)
      end

      context "when the chapter is unlocked" do
        before :each do
          allow_any_instance_of(User).to receive(:ondemand_subscription?).and_return(true)
          visit lesson_path(topic)
        end

        it "renders the ondemand read lesson container" do
          expect(page).to have_content("Read the Text Lesson")
          expect(page).to have_css("span.read-lesson-img")
          expect(page).to have_css("span.dots-image")
          expect(page).to have_css("div.question-image")
          expect(page).to have_content("Watch the OnDemand Video Lesson")
          expect(page).to have_content("Should You Watch the Video or Read the Lesson?")
        end
      end

      context "when the chapter is locked" do
        before :each do
          allow_any_instance_of(User).to receive(:ondemand_subscription?).and_return(false)
          allow_any_instance_of(User).to receive(:unlocked_chapter_ids_for_ondemand_trial).and_return([])

          visit lesson_path(topic)
        end

        it "does not render the ondemand read lesson container" do
          expect(page).not_to have_content("Read the Text Lesson")
          expect(page).not_to have_css("span.read-lesson-img")
          expect(page).not_to have_css("span.dots-image")
        end
      end
    end
  end
end
