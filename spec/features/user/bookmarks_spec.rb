require "spec_helper"
RSpec.feature "User accesses bookmarks page", js: true do
  context "when logged out" do
    it "redirect to login page" do
      visit chapter_list_flags_path(section: "quant")
      expect(page).to have_current_path(new_user_session_path)
    end
  end

  context "when logged in" do
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }
    let!(:chapter) { FactoryBot.create(:chapter, section: section, subsection: subsection) }
    let!(:instructors) { FactoryBot.create_list(:instructor, 5) }
    let!(:online_class) { FactoryBot.create(:online_class, :with_sessions, :skip_validate, instructor: instructors.first) }
    let!(:online_class_session) do
      session = online_class.sessions.first
      session.update!(recording_url: "http://example.com/recording.mp4", show_in_library: true)
      session
    end

    let!(:lesson) { FactoryBot.create(:topic, chapter: chapter, parent: chapter) }
    let!(:problem) { FactoryBot.create(:problem, chapter: chapter) }
    let!(:must_know) { FactoryBot.create(:must_know, lesson: lesson) }
    let!(:concept_mastery) { FactoryBot.create(:concept_mastery, chapter: chapter, lesson: lesson) }

    let!(:lesson_bookmark) { FactoryBot.create(:flag, :label_review, user: user, flaggable: lesson) }
    let!(:problem_bookmark) { FactoryBot.create(:flag, :label_review, user: user, flaggable: problem) }
    let!(:must_know_bookmark) { FactoryBot.create(:flag, :label_review, user: user, flaggable: must_know) }
    let!(:concept_mastery_bookmark) { FactoryBot.create(:flag, :label_review, user: user, flaggable: concept_mastery) }

    let!(:chapter_2) { FactoryBot.create(:chapter) }
    let!(:lesson_2) { FactoryBot.create(:topic, parent: chapter) }
    let!(:problem_2) { FactoryBot.create(:problem, chapter: chapter) }
    let!(:must_know_2) { FactoryBot.create(:must_know, lesson: lesson) }
    let!(:concept_mastery_2) { FactoryBot.create(:concept_mastery, chapter: chapter, lesson: lesson) }
    let!(:flag) { FactoryBot.create(:flag, label: "first_visit_for_chat_ai_modal_acknowledged", user: user, flaggable: user) }

    before :each do
      login(user)

      visit chapter_list_flags_path(section: section)
    end

    context "when QUANT" do
      let(:user) { FactoryBot.create(:member) }
      let(:section) { "quant" }
      let(:subsection) { nil }

      it "renders properly" do
        visit root_path

        within "nav.navbar" do
          click_link "Bookmarks"
        end

        expect(page).to have_current_path(chapter_list_flags_path(section: section))

        expect(page).to have_link("SUMMARY", href: chapter_list_flags_path(section))
        expect(page).to have_link("BOOKMARKS", href: flags_path(section, flaggable_type: "topic"))
        expect(page).to have_link("NOTES", href: notes_flags_path(section))

        expect(page).to have_content(/#{chapter.name}/i)
      end

      context "when SUMMARY tab selected" do
        it "shows per-chapter bookmarks list" do
          selector = ".ttp-datatable > tbody > tr:first-child td"
          within "#{selector}:nth-child(2)" do
            # lessons
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(3)" do
            # examples
            expect(page).to have_content("0")
          end

          within "#{selector}:nth-child(4)" do
            # problems
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(5)" do
            # must knows
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(6)" do
            # notes
            expect(page).to have_content("0")
          end
        end

        it "hasn't Concept Mastery bookmark tag" do
          expect(page).not_to have_content("CONCEPT MASTERIES")
        end

        it "shows Problems Bookmark tag" do
          expect(page).not_to have_content("QUESTIONS")
          expect(page).to have_content("PROBLEMS")
        end

        it "bookmarks another Lesson from the same chapter" do
          visit lessons_path(section: section)

          find("#chapter-#{chapter.id} .progress-dial").click
          find("#chapter-modal-#{chapter.id} .modal-body .topics-list li:nth-child(2) a").click

          expect(page).to have_content(html_text(lesson_2.content), wait: 5)

          find(".flags a.bookmark").click

          expect(page).to have_selector(".flags a.bookmark.flagged")

          visit chapter_list_flags_path(section: section)

          selector = ".ttp-datatable > tbody > tr:first-child td"

          within "#{selector}:nth-child(2)" do
            expect(page).to have_content("2")
          end

          within "#{selector}:nth-child(3)" do
            expect(page).to have_content("0")
          end

          within "#{selector}:nth-child(4)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(5)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(6)" do
            expect(page).to have_content("0")
          end
        end

        it "bookmarks another Problem from the same chapter" do
          visit show_problem_path(id: problem_2.id)

          expect(page).to have_content(html_text(problem_2.content))
          expect(page).to have_selector(".flag-icon-link")

          find(".flag-icon-link").click

          expect(page).to have_selector(".flag-icon-link.flagged")

          visit chapter_list_flags_path(section: section)

          selector = ".ttp-datatable > tbody > tr:first-child td"

          within "#{selector}:nth-child(2)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(3)" do
            expect(page).to have_content("0")
          end

          within "#{selector}:nth-child(4)" do
            expect(page).to have_content("2")
          end

          within "#{selector}:nth-child(5)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(6)" do
            expect(page).to have_content("0")
          end
        end

        it "bookmarks another Must Knows from the same chapter" do
          visit lesson_path(lesson)

          expect(page).to have_content(html_text(lesson.name))

          expect(page).to have_content(html_text(must_know.content))

          expect(page).to have_content(html_text(must_know_2.content))

          expect(page).to have_selector("#must_know-#{must_know.id} .header a.flagged")

          expect(page).to have_selector("#must_know-#{must_know_2.id} .header a")

          find("#must_know-#{must_know_2.id} .header a").click

          expect(page).to have_selector("#must_know-#{must_know_2.id} .header a.flagged")

          visit chapter_list_flags_path(section: section)

          selector = ".ttp-datatable > tbody > tr:first-child td"

          within "#{selector}:nth-child(2)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(3)" do
            expect(page).to have_content("0")
          end

          within "#{selector}:nth-child(4)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(5)" do
            expect(page).to have_content("2")
          end

          within "#{selector}:nth-child(6)" do
            expect(page).to have_content("0")
          end
        end

        it "creates a Note from a Lesson" do
          take_note_in(lesson)

          expect(page).to have_content(html_text(lesson.name))

          visit chapter_list_flags_path(section: section)

          selector = ".ttp-datatable > tbody > tr:first-child td"

          within "#{selector}:nth-child(2)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(3)" do
            expect(page).to have_content("0")
          end

          within "#{selector}:nth-child(4)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(5)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(6)" do
            expect(page).to have_content("1")
          end
        end

        it "creates a Note from a Problem" do
          visit show_problem_path(id: problem.id)

          expect(page).to have_content(html_text(problem.content))

          all(".noteable p span.notetaking-preselection").first.click

          fill_in "note[annotation]", with: "User annotation"

          find(".btn.save-note").click

          visit chapter_list_flags_path(section: section)

          selector = ".ttp-datatable > tbody > tr:first-child td"

          within "#{selector}:nth-child(2)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(3)" do
            expect(page).to have_content("0")
          end

          within "#{selector}:nth-child(4)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(5)" do
            expect(page).to have_content("1")
          end

          within "#{selector}:nth-child(6)" do
            expect(page).to have_content("1")
          end
        end

        context "when choose a Chapter" do
          context "without bookmarks" do
            it "shows no bookmarks message" do
              visit chapter_flags_path(section: section, chapter_id: chapter_2)

              expect(page).to have_content("You don’t have bookmarks or notes yet")
            end
          end

          context "with bookmarks" do
            before :each do
              click_link chapter.name
            end

            it "navigates to the Lesson bookmarked" do
              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
              expect(page).to have_selector(".topic-bookmark .content a:not(.delete-bookmark)")

              find(".topic-bookmark .content a:not(.delete-bookmark)").click

              expect(page).to have_selector(".topic-bookmark li.hidden.no-bookmarks-message", visible: false)

              # Page opens in a new tab
              page.switch_to_window(page.windows[1])

              expect(page).to have_content(html_text(lesson.name))
            end

            it "navigates to the Problem bookmarked" do
              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))

              expect(page).to have_selector(".problem-bookmark .content a")
              expect(page).to have_selector(".problem-bookmark li.hidden.no-bookmarks-message", visible: false)

              find(".problem-bookmark .content a:not(.delete-bookmark)").click

              # Page is opened in a new tab
              page.switch_to_window(page.windows[1])

              expect(page).to have_content(html_text(problem.content))
            end

            it "navigates to the Must Know bookmarked" do
              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))

              expect(page).to have_selector(".must_know-bookmark .content a:not(.delete-bookmark)")
              expect(page).to have_selector(".must_know-bookmark li.hidden.no-bookmarks-message", visible: false)

              execute_script("$('.must_know-bookmark .content a:not(.delete-bookmark').text('Testing purpose - Selenium does not accept empty link')")

              find(".must_know-bookmark .content a:not(.delete-bookmark)").click

              # Page is opened in a new tab
              page.switch_to_window(page.windows[1])

              expect(page).to have_content(html_text(must_know.content))
            end

            it "shows all notes in that chapter" do
              take_note_in(lesson)

              # create a note in problem
              visit show_problem_path(id: problem.id)

              all(".noteable p span.notetaking-preselection").first.click

              fill_in "note[annotation]", with: "User annotation"

              find(".btn.save-note").click

              # navigate to bookmarks
              visit chapter_flags_path(section: section, chapter_id: chapter)

              expect(page).to have_content(html_text(chapter.name))

              expect(page).to have_selector("#view-all-note a")

              find("#view-all-note a").click

              expect(page).to have_current_path(details_flags_path(section: section, chapter_id: chapter.id, flaggable_type: "note"))

              expect(page).to have_content("Test Problems")

              expect(page).to have_selector(".accordion ul li", count: 2)
            end

            it "removes a bookmarked lesson" do
              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
              expect(page).to have_selector(".topic-bookmark li:not(#no-bookmarks-topic)", count: 1)

              find(".topic-bookmark .content a.delete-bookmark").click

              find(".modal#confirmation-modal button.confirm").click

              expect(page).to have_selector(".topic-bookmark li:not(#no-bookmarks-topic)", count: 0)

              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
            end

            it "removes a bookmarked problem" do
              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
              expect(page).to have_selector(".problem-bookmark li:not(#no-bookmarks-problem)", count: 1)

              find(".problem-bookmark .content a.delete-bookmark").click
              find(".modal#confirmation-modal button.confirm").click

              expect(page).to have_selector(".problem-bookmark li:not(#no-bookmarks-problem)", count: 0)

              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
            end

            it "removes a bookmarked must knows" do
              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
              expect(page).to have_selector(".must_know-bookmark li:not(#no-bookmarks-must_know)", count: 1)

              find(".must_know-bookmark .content a.delete-bookmark").click

              find(".modal#confirmation-modal button.confirm").click

              expect(page).to have_selector(".must_know-bookmark li:not(#no-bookmarks-must_know)", count: 0)

              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
            end

            it "removes a bookmarked must knows" do
              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
              expect(page).to have_selector(".must_know-bookmark li:not(#no-bookmarks-must_know)", count: 1)

              find(".must_know-bookmark .content a.delete-bookmark").click
              find(".modal#confirmation-modal button.confirm").click

              expect(page).to have_selector(".must_know-bookmark li:not(#no-bookmarks-must_know)", count: 0)

              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
            end

            it "removes a bookmarked note" do
              take_note_in(lesson)

              visit chapter_flags_path(section: section, chapter_id: chapter)

              expect(page).to have_selector(".note-bookmark li:not(#no-bookmarks-note)", count: 1)

              find(".note-bookmark li a.delete-bookmark").click
              find(".modal#confirmation-modal button.confirm").click

              expect(page).to have_selector(".note-bookmark li:not(#no-bookmarks-note)", count: 0)

              expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
            end

            context "when lesson is archived" do
              let!(:lesson_3) { FactoryBot.create(:topic, :archived, parent: chapter) }
              let!(:lesson_bookmark) { FactoryBot.create(:flag, :label_review, user: user, flaggable: lesson_3) }

              it "shows archived tag" do
                expect(page).to have_selector(".topic-bookmark li span.badge", text: "Archived")
                expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))
              end

              it "navigates to Lesson with 'Archived badge'" do
                find(".topic-bookmark .content a:not(.delete-bookmark)").click

                page.switch_to_window(page.windows[1])

                expect(page).to have_content(html_text(lesson_3.name))
              end

              it "navigates to note with 'Archived' badge" do
                take_note_in(lesson)
                lesson.update_column(:archived_at, Time.zone.now)

                visit chapter_flags_path(section: section, chapter_id: chapter)

                expect(page).to have_selector(".note-bookmark .card span.badge", text: "Archived")

                find(".note-bookmark .card a:not(.delete-bookmark)").click

                page.switch_to_window(page.windows[1])

                expect(page).to have_content(html_text(lesson.name))
              end
            end
          end
        end
      end

      context "when BOOKMARKS tab selected" do
        it "renders properly" do
          click_link "BOOKMARKS"
          expect(page).to have_current_path(flags_path(section: section, flaggable_type: "topic"))
        end

        context "when Liveteach tab selected" do
          let!(:online_class_session_bookmark) { FactoryBot.create(:flag, :label_review, user: user, flaggable: online_class_session) }

          before :each do
            click_link "BOOKMARKS"
            click_link "LiveTeach"
          end
          it "renders properly" do
            expect(page).to have_current_path(flags_path(section: section, flaggable_type: "online_class_session"))
          end

          it "navigates to the Online Class bookmarked details page" do
            find(".content-bookmarks-instructor .view-all-link").click

            expect(page).to have_content(html_text(instructors.first.name))
          end
        end
      end

      context "when NOTES tab selected" do
        it "renders properly" do
          click_link "NOTES"
          expect(page).to have_current_path(notes_flags_path(section: section))
        end
      end
    end

    context "when VERBAL" do
      let(:user) { FactoryBot.create(:member) }
      let(:section) { "verbal" }
      let(:subsection) { ["sentence correction", "critical reasoning"].sample }

      before :each do
        visit chapter_list_flags_path(section: section)
      end

      it "renders properly" do
        expect(page).to have_content(/#{chapter.name}/i)
      end

      it "shows per-chapter bookmarks list" do
        selector = ".ttp-datatable > tbody > tr:first-child td"

        within "#{selector}:nth-child(2)" do
          # lessons
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(3)" do
          # examples
          expect(page).to have_content("0")
        end

        within "#{selector}:nth-child(4)" do
          # concept masteries
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(5)" do
          # questions
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(6)" do
          # must knows
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(7)" do
          # notes
          expect(page).to have_content("0")
        end
      end

      it "shows concept mastery bookmark tag" do
        expect(page).to have_content("CONCEPT MASTERIES")
      end

      it "shows Problems Bookmark tag" do
        expect(page).not_to have_content("PROBLEMS")
        expect(page).to have_content("QUESTIONS")
      end

      it "navigates to the Lesson bookmarked" do
        click_link chapter.name

        expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))

        expect(page).to have_selector(".topic-bookmark .content a")

        find(".topic-bookmark .content a:not(.delete-bookmark)").click

        # Page is opened in a new tab
        page.switch_to_window(page.windows[1])

        expect(page).to have_content(html_text(lesson.name))
      end

      it "navigates to the Question bookmarked" do
        click_link chapter.name

        expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))

        expect(page).to have_selector(".problem-bookmark .content a")

        find(".problem-bookmark .content a:not(.delete-bookmark)").click

        # Page is opened in a new tab
        page.switch_to_window(page.windows[1])

        expect(page).to have_content(html_text(problem.content))
      end

      it "navigates to the Concept Mastery bookmarked" do
        click_link chapter.name

        expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))

        expect(page).to have_selector(".concept_mastery-bookmark .content a")

        find(".concept_mastery-bookmark .content a:not(.delete-bookmark)").click

        # Page is opened in a new tab
        page.switch_to_window(page.windows[1])

        expect(page).to have_content(html_text(concept_mastery.content))
      end

      it "navigates to the Must Know bookmarked" do
        click_link chapter.name

        expect(page).to have_current_path(chapter_flags_path(section: section, chapter_id: chapter))

        expect(page).to have_selector(".must_know-bookmark .content a:not(.delete-bookmark)")

        execute_script("$('.must_know-bookmark .content a:not(.delete-bookmark').text('Testing purpose - Selenium does not accept empty link')")

        find(".must_know-bookmark .content a:not(.delete-bookmark)").click

        # Page is opened in a new tab
        page.switch_to_window(page.windows[1])

        expect(page).to have_content(html_text(must_know.content))
      end

      it "bookmarks another Lesson from the same chapter" do
        visit lessons_path(section: section)

        find("#chapter-#{chapter.id} .progress-dial").click
        find("#chapter-modal-#{chapter.id} .modal-body .topics-list li:nth-child(2) a").click

        expect(page).to have_content(html_text(lesson_2.name))

        find(".flags a.bookmark").click

        expect(page).to have_selector(".flags a.bookmark.flagged")

        visit chapter_list_flags_path(section: section)

        selector = ".ttp-datatable > tbody > tr:first-child td"

        within "#{selector}:nth-child(2)" do
          expect(page).to have_content("2")
        end

        within "#{selector}:nth-child(3)" do
          expect(page).to have_content("0")
        end

        within "#{selector}:nth-child(4)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(5)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(6)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(7)" do
          expect(page).to have_content("0")
        end
      end

      it "bookmarks another Question from the same chapter" do
        visit show_problem_path(id: problem_2.id)

        expect(page).to have_content(html_text(problem_2.content))
        expect(page).to have_selector(".flag-icon-link")

        find(".flag-icon-link").click

        expect(page).to have_selector(".flag-icon-link.flagged")

        visit chapter_list_flags_path(section: section)

        selector = ".ttp-datatable > tbody > tr:first-child td"

        within "#{selector}:nth-child(2)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(3)" do
          expect(page).to have_content("0")
        end

        within "#{selector}:nth-child(4)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(5)" do
          expect(page).to have_content("2")
        end

        within "#{selector}:nth-child(6)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(7)" do
          expect(page).to have_content("0")
        end
      end

      it "bookmarks another Concept Mastery from the same chapter" do
        visit lesson_path(lesson)

        expect(page).to have_content(html_text(lesson.name))

        expect(page).to have_selector("#concept_mastery-#{concept_mastery_2.id} .flags a.flag_link")

        find("#concept_mastery-#{concept_mastery_2.id} .flags a.flag_link").click

        expect(page).to have_selector("#concept_mastery-#{concept_mastery_2.id} .flags a.flag_link.flagged")

        visit chapter_list_flags_path(section: section)

        selector = ".ttp-datatable > tbody > tr:first-child td"

        within "#{selector}:nth-child(2)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(3)" do
          expect(page).to have_content("0")
        end

        within "#{selector}:nth-child(4)" do
          expect(page).to have_content("2")
        end

        within "#{selector}:nth-child(5)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(6)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(7)" do
          expect(page).to have_content("0")
        end
      end

      it "bookmarks another Must Know from the same chapter" do
        visit lesson_path(lesson)

        expect(page).to have_content(html_text(must_know_2.content))

        expect(page).to have_selector("#must_know-#{must_know.id} .header a.flagged")

        expect(page).to have_selector("#must_know-#{must_know_2.id} .header a")

        find("#must_know-#{must_know_2.id} .header a").click

        expect(page).to have_selector("#must_know-#{must_know_2.id} .header a.flagged")

        visit chapter_list_flags_path(section: section)

        selector = ".ttp-datatable > tbody > tr:first-child td"

        within "#{selector}:nth-child(2)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(3)" do
          expect(page).to have_content("0")
        end

        within "#{selector}:nth-child(4)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(5)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(6)" do
          expect(page).to have_content("2")
        end

        within "#{selector}:nth-child(7)" do
          expect(page).to have_content("0")
        end
      end

      it "creates a Note from a Lesson" do
        take_note_in(lesson)
        expect(page).to have_content(html_text(lesson.name))

        visit chapter_list_flags_path(section: section)

        selector = ".ttp-datatable > tbody > tr:first-child td"

        within "#{selector}:nth-child(2)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(3)" do
          expect(page).to have_content("0")
        end

        within "#{selector}:nth-child(4)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(5)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(6)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(7)" do
          expect(page).to have_content("1")
        end
      end

      it " creates a Note from a Problem" do
        visit show_problem_path(id: problem.id)

        expect(page).to have_content(html_text(problem.content))

        all(".noteable p span.notetaking-preselection").first.click

        fill_in "note[annotation]", with: "User annotation"

        find(".btn.save-note").click

        visit chapter_list_flags_path(section: section)

        selector = ".ttp-datatable > tbody > tr:first-child td"

        within "#{selector}:nth-child(2)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(3)" do
          expect(page).to have_content("0")
        end

        within "#{selector}:nth-child(4)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(5)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(6)" do
          expect(page).to have_content("1")
        end

        within "#{selector}:nth-child(7)" do
          expect(page).to have_content("1")
        end
      end

      it "show all notes in that chapter" do
        take_note_in(lesson)

        # create a note in problem
        visit show_problem_path(id: problem.id)

        all(".noteable p span.notetaking-preselection").first.click

        fill_in "note[annotation]", with: "User annotation"

        find(".btn.save-note").click

        # navigate to bookmarks
        visit chapter_flags_path(section: section, chapter_id: chapter)

        expect(page).to have_content(html_text(chapter.name))

        expect(page).to have_selector("#view-all-note a")

        find("#view-all-note a").click

        expect(page).to have_current_path(details_flags_path(section: section, chapter_id: chapter.id, flaggable_type: "note"))

        expect(page).to have_content("Test Problems")

        expect(page).to have_selector(".accordion ul li", count: 2)
      end
    end
  end
end
