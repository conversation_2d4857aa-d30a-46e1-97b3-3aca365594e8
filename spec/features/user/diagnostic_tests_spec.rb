require "spec_helper"

RSpec.feature "User accesses Diagnostic test page", js: true do
  context "when logged out" do
    it "redirects to login page" do
      visit diagnostic_list_path
      expect(page).to have_current_path(new_user_session_path)
    end
  end

  context "when logged in" do
    let!(:user) { FactoryBot.create(:member) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }
    let!(:chapter) { FactoryBot.create(:chapter) }

    let!(:ps_problems) do
      FactoryBot.create_list(:problem, 4, chapter: chapter, question_type: "problem_solving", level: "E")
    end

    let!(:ds_problems) do
      FactoryBot.create_list(:problem, 4, chapter: chapter, question_type: "data_sufficiency", level: "E")
    end

    before :each do
      login(user)

      visit diagnostic_list_path
    end

    it "renders properly" do
      visit root_path

      within "nav.navbar" do
        click_link "Tests"

        click_on "Diagnostic Test"
      end

      expect(page).to have_content(/Diagnostic Test/i)
      expect(page).to have_content(/It’s important for us to get a better understanding of your current skills/)
    end

    it "takes the diagnostic test" do
      expect(page).to have_css(".actions-links a i.fal.fa-play")

      find(".actions-links a").click

      expect(page).to have_content(/About the Diagnostic Test/i)

      within "#diagnostic-info-modal-to-continue" do
        click_on "OK, CONTINUE"
      end

      expect(page).to have_content(/Set up your diagnostic test/i)

      within "#select-time-per-question-diagnostic" do
        click_on "Start Test"
      end

      expect(page).to have_current_path(/evaluation\/[0-9]+\/problem\/[0-9]+/)
    end

    context "when user has already taken a diagnostic test" do
      let!(:evaluation) { user.track.diagnostic }

      let!(:evaluation_attempt) do
        FactoryBot.create(:evaluation_attempt,
          user: user,
          evaluation: evaluation,
          start_time: 13.minutes.ago,
          end_time: Time.zone.now,
          total_elapsed_seconds: (evaluation.problems.count * 90),
          percentage_correct: 50)
      end

      before do
        total_elapsed_seconds = 0

        user.user_diagnostics.create(diagnostic: evaluation, track: user.track)

        evaluation.problems.each_with_index do |problem, index|
          problem.chapter = chapter
          problem.save
          evaluation_attempt.problem_attempts.create(
            evaluation_attempt: evaluation_attempt,
            problem: problem,
            start_time: evaluation_attempt.start_time + total_elapsed_seconds.seconds,
            end_time: evaluation_attempt.start_time + total_elapsed_seconds.seconds + 90.seconds,
            answer: index.even? ? problem.correct_option_index : "-1",
            correct: index.even?,
            total_elapsed_seconds: 90,
            is_last_attempt: true
          )

          total_elapsed_seconds += 90
        end

        visit diagnostic_list_path
      end

      it "shows finished status on test" do
        expect(page.find(".actions-links a i.fal.fa-file-alt").visible?).to be true
      end

      it "renders results page correctly" do
        visit evaluation_attempt_path(user.evaluation_attempts.first)

        expect(page).to have_content(/Diagnostic Test Results/i)
        expect(page).to have_content(/Questions/i)
        expect(page).to have_content(/6/i)
        expect(page).to have_content(/Test Time/i)
        expect(page).to have_content(/9 min 0 s/i)
        expect(page).to have_content(/Your Initial Level of Preparedness \(ILP\)/i)

        find("a", text: "What does this mean?").click

        within "#ilp-info-modal" do
          expect(page).to have_content(/OK, GOT IT/i)

          find("button", text: "OK, GOT IT").click
        end

        expect(page).to have_link("SEE PERFORMANCE BY TOPIC", href: ilp_by_chapter_evaluation_attempt_path(id: evaluation_attempt.id, section: "quant"))

        click_link "SEE PERFORMANCE BY TOPIC"

        expect(page).to have_content(/Performance by Topic/i)

        page.click_link("", href: "/evaluation_results/#{evaluation_attempt.id}")

        click_link "QUESTION REVIEW"

        expect(page).to have_content(/QUESTIONS/i)
        expect(page).to have_selector(".question-element .correct", count: 3)
        expect(page).to have_selector(".question-element .incorrect", count: 3)
      end
    end
  end
end
