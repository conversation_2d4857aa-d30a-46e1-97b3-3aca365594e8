require "spec_helper"

RSpec.feature "User visits homepage", js: true do
  context "when is logged out" do
    it "redirect login page" do
      visit home_path

      expect(page).to have_current_path(new_user_session_path)
    end
  end

  context "when is logged in" do
    let(:user) { FactoryBot.create(:member) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }
    let!(:ondemand_weekly_hour) { FactoryBot.create(:ondemand_weekly_hour, day: Time.zone.now.strftime("%A")) }
    let!(:instructor) { FactoryBot.create(:instructor, name: "<PERSON>") }
    let!(:weekly_instructor) { OndemandWeeklyHourInstructor.create(instructor_id: instructor.id) }

    before :each do
      login(user)

      visit root_path
    end

    it "redirects to home page" do
      expect(page).to have_current_path(home_path)
    end

    it "renders properly" do
      expect(page).to have_content(/Welcome back, /i)

      expect(page).to have_content(/SELF-STUDY/i)
      expect(page).to have_content(/LIVE CLASSES/i)

      expect(page).to have_content(/Course Completed/i)
      expect(page).to have_content(/Spent this week/i)
      expect(page).to have_content(/Your Study Plan/i)

      expect(page).to have_selector("footer#user-footer")

      within "footer#user-footer" do
        expect(page).to have_content("#{Date.current.year} © TargetTestPrep. All rights reserved")
      end
    end

    it "can be accessed from navbar" do
      visit analytics_path

      within "#user-navbar" do
        click_on "Home"
      end

      expect(page).to have_current_path(home_path)
    end

    it "can accessed self study content" do
      within ".header-sub-navigation" do
        click_on "self-study"
      end

      within "#self-study" do
        expect(page).to have_content(/Course Completed/i)
        expect(page).to have_content(/Spent this week/i)
        expect(page).to have_content(/Your Study Plan/i)
      end
    end

    it "can accessed promotional liveteach content" do
      within ".header-sub-navigation" do
        click_on "live classes"
      end

      within "#promotional-liveteach" do
        expect(page).to have_content(/Turbocharge your test prep with GMAT Live classes online/i)
        expect(page).to have_link("learn more")
        expect(page).to have_link("find a class now")
      end
    end

    context "with Online Class subscription" do
      let!(:online_class) { create(:online_class, :skip_validate) }
      let!(:sessions) { create_list(:online_class_session, 3, weeks_later: 4, online_class: online_class) }

      let!(:start_class) { online_class.first_class_session.starts_at }
      let!(:end_class) { online_class.last_class_session.ends_at }

      let!(:online_classes_user) { create(:online_classes_user, online_class_id: online_class.id, user_id: user.id) }

      it "can accessed live classes content" do
        visit root_path

        within ".header-sub-navigation" do
          click_on "live classes"
        end

        expect(page).to have_content(/Next Class/i)
        expect(page).to have_content(/Cohort Info/i)
        expect(page).to have_content(/Schedule/i)
        expect(page).to have_link("Download as pdf", href: online_class.syllabus_url)
      end
    end

    it "takes a diagnostic test" do
      click_on "Start it from here!"

      within "#diagnostic-info-modal-to-continue" do
        expect(page).to have_content("About the Diagnostic Test")
        expect(page).to have_selector("button#continue-next-modal-button")
        click_on "OK, CONTINUE"
      end

      within "#select-time-per-question-diagnostic" do
        expect(page).to have_content("Set Up your Diagnostic Test")
        expect(page).to have_link("time-per-question-diagnostic-modal-button")

        click_on "Start Test"
      end

      expect(page).to have_content("Problem Solving")
    end

    it "goes to the analytics page" do
      within ".course-stats.card-ttp" do
        expect(page).to have_link("", href: analytics_path)
        expect(page).to have_selector("a.show-overlay")

        click_link ""
      end

      expect(page).to have_current_path(analytics_path)
    end

    it "goes to the Study Plan page" do
      expect(page).to have_link("Study Plan", href: study_plan_path)

      click_on "Study Plan"

      expect(page).to have_current_path(study_plan_path)
    end

    it "shows the 'Quote of the day'" do
      within ".quote" do
        expect(page).to have_selector(".quote-container")
      end
    end

    it "does not render NPS modal" do
      expect(page).to_not have_content("How likely are you to recommend TTP to a friend based on your experience?")
    end

    context "when user has ondemand plan" do
      before :each do
        Flag.flag(user, user, "ondemand_congratulations_modal_acknowledged", true)
        Setting.create(key: "ondemand_enabled", value: true)
        allow_any_instance_of(User).to receive(:ondemand_active?).and_return(true)
        user.update(ondemand_enabled: true, full_ondemand_access: true)

        visit root_path
      end

      it "shows the referrals banner" do
        within ".messages" do
          expect(page).to have_selector("#referrals-banner-container")
        end
      end
    end

    context "when user has monthly plan" do
      before :each do
        Setting.create(key: "ondemand_enabled", value: true)
        user.update(current_plan: "4months")

        visit root_path
      end

      it "shows the try on ondemand banner" do
        within ".messages" do
          expect(page).to have_selector(".try-on-ondemand-banner")
        end
      end
    end

    context "with one Video" do
      let!(:video) { FactoryBot.create(:video_tip) }

      it "shows the video tip of the day" do
        visit current_path

        within ".video-preview" do
          expect(page).to have_link("Video Tip of the Day", href: videos_path)
          expect(page).to have_link("", href: video_path(id: video.id))

          within ".video-description" do
            expect(page).to have_content(video.title)
            expect(page).to have_content("0 min 50 s")
          end
        end
      end
    end

    context "with one Message" do
      let!(:message) { FactoryBot.create(:message, recipient_id: user.id) }

      before :each do
        visit current_path
      end

      it "shows Message" do
        visit current_path

        within ".messages" do
          expect(page).to have_content(/Message Board/i)

          within ".message-list" do
            expect(page).to have_selector(".message", count: 1)
            expect(page).to have_content("Test Message - Lorem Ipsus")
          end
        end
      end

      it "deletes Message" do
        within ".messages .message-list" do
          expect(page).to have_selector(".message", count: 1)

          within ".message" do
            expect(page).to have_selector("a[data-remote='true']")

            find(".actions a").click
          end
        end

        within "#confirmation-modal" do
          expect(page).to have_content("Are you sure you want to\ndelete this message?")

          click_on "Delete"
        end

        within ".messages" do
          expect(page).to have_selector(".no-messages")
        end
      end
    end

    context "user access notification model" do
      let!(:message) { FactoryBot.create(:message, recipient_id: user.id) }

      before :each do
        login(user)

        visit home_path
      end

      it "navigates to the notification modal, opens a message, and marks it as read" do
        find(".profile-menu > li.dropdown > a.dropdown-toggle").click
        expect(page).to have_link("New Messages")

        within "nav.navbar" do
          click_link("New Messages")
        end

        allow_any_instance_of(MessagesController).to receive(:load_and_authorize_resource)
        expect(page).to have_css("#notification-message-modal")
        expect(page).to have_content(/Messages /i)

        within "#notification-message-modal" do
          expect(page).to have_css(".message-list")

          first(".message-list").click_link("View More")
        end

        expect(page).to have_css(".message-modal")
        find(".message-modal .modal-content .modal-header .btn-close").click

        expect(message.mark_as_read!).to be_truthy

        expect(user.received_messages.last.read?).to be_truthy
        expect(page).to have_no_selector("#user-navbar .nav-link .active")
      end

      it "navigate to the notification modal without opening a message" do
        find(".profile-menu > li.dropdown > a.dropdown-toggle").click
        expect(page).to have_link("New Messages")

        within "nav.navbar" do
          click_link("New Messages")
        end

        allow_any_instance_of(MessagesController).to receive(:load_and_authorize_resource)
        expect(page).to have_css("#notification-message-modal")
        expect(page).to have_content(/Messages /i)

        within "#notification-message-modal" do
          expect(page).to have_css(".message-list")
        end

        expect(user.received_messages.last.read?).to be_falsey
        expect(page).to have_selector("#user-navbar .nav-link .active")
      end

      it "deletes Message" do
        within ".messages .message-list" do
          expect(page).to have_selector(".message", count: 1)

          within ".message" do
            expect(page).to have_selector("a[data-remote='true']")

            find(".actions a").click
          end
        end

        within "#confirmation-modal" do
          expect(page).to have_content("Are you sure you want to\ndelete this message?")

          click_on "Delete"
        end

        within ".messages" do
          expect(page).to have_selector(".no-messages")
        end
      end
    end

    context "with respond feedback messages from admin" do
      let!(:topic) { FactoryBot.create(:topic) }
      let(:admin) { FactoryBot.create(:admin) }
      let!(:lesson_feedback) { FactoryBot.create(:lesson_feedback, lesson: topic, comment: "Test Message - Lorem Ipsus", user_id: user.id) }

      before :each do
        FactoryBot.create(:message, lesson_feedback_id: lesson_feedback.id, recipient_id: user.id, sender_id: admin.id)

        visit current_path
      end

      it "shows feedback respond modal" do
        visit current_path

        within ".messages" do
          expect(page).to have_content(/Message Board/i)

          expect(page).to have_selector(".message", count: 1)
          find(".message i.fa-comments").click
        end

        expect(page).to have_selector(".feedback-conversation-modal", visible: true)
      end

      it "responds to feedback messages" do
        visit current_path

        within ".messages" do
          find(".message i.fa-comments").click
        end

        within ".feedback-conversation-modal" do
          expect(page).to have_content(/Message Detail/i)

          fill_in "message[content]", with: "Respond to lesson feedback message from admin"
          find("button#admin-respond-reply").click

          expect(page).to have_content(/Respond to lesson feedback message from admin/i)
        end
      end
    end

    context "when user hasn't submitted NPS and has completed at least 50% of the course" do
      before do
        user.update!(current_plan: (RecurlyProxy::PLAN_CODES - RecurlyProxy::TRIAL_PLAN_CODES).sample, nps: nil)
        allow_any_instance_of(User).to receive(:course_completion).and_return(50)

        visit home_path
      end

      it "renders NPS modal" do
        expect(page).to have_content("How likely are you to recommend TTP to a friend based on your experience?")
      end

      it "saves NPS score" do
        click_on("9")
        click_on("Submit your Answer")

        expect(page).to have_content(/Welcome back, /i)
        expect(user.reload.nps).to eq(9)
      end

      it "shows error if NPS score is not selected" do
        click_on("Submit your Answer")

        expect(page).to have_content("How likely are you to recommend TTP to a friend based on your experience?")
        expect(page).to have_content("Please choose one option")
        expect(user.reload.nps).to be_blank
      end
    end

    context "when ondemand is enabled and weekly office hours are present" do
      before :each do
        Flag.flag(user, user, "ondemand_congratulations_modal_acknowledged", true)
        Setting.create(key: "ondemand_enabled", value: true)
        allow_any_instance_of(User).to receive(:ondemand_active?).and_return(true)
        user.update(ondemand_enabled: true, full_ondemand_access: true)

        visit root_path
      end

      it "shows weekly office hour" do
        expect(page).to have_content("OnDemand Office Hours")
        expect(page).to have_selector(".ondemand-office-hours-deatils")
        expect(page).to have_current_path(home_path)
      end

      it "renders weekly office hour modal" do
        find(".ondemand-office-hours-card").click

        expect(page).to have_content("Weekly Office Hours")
        expect(page).to have_selector("#ondemand-office-hour-modal")
        expect(page).to have_current_path(home_path)
      end

      it "renders weekly office hour modal and click on copy button" do
        find(".ondemand-office-hours-card").click

        find("#ondemand-office-hour-modal .copy-button").click

        expect(page).to have_content("Link copied to your clipboard")
        expect(page).to have_current_path(home_path)
      end

      it "renders weekly office hour modal and closed modal" do
        find(".ondemand-office-hours-card").click

        find("#ondemand-office-hour-modal .btn-close").click

        expect(page).not_to have_selector("#ondemand-office-hour-modal")
        expect(page).to have_current_path(home_path)
      end
    end
  end

  context "when user logged in at day 1 since signup with trial plan" do
    let(:user) { FactoryBot.create(:member, created_at: Time.now - 1.days, on_trial: true) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }

    before :each do
      login(user)

      visit root_path
    end

    it "renders image modal at day 1" do
      expect(page).to have_current_path(home_path)
      expect(page).to have_selector("#marketing-modal")
    end
  end

  context "when user logged in at day 2 since signup with trial plan" do
    let(:user) { FactoryBot.create(:member, created_at: Time.now - 2.days, on_trial: true) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }

    before :each do
      login(user)

      visit root_path
    end

    it "renders image modal at day 2" do
      expect(page).to have_current_path(home_path)
      expect(page).to have_selector("#marketing-modal")
    end
  end

  context "when user logged in at day 3 since signup" do
    let(:user) { FactoryBot.create(:member, created_at: Time.now - 3.days) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }

    before :each do
      login(user)

      visit root_path
    end

    it "renders image modal at day 3" do
      expect(page).to have_current_path(home_path)
      expect(page).to have_selector("#marketing-modal")
    end
  end

  context "when user logged in at day 4 since signup" do
    let(:user) { FactoryBot.create(:member, created_at: Time.now - 4.days) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }

    before :each do
      login(user)

      visit root_path
    end

    it "renders image modal at day 4" do
      expect(page).to have_current_path(home_path)
      expect(page).to have_selector("#marketing-modal")
    end
  end

  context "when user logged in at day 5 since signup" do
    let(:user) { FactoryBot.create(:member, created_at: Time.now - 5.days) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }

    before :each do
      login(user)
      visit root_path
    end

    it "renders image modal at day 5" do
      expect(page).to have_current_path(home_path)
      expect(page).to have_selector("#marketing-modal")
    end
  end

  context "when user logged in after day 5 since signup" do
    let(:user) { FactoryBot.create(:member, created_at: Time.now - 6.days) }
    let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }

    before :each do
      login(user)
      visit root_path
    end

    it "does not render marketing modal" do
      expect(page).to have_current_path(home_path)
      expect(page).not_to have_selector("#marketing-modal")
    end
  end
end
