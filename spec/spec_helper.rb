# require 'codeclimate-test-reporter'
# CodeClimate::TestReporter.start
require "simplecov"
SimpleCov.start "rails" do
  refuse_coverage_drop
  add_filter "lib"
  add_filter "/app/services/recurly_proxy.rb"
  coverage_dir "./tmp/simplecov"
end

SimpleCov.at_exit do
  SimpleCov.result.format!
  warn "Current coverage: #{SimpleCov.result.covered_percent}%"
end

require "codecov"
SimpleCov.formatter = SimpleCov::Formatter::Codecov if ENV["CODECOV_TOKEN"]

ENV["RAILS_ENV"] ||= "test"
require File.expand_path("../config/environment", __dir__)

require "rubygems"
require "content_fixer"
require "rspec/rails"
require "capybara/rspec"
require "capybara/rails"
require "capybara/email/rspec"
require "factory_bot_rails"
require "rspec/mocks"
require "rspec/retry"
require "cancan/matchers"

FactoryBot.factories.clear
FactoryBot.reload
FactoryBot::SyntaxRunner.class_eval do
  include RSpec::Mocks::ExampleMethods
end

# This aligns to Rails future behavior (Rails 8+)
ActiveSupport.to_time_preserves_timezone = true

# Requires supporting ruby files with custom matchers and macros, etc,
# in spec/support/ and its subdirectories.
Dir[Rails.root.join("spec/support/**/*.rb")].sort.each { |file| require file }

# Checks for pending migrations before tests are run.
# If you are not using ActiveRecord, you can remove this line.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  abort e.to_s.strip
end

Capybara.register_driver :selenium_chrome do |app|
  client = Selenium::WebDriver::Remote::Http::Default.new
  client.read_timeout = 180
  client.open_timeout = 180

  options = Selenium::WebDriver::Chrome::Options.new
  options.add_argument("--headless")
  options.add_argument("--disable-gpu")
  options.add_argument("--window-size=1440,800")
  options.add_argument("--no-sandbox")
  options.add_argument("--disable-dev-shm-usage")
  options.add_preference(:download, prompt_for_download: false, default_directory: DownloadHelpers::PATH.to_s)

  Capybara::Selenium::Driver.new(app, browser: :chrome, http_client: client, options: options)
end

Capybara.javascript_driver = :selenium_chrome

Capybara.configure do |config|
  config.default_max_wait_time = 30 # seconds
  config.default_driver = :selenium_chrome
end

# The following is a monkey patch to let the capybara server and the test
# suite share the database connection when using transactional fixtures
Capybara.server = :webrick
Capybara.server_port = 3001
Capybara.app_host = "http://localhost:3001"

# Change rails logger level to reduce IO during tests

Rails.logger.level = 4
RSpec.configure do |config|
  config.infer_base_class_for_anonymous_controllers = false
  config.order = :random
  config.color = true

  config.verbose_retry = true
  config.default_retry_count = 2

  config.include Rails.application.routes.url_helpers
  config.include FactoryBot::Syntax::Methods
  config.include Devise::Test::ControllerHelpers, type: :controller
  config.include ActiveSupport::Testing::TimeHelpers
  config.include CkeditorHelpers, type: :feature
  config.include RequestHelpers
  config.include RecurlyHelpers
  config.include MailerHelpers
  config.include Capybara::DSL
  config.include BookmarkHelpers
  config.include CalendarHelper
  config.include OnlineClassesHelpers
  config.include HomepageHelpers
  config.include WaitForAjax, type: :feature

  config.before(:suite) do
    DatabaseCleaner.clean_with :truncation
  end

  ###### Uncomment below for debugging and get logger to output to console ######
  # config.before(:each) do
  #   Rails.logger = Logger.new($stdout)
  #   Rails.logger.level = :debug
  # end

  config.before(:each) do
    DatabaseCleaner.strategy = :transaction
    WebMock.allow_net_connect!
  end

  config.before(:each, js: true) do
    DatabaseCleaner.strategy = :truncation
  end

  config.before(:each) do
    DatabaseCleaner.start
    Session.delete_all if defined?(Session)
  end

  config.append_after(:each) do
    DatabaseCleaner.clean
  end

  config.append_after(:each, js: true) do
    DatabaseCleaner.clean_with :deletion
  end

  config.before(:each) do
    DownloadHelpers.clear_downloads
  end
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

######### Javascript Tests Debugging Only #########

RSpec.configure do |config|
  js_errors = []

  config.after(:each, type: :feature, js: true) do |example|
    errors = page.driver.browser.manage.logs.get(:browser)
    if errors.present?
      js_errors << {
        test: example.full_description,
        errors: errors.map(&:message)
      }
    end
  end

  config.after(:suite) do
    if js_errors.any?
      File.open("js_errors_summary.log", "w") do |f|
        js_errors.each do |error|
          f.puts "Errors in '#{error[:test]}':"
          error[:errors].each { |e| f.puts "  #{e}" }
          f.puts
        end
      end
      puts "JavaScript errors encountered. See js_errors_summary.log for details."
    end
  end
end
