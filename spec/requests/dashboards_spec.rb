require "spec_helper"

describe "GET #prep_toolbox", type: :request do
  let!(:user) { FactoryBot.create(:user) }

  before :each do
    login(user)
  end

  it "renders the prep toolbox template" do
    get dashboards_prep_toolbox_path, params: {}
    expect(response).to render_template :prep_toolbox
  end
end

describe "GET #online_classes", type: :request do
  let(:user) { create(:user) }

  before do
    login(user)
  end

  it "renders the online_classes template" do
    get dashboard_online_classes_path, params: {}

    expect(response).to render_template :online_classes
  end
end

describe "GET #admissions", type: :request do
  let(:user) { create(:user) }

  before do
    login(user)
  end

  it "renders the not_found template" do
    get dashboards_admissions_path, params: {}

    expect(response).to render_template :admissions
  end

  context "when the exam name is SAT" do
    before do
      stub_const("EXAM_NAME", "sat")
    end

    it "renders the not_found template" do
      get dashboards_admissions_path, params: {}

      expect(response).to render_template "content_pages/not_found"
    end
  end
end

describe "GET #ondemand", type: :request do
  let(:user) { create(:user) }

  before do
    login(user)
  end

  it "renders the ondemand template" do
    get dashboards_ondemand_path, params: {}

    expect(response).to render_template :ondemand
  end
end

describe "GET #next_liveteach_class", type: :request do
  let(:user) { create(:user) }
  let!(:online_class) { create(:online_class, :skip_validate) }
  let!(:sessions) { create_list(:online_class_session, 3, weeks_later: 4, online_class: online_class, date: 1.days.from_now.to_date) }

  before do
    login(user)
    allow(user).to receive(:access_online_class?).and_return(true)
  end

  it "renders the next liveteach class template" do
    get dashboard_next_liveteach_class_path, params: { session_id: sessions.first.id }

    expect(response).to render_template :next_liveteach_class
  end
end
