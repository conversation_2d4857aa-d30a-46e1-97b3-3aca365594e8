require "spec_helper"

RSpec.describe "Evaluations", type: :request do
  let!(:user) { FactoryBot.create(:user, :with_billing_info) }
  let!(:online_class) { FactoryBot.create(:online_class, :skip_validate) }
  let!(:online_class_session) { FactoryBot.create(:online_class_session, online_class: online_class) }
  let!(:homework_group) { FactoryBot.create(:homework_group, online_class_session: online_class_session) }
  before do
    login(user)
  end

  describe "POST /evaluations/create_homework_custom_test" do
    let!(:problem_1) { FactoryBot.create(:problem) }
    let!(:problem_2) { FactoryBot.create(:problem) }
    let!(:problem_3) { FactoryBot.create(:problem) }
    let!(:homework_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:custom_test], data: [problem_1.id, problem_2.id, problem_3.id], homework_group: homework_group) }

    let(:valid_params) do
      {
        session_homework_id: homework_task.id,
        time_per_question: 120
      }
    end

    context "when homework task exists and has custom tests" do
      it "creates a custom test evaluation successfully and starts it" do
        evaluation_attempt_double = double("EvaluationAttempt", id: 123)
        problem_attempt_double = double("ProblemAttempt", id: 456)
        evaluation_starter_double = double(
          evaluation_attempt: evaluation_attempt_double,
          problem_attempt: problem_attempt_double
        )

        expect(EvaluationStarter).to receive(:call).with(
          user,
          instance_of(UserEvaluation),
          "120",
          false,
          false
        ).and_return(evaluation_starter_double)

        expect do
          post create_homework_custom_test_path, params: valid_params
        end.to change(UserEvaluation, :count).by(1)

        evaluation = UserEvaluation.last
        expect(evaluation.user).to eq(user)
        expect(evaluation.homework_task_id).to eq(homework_task.id)
        expect(evaluation.problems.count).to eq(3)
        expect(evaluation.time_per_question).to eq(120)
        expect(evaluation.problems.pluck(:id)).to eq([problem_1.id, problem_2.id, problem_3.id])
        expect(response).to redirect_to(problem_solving_ask_path(evaluation_attempt_double, problem_attempt_double))
      end

      it "uses default time per question when not provided" do
        post create_homework_custom_test_path, params: { session_homework_id: homework_task.id }

        evaluation = UserEvaluation.last
        expect(evaluation.time_per_question).to eq(Evaluation.exam_time_per_question)
      end
    end

    context "when homework task does not exist" do
      let(:invalid_params) { { session_homework_id: 999_999 } }

      it "does not create an evaluation" do
        expect do
          post create_homework_custom_test_path, params: invalid_params
        end.not_to change(UserEvaluation, :count)
      end

      it "redirects to class session dashboard with default alert" do
        post create_homework_custom_test_path, params: invalid_params

        expect(response).to redirect_to(dashboard_next_liveteach_class_path(session_id: nil))
        expect(flash[:alert]).to eq("Homework tasks not found.")
      end
    end

    context "when homework task exists but session has no custom tests" do
      let!(:session_without_custom_tests) { FactoryBot.create(:online_class_session, online_class: online_class) }
      let!(:homework_group) { FactoryBot.create(:homework_group, online_class_session: session_without_custom_tests) }
      let!(:prereading_only_homework) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:prereading], homework_group: homework_group) }

      it "does not create an evaluation and redirects with error" do
        expect do
          post create_homework_custom_test_path, params: { session_homework_id: prereading_only_homework.id }
        end.not_to change(UserEvaluation, :count)

        expect(response).to redirect_to(dashboard_next_liveteach_class_path(session_id: session_without_custom_tests.id))
        expect(flash[:alert]).to eq("Homework tasks not found.")
      end
    end

    context "when evaluation fails to save" do
      before do
        allow_any_instance_of(UserEvaluation).to receive(:save).and_return(false)
      end

      it "redirects to class session dashboard with error message" do
        expect do
          post create_homework_custom_test_path, params: valid_params
        end.not_to change(UserEvaluation, :count)

        expect(response).to redirect_to(dashboard_next_liveteach_class_path(session_id: online_class_session.id))
        expect(flash[:alert]).to eq("Test could not be created. Please contact support if the problem persists.")
      end
    end

    context "when user is not authenticated" do
      before { logout }

      it "redirects to login" do
        post create_homework_custom_test_path, params: valid_params

        expect(response).to redirect_to(new_user_session_path)
      end
    end
  end
end
