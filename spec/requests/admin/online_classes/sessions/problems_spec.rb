require "spec_helper"

describe "GET /new_admin/homework/problems/homework_task_type", type: :request do
  let!(:user) { create(:admin) }
  let!(:chapter) { FactoryBot.create(:chapter) }
  let!(:problem) { FactoryBot.create(:problem, chapter_id: chapter.id) }
  let!(:online_class) { FactoryBot.create(:online_class, online_class_sessions: online_class_session) }
  let!(:online_class_session) { FactoryBot.build_list(:online_class_session, 1, homework_task_data: [problem.id]) }

  before :each do
    login(user)
  end

  it "returns http success" do
    get homework_task_type_admin_homework_problems_path(online_class_session_id: online_class_session.first.id, chapter_id: chapter.id, format: :js), xhr: true

    expect(response).to be_truthy
  end
end
