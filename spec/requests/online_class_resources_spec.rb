require "spec_helper"

RSpec.describe "OnlineClassResources", type: :request do
  let!(:user) { create(:member, :with_billing_info, set_current_plan: true) }
  let!(:instructors) { create_list(:instructor, 5) }
  let!(:online_class) { FactoryBot.create(:online_class, :with_sessions, :skip_validate, instructor: instructors.first) }
  let!(:session) do
    session = online_class.sessions.first
    session.update!(recording_url: "http://example.com/recording.mp4", show_in_library: true)
    session
  end

  before do
    login user
  end

  describe "GET #library" do
    context "when user has access to online class" do
      before do
        allow(user).to receive(:access_online_class?).and_return(true)
      end

      it "renders the cohort library template" do
        get online_class_resources_library_path
        expect(response).to render_template :library
      end
    end

    context "when user does not have access to online class" do
      before do
        allow(user).to receive(:access_online_class?).and_return(false)
      end

      it "renders the not_found template" do
        get online_class_resources_library_path
        expect(response).to render_template("content_pages/not_found")
      end
    end
  end

  describe "GET #cohort_video_repository" do
    context "when instructor exists" do
      before do
        allow(user).to receive(:access_online_class?).and_return(true)
      end
      it "renders the cohort video repository template" do
        get cohort_video_repository_path(instructor_id: instructors.first.id, back_to: home_path)
        expect(response).to render_template :cohort_video_repository
      end
    end
  end
end
