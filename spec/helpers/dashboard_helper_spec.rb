require "spec_helper"

RSpec.describe DashboardsHelper, type: :helper do
  let!(:user) { FactoryBot.create(:user, :with_billing_info) }
  let!(:online_class) { FactoryBot.create(:online_class, :skip_validate) }
  let!(:session) { FactoryBot.create(:online_class_session, online_class: online_class, weeks_later: 2, date: 1.day.from_now.to_date) }

  describe "#zoom_link_active" do
    context "when zoom link is present and date is today or in the past" do
      it "returns true" do
        allow(session).to receive(:zoom_link).and_return("https://zoom.us/my_own")
        allow(session).to receive(:date).and_return(Date.current)
        expect(helper.zoom_link_active(session)).to be(true)
      end
    end

    context "when zoom link is present and date is in future" do
      it "returns false" do
        allow(session).to receive(:date).and_return(Date.current + 2)
        expect(helper.zoom_link_active(session)).to be(false)
      end
    end

    context "when session has its own zoom link" do
      it "returns true" do
        allow(session).to receive(:zoom_link).and_return("https://zoom.us/my_own")
        allow(session).to receive(:date).and_return(Date.current)
        expect(helper.zoom_link_active(session)).to be(true)
      end
    end
  end

  describe "#liveteach_action_btn_attributes" do
    context "when class is completed and recording_url is present" do
      let!(:session) { FactoryBot.create(:online_class_session, online_class: online_class, recording_url: "123456", show_in_library: true) }

      it "returns attributes for viewing recording" do
        result = helper.liveteach_action_btn_attributes(session, true)
        expect(result[:icon]).to include("clapperboard_play_icon.svg")
        expect(result[:disabled]).to be(false)
        expect(result[:title]).to eq("View Class Recording")
      end
    end

    context "when class is completed but no recording_url" do
      it "returns disabled recording button" do
        result = helper.liveteach_action_btn_attributes(session, true)
        expect(result[:icon]).to include("clapperboard_play_disabled.svg")
        expect(result[:disabled]).to be(true)
        expect(result[:title]).to eq("No recording available yet")
      end
    end

    context "when class is not completed and zoom link is available" do
      let!(:session) { FactoryBot.create(:online_class_session, online_class: online_class, zoom_link: "https://zoom.us/my_own", weeks_later: 2, date: 1.day.from_now.to_date) }

      it "returns zoom join button" do
        result = helper.liveteach_action_btn_attributes(session, false)
        expect(result[:disabled]).to be(false)
        expect(result[:title]).to eq("Open Zoom Link")
      end
    end

    context "when zoom link is not available" do
      it "returns disabled zoom button" do
        result = helper.liveteach_action_btn_attributes(session, false)
        expect(result[:icon]).to include("zoom_meet_icon_disabled.webp")
        expect(result[:disabled]).to be(true)
        expect(result[:title]).to eq("Zoom link coming soon")
      end
    end
  end

  describe "#see_files_btn_attributes" do
    it "returns correct data attributes" do
      result = helper.see_files_btn_attributes(session)
      expect(result[:data][:bs_target]).to eq("#online-class-session-#{session.id}-files")
      expect(result[:type]).to eq("button")
      expect(result[:data][:bs_toggle]).to eq("tooltip")
    end
  end

  describe "#sessions_homework_partial_name" do
    let!(:homework_group) { FactoryBot.create(:homework_group, online_class_session: session) }

    context "when homework is custom_test" do
      let(:homework_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:custom_test], homework_group: homework_group) }

      it "returns custom_test_homework partial name" do
        expect(helper.sessions_homework_partial_name(homework_task)).to eq("dashboards/custom_test_homework")
      end
    end

    context "when homework is prereading" do
      let(:homework_task) { FactoryBot.create(:homework_task, task_type: HomeworkTask::TASK_TYPES[:prereading], homework_group: homework_group) }

      it "returns pre_reading_homework partial name" do
        expect(helper.sessions_homework_partial_name(homework_task)).to eq("dashboards/pre_reading_homework")
      end
    end
  end

  describe "#fetch_topic_from_homework_data" do
    let!(:topic) { FactoryBot.create(:topic) }

    it "returns the topic when it exists" do
      expect(helper.fetch_topic_from_homework_data(topic.id)).to eq(topic)
    end

    it "returns nil when topic does not exist" do
      expect(helper.fetch_topic_from_homework_data(999_999)).to be_nil
    end
  end

  describe "#homework_groups_completed?" do
    let!(:group1) { FactoryBot.create(:homework_group, online_class_session: session) }
    let!(:group2) { FactoryBot.create(:homework_group, online_class_session: session) }
    let!(:task1) { FactoryBot.create(:homework_task, homework_group: group1) }
    let!(:task2) { FactoryBot.create(:homework_task, homework_group: group2) }

    before { allow(helper).to receive(:homework_group_completed?).and_call_original }

    it "returns false when at least one group is not completed" do
      allow(task1).to receive(:generate_completion_for).with(user).and_return({ percentage: 100 })
      allow(task2).to receive(:generate_completion_for).with(user).and_return({ percentage: 50 })
      expect(helper.homework_groups_completed?(session, user)).to be false
    end
  end

  describe "#first_incomplete_group_for_session" do
    let!(:group1) { FactoryBot.create(:homework_group, online_class_session: session) }
    let!(:group2) { FactoryBot.create(:homework_group, online_class_session: session) }
    let!(:task1) { FactoryBot.create(:homework_task, homework_group: group1) }
    let!(:task2) { FactoryBot.create(:homework_task, homework_group: group2) }

    it "returns the first incomplete group" do
      allow(task1).to receive(:generate_completion_for).with(user).and_return({ percentage: 100 })
      allow(task2).to receive(:generate_completion_for).with(user).and_return({ percentage: 50 })
      expect(helper.first_incomplete_group_for_session(session, user)).to eq(group1)
    end
  end

  describe "#homework_task_completion" do
    let!(:group) { FactoryBot.create(:homework_group, online_class_session: session) }
    let!(:task) { FactoryBot.create(:homework_task, homework_group: group) }

    it "returns completed true when task is 100%" do
      allow(task).to receive(:generate_completion_for).with(user).and_return({ percentage: 100 })
      result = helper.homework_task_completion(task, user)
      expect(result[:completed]).to be true
      expect(result[:completion][:percentage]).to eq 100
    end

    it "returns completed false when task is < 100%" do
      allow(task).to receive(:generate_completion_for).with(user).and_return({ percentage: 50 })
      result = helper.homework_task_completion(task, user)
      expect(result[:completed]).to be false
      expect(result[:completion][:percentage]).to eq 50
    end
  end
end
