FactoryBot.define do
  factory :chapter do
    type  { "Chapter" }
    section { "quant" }
    subsection { nil }
    sequence(:name) { |n| "chapter #{n}" }
    sequence(:number, &:to_s)

    trait :with_problems do
      before(:create, :build) do |chapter, _evaluator|
        chapter.problems = FactoryBot.build_list(:problem, 5, chapter_id: nil)
      end
    end

    trait :awa do
      section { "awa" }
      subsection { nil }
    end

    trait :ir do
      section { "ir" }
      subsection { nil }
    end

    trait :di do
      section { "ir" }
      subsection { nil }
    end

    trait :with_topics do
      after(:create, :build) do |chapter|
        chapter.topics = FactoryBot.create_list(:topic, 5, chapter_id: chapter.id)
      end
    end

    after(:create) do |chapter|
      Track.all.each do |track|
        FactoryBot.create(:lesson_track, track: track, lesson_id: chapter.id, dynamic_lesson_number: chapter.number)
      end

      Section.find_or_create_by(name: chapter.section)
      FactoryBot.create(:video, videable: chapter)
    end
  end
end
