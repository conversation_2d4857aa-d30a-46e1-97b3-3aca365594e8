FactoryBot.define do
  factory :topic do
    association :parent, factory: :chapter
    chapter { parent }
    type { "Topic" }
    section { nil }
    name { Faker::Lorem.sentence }
    content { Faker::Lorem.paragraph(sentence_count: 3) }

    after(:create) do |topic|
      Track.all.each do |track|
        FactoryBot.create(:lesson_track, track: track, lesson_id: topic.id, dynamic_lesson_number: topic.number)
      end

      # FactoryBot.create(:video, videable: topic)
    end

    trait :archived do
      archived_at { Time.zone.now }
    end

    trait :with_empty_examples do
      after(:create) do |topic|
        Track.all.each do |track|
          FactoryBot.create(:lesson_track, track: track, lesson_id: topic.id, dynamic_lesson_number: topic.number)
        end
      end
    end

    factory :topic_with_empty_question_type_examples do
      after(:create) do |topic|
        FactoryBot.create(:example, question_type: "empty_question", lesson: topic)

        topic.reload.update(content: "#{Faker::Lorem.paragraph(sentence_count: 3)} [[example]]")
      end
    end

    factory :topic_with_vocab_list_in_content do
      after(:create) do |topic|
        topic.chapter.update(section: "verbal")
        FactoryBot.create_list(:word_definition, 10, chapter: topic.chapter)

        topic.reload.update(content: "#{Faker::Lorem.paragraph(sentence_count: 3)} [[VOCAB_LIST]]")
      end
    end

    factory :topic_with_vocab_word_in_example_solution do
      after(:create) do |topic|
        FactoryBot.create(:word_definition, word: "abstract", chapter: topic.chapter)

        topic.reload.update(content: "#{Faker::Lorem.paragraph(sentence_count: 3)} [[example]]")
      end
    end

    factory :topic_with_example do
      after(:create) do |topic|
        FactoryBot.create(:example, lesson: topic)

        topic.reload.update(content: "#{Faker::Lorem.paragraph(sentence_count: 3)} [[example]]")
      end
    end
  end
end
